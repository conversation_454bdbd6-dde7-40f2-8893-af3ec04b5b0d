package errors

import "net/http"

var (
	ErrBadRequest    = New(http.StatusBadRequest, "E1001", "Bad request")
	ErrNotFound      = New(http.StatusNotFound, "E4004", "Resource not found")
	ErrInternal      = New(http.StatusInternalServerError, "E5000", "Internal server error")
	ErrUnauthorized  = New(http.StatusUnauthorized, "E4001", "Unauthorized")
	ErrForbidden     = New(http.StatusForbidden, "E4003", "Forbidden")
	ErrConflict      = New(http.StatusConflict, "E4009", "Conflict")
	ErrUnprocessable = New(http.StatusUnprocessableEntity, "E422", "Unprocessable entity")
)

// User service start prefix 1 only
var (
	ErrPinInvalid             = New(http.StatusBadRequest, "E1001", "Pin Invalid")
	ErrPasswordInvalid        = New(http.StatusBadRequest, "E1002", "Password Invalid")
	ErrPasswordDuplicate      = New(http.StatusBadRequest, "E1003", "Password Duplicate")
	ErrInvalidSearchUser      = New(http.StatusBadRequest, "E1004", "Invalid search user")
	ErrCreateNewUser          = New(http.StatusBadRequest, "E1005", "Create new user failed")
	ErrAssignRolesAfterCreate = New(http.StatusBadRequest, "E1006", "Assign roles after create failed")
	ErrGenerateFromPassword   = New(http.StatusBadRequest, "E1007", "Generate from password failed")
)

// Auth service start prefix 2 only
// var (
// 	ErrPinInvalid        = New(http.StatusBadRequest, "E2002", "Pin Invalid")
// 	ErrPasswordInvalid   = New(http.StatusBadRequest, "E2003", "Password Invalid")
// 	ErrPasswordDuplicate = New(http.StatusBadRequest, "E2004", "Password Duplicate")
// )

// Datahub service start prefix 3 only
var (
	ErrAuctionWebImageSystemBadGateway = New(http.StatusBadGateway, "E3001", "Cannot connect to auction web image system")
)
