package util

import (
	"crypto/sha256"
	"encoding/base64"
	"regexp"
)

func HashWithSalt(data, salt string) string {
	hash := sha256.Sum256([]byte(data + salt))
	return base64.StdEncoding.EncodeToString(hash[:])
}

func IsValidPassword(password string) bool {
	isLongEnough := len(password) >= 8
	hasNumber, _ := regexp.MatchString(`[0-9]`, password)
	hasUpper, _ := regexp.MatchString(`[A-Z]`, password)
	hasLower, _ := regexp.MatchString(`[a-z]`, password)
	hasSpecial, _ := regexp.MatchString(`[!@#\$%\^&\*\(\)_\+\|\~\-=\`+"`"+`\{\}\[\]:\";'<>?,./]+`, password)

	return isLongEnough && hasNumber && hasUpper && hasLower && hasSpecial
}

func IsValidPin(pin string) bool {
	isValid, _ := regexp.MatchString(`^[0-9]{6}$`, pin)
	return isValid
}

func HashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return base64.StdEncoding.EncodeToString(hash[:])
}
