package util

import (
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
)

// MapToPtr Auto mapper source to dest
func MapToPtr[T any](source interface{}) *T {
	var result = new(T)
	if source != nil {
		MapValueOf(reflect.ValueOf(source), reflect.ValueOf(&result))
	}
	return result
}

func MapToWithCreatedByAndUpdatedBy[T any](source interface{}) T {
	result := MapToPtr[T](source)
	if source == nil {
		return *result
	}

	src := reflect.Indirect(reflect.ValueOf(source))
	dst := reflect.Indirect(reflect.ValueOf(result))

	createdUser := src.FieldByName("CreatedUser")
	updatedUser := src.FieldByName("UpdatedUser")

	if !createdUser.IsValid() || !updatedUser.IsValid() {
		return *result
	}

	//NOTE case preload: CreatedUser/UpdatedUser is *User struct
	if createdUser.Kind() == reflect.Ptr && !createdUser.IsNil() && createdUser.Elem().Kind() == reflect.Struct {
		if full := getFullName(createdUser); full != "" {
			setStringPtr(dst, "CreatedBy", full)
		}
		if full := getFullName(updatedUser); full != "" {
			setStringPtr(dst, "UpdatedBy", full)
		}
		return *result
	}

	//NOTE case joined string: CreatedUser/UpdatedUser is string
	if createdUser.Kind() == reflect.Ptr && createdUser.Elem().Kind() == reflect.String {
		dstField := dst.FieldByName("CreatedBy")
		if dstField.IsValid() && dstField.Kind() == reflect.Ptr {
			dstField.Set(createdUser)
		}
		dstField = dst.FieldByName("UpdatedBy")
		if dstField.IsValid() && dstField.Kind() == reflect.Ptr {
			dstField.Set(updatedUser)
		}
	}

	return *result
}

func getFullName(user reflect.Value) string {
	if !user.IsValid() || user.IsNil() {
		return ""
	}
	first := user.Elem().FieldByName("FirstName")
	last := user.Elem().FieldByName("LastName")
	if !first.IsValid() || !last.IsValid() {
		return ""
	}
	return strings.TrimSpace(first.String() + " " + last.String())
}

func setStringPtr(v reflect.Value, field string, value string) {
	f := v.FieldByName(field)
	if f.IsValid() && f.Kind() == reflect.Ptr {
		f.Set(reflect.ValueOf(&value))
	}
}

// MapValue Auto mapper source to dest
func MapValue[T any, S any](source *T, dest *S) {
	MapValueOf(reflect.ValueOf(source), reflect.ValueOf(dest))
}

// MapValueOf Auto mapper source to dest
func MapValueOf(source reflect.Value, dest reflect.Value) {
	if source.Kind() == reflect.Ptr {
		source = source.Elem()
	}
	if dest.Kind() == reflect.Ptr {
		dest = dest.Elem()
	}
	if dest.Kind() == reflect.Ptr {
		dest = dest.Elem()
	}
	if dest.Kind() != reflect.Ptr {
		for i := 0; i < dest.NumField(); i++ {
			fieldType := dest.Type().Field(i)
			field := dest.Field(i)
			if fieldValue := source.FieldByName(fieldType.Name); true {
				val := getValue(fieldValue)
				fieldSet := getField(field, fieldValue)
				if val.IsValid() {
					if fieldSet.Kind() == reflect.Struct {
						if fieldSet.Type().Name() == val.Type().Name() && fieldSet.CanSet() {
							fieldSet.Set(reflect.ValueOf(val.Interface()))
						}
					} else if fieldSet.CanSet() {
						fieldSet.Set(reflect.ValueOf(val.Interface()))
					} else if field.Kind() == reflect.Ptr && fieldSet.Kind() == reflect.Pointer {
						//else if field.Kind() == reflect.Ptr && field.CanSet() && field.IsNil() && field.Elem().Kind() != reflect.Invalid {
						field.Set(reflect.New(field.Type().Elem()))
						//field.Elem().Set(reflect.ValueOf(val.Interface()))
						field.Set(reflect.ValueOf(val.Interface()))
					}
				}
			}
			if fieldType.Name == "BaseDto" { //Case BaseEntity map to BaseDto
				base := getValue(source.FieldByName("BaseEntity"))
				fieldSet := getValue(field)
				if base.Kind() == reflect.Struct && fieldSet.Kind() == reflect.Struct {
					for j := 0; j < fieldSet.NumField(); j++ {
						subField := fieldSet.Field(j)
						subType := fieldSet.Type().Field(j)
						baseField := base.FieldByName(subType.Name)

						if baseField.IsValid() && subField.CanSet() && subField.Type() == baseField.Type() {
							subField.Set(baseField)
						}
					}
				}
			}
		}
	}
}

func getValue(fieldValue reflect.Value) reflect.Value {
	val := fieldValue
	if fieldValue.Kind() == reflect.Ptr {
		val = fieldValue.Elem()
	}
	return val
}

func getField(field reflect.Value, fieldValue reflect.Value) reflect.Value {
	fieldSet := field
	if field.CanSet() && field.Kind() == reflect.Ptr {
		if fieldValue.IsValid() && fieldValue.Kind() == reflect.Ptr && !fieldValue.IsNil() {
			field.Set(reflect.New(field.Type().Elem()))
		}
		fieldSet = field.Elem()
	}
	return fieldSet
}

func Ptr[T any](v T) *T {
	result := new(T)
	*result = v
	return result
}

func Val[T any](p *T) T {
	if p != nil {
		return *p
	}
	var zero T
	return zero
}

func CompareStructDeep(srcStruct, targetStruct interface{}, prefix string) map[string]interface{} {
	changedFields := make(map[string]interface{})

	srcVal := reflect.ValueOf(srcStruct)
	targetVal := reflect.ValueOf(targetStruct)

	if srcVal.Kind() == reflect.Ptr {
		srcVal = srcVal.Elem()
	}
	if targetVal.Kind() == reflect.Ptr {
		targetVal = targetVal.Elem()
	}

	srcType := srcVal.Type()

	for i := 0; i < srcVal.NumField(); i++ {
		fieldInfo := srcType.Field(i)
		tag := fieldInfo.Tag

		//skip ignore tag
		if tag.Get("ignore") == "true" {
			continue
		}

		srcField := srcVal.Field(i)
		targetField := targetVal.Field(i)

		if !srcField.CanInterface() || !targetField.CanInterface() {
			continue
		}

		fieldName := fieldInfo.Name
		if prefix != "" {
			fieldName = prefix + "." + fieldName
		}

		//handle embedded pointer struct
		if fieldInfo.Anonymous && srcField.Kind() == reflect.Ptr && !srcField.IsNil() {
			nestedChanges := CompareStructDeep(srcField.Interface(), targetField.Interface(), fieldName)
			for k, v := range nestedChanges {
				changedFields[k] = v
			}
			continue
		}

		switch srcField.Kind() {
		case reflect.Struct:
			if srcField.Type().String() == "time.Time" {
				srcTime := srcField.Interface().(time.Time)
				targetTime := targetField.Interface().(time.Time)
				if !srcTime.Equal(targetTime) {
					changedFields[fieldName] = targetTime
				}
			} else {
				nestedChanges := CompareStructDeep(srcField.Interface(), targetField.Interface(), fieldName)
				for k, v := range nestedChanges {
					changedFields[k] = v
				}
			}
		case reflect.Slice, reflect.Array:
			if !reflect.DeepEqual(srcField.Interface(), targetField.Interface()) {
				changedFields[fieldName] = targetField.Interface()
			}
		default:
			if !reflect.DeepEqual(srcField.Interface(), targetField.Interface()) {
				changedFields[fieldName] = targetField.Interface()
			}
		}
	}

	return changedFields
}

func GetActionByFromCtx(c *fiber.Ctx) *int {
	userId, ok := c.Locals("user_id").(*int)
	if !ok || userId == nil {
		return nil
	}
	return userId
}

func GetActionByFromHeader(c *fiber.Ctx) *int {

	userIDStr := c.Get("X-Auth-User-Id")
	if userIDStr == "" {
		userId, ok := c.Locals("user_id").(*int) //FIXME - to delete
		if !ok || userId == nil {
			return nil
		}
		return userId
	}

	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		return nil
	}
	return &userID

}
