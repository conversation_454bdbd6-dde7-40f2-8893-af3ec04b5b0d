package util

import (
	"math"

	"backend-common-lib/model"
)

func CalculatePageNumberFromOffset(offset, limit int) int {
	if limit <= 0 {
		return 1
	}
	return int(math.Floor(float64(offset)/float64(limit))) + 1
}

func MapPaginationResult[T any](data []T, total, offset, limit int) *model.PagingModel[T] {
	if data == nil {
		result := model.NewPagingEmptyModel[T]()
		return &result
	}

	result := &model.PagingModel[T]{
		Data:   data,
		Total:  total,
		Limit:  limit,
		Offset: offset,
		Page:   CalculatePageNumberFromOffset(offset, limit),
	}
	return result
}
