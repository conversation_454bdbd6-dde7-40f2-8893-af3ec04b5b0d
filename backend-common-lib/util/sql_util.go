package util

import (
	"fmt"
	"reflect"
	"strings"
	"time"

	"gorm.io/gorm"
)

func WithTx(db *gorm.DB, fn func(tx *gorm.DB) error) error {
	return db.Transaction(fn)
}

func BuildILikeCondition(field string, value string) (query string, args []interface{}) {

	if value == "-" {
		return fmt.Sprintf("%s IS NULL OR %s = '' OR %s ILIKE '%%-%%'", field, field, field), nil
	}

	return fmt.Sprintf("%s ILIKE ?", field), []interface{}{fmt.Sprintf("%%%s%%", value)}
}

func JoinUsers(tableName string) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.
			Preload("CreatedUser").
			Preload("UpdatedUser")
	}
}

func ApplyEmployeeSort(db *gorm.DB, sortBy string, sortOrder string, tableName string) *gorm.DB {
	sortOrder = strings.ToUpper(sortOrder)
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "ASC"
	}

	switch sortBy {
	case "created_by":
		return db.Joins(fmt.Sprintf("LEFT JOIN employee AS created_user ON created_user.id = %s.created_by", tableName)).
			Order(fmt.Sprintf("created_user.first_name || created_user.last_name %s", sortOrder))
	case "updated_by":
		return db.Joins(fmt.Sprintf("LEFT JOIN employee AS updated_user ON updated_user.id  = %s.updated_by", tableName)).
			Order(fmt.Sprintf("updated_user.first_name || updated_user.last_name %s", sortOrder))
	default:
		return db.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))
	}
}

func ApplySortFromMapField(db *gorm.DB, sortBy string, sortOrder string, tableName string, mapField map[string][]string, joinUser bool) *gorm.DB {
	sortOrder = strings.ToUpper(sortOrder)
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "ASC"
	}

	if joinUser {
		switch sortBy {
		case "created_by":
			return db.Joins(fmt.Sprintf("LEFT JOIN employee AS created_user ON created_user.id = %s.created_by", tableName)).
				Order(fmt.Sprintf("created_user.first_name || created_user.last_name %s", sortOrder))
		case "updated_by":
			return db.Joins(fmt.Sprintf("LEFT JOIN employee AS updated_user ON updated_user.id  = %s.updated_by", tableName)).
				Order(fmt.Sprintf("updated_user.first_name || updated_user.last_name %s", sortOrder))
		}
	}

	if sortFields, ok := mapField[sortBy]; ok {
		for _, field := range sortFields {
			db = db.Order(fmt.Sprintf("%s %s", field, sortOrder))
		}
		return db
	}
	return db.Order(fmt.Sprintf("%s.%s %s", tableName, sortBy, sortOrder))
}

func ApplyFiltersFromStruct(db *gorm.DB, input interface{}, fieldMapOpt ...map[string]string) *gorm.DB {
	val := reflect.ValueOf(input)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}
	typ := val.Type()

	var fieldMap map[string]string
	if len(fieldMapOpt) > 0 {
		fieldMap = fieldMapOpt[0]
	} else {
		fieldMap = make(map[string]string)
	}

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)

		// Skip embedded and struct-type fields (no deep struct mapping)
		if field.Anonymous || field.Type.Kind() == reflect.Struct {
			continue
		}

		value := val.Field(i)
		zero := reflect.Zero(value.Type()).Interface()
		if !value.IsValid() || (value.Kind() == reflect.Ptr && value.IsNil()) || value.Interface() == zero {
			continue
		}

		// use alias column if exists
		column, ok := fieldMap[field.Name]
		if !ok {
			column = CamelToSnake(field.Name)
		}

		valInterface := value.Interface()
		if value.Kind() == reflect.Ptr {
			valInterface = value.Elem().Interface()
		}

		switch val := valInterface.(type) {
		case string:
			where, args := BuildILikeCondition(column, val)
			db = db.Where(where, args...)
		case bool, int, int64, float64, time.Time:
			db = db.Where(fmt.Sprintf("%s = ?", column), val)
		}
	}

	return db
}
