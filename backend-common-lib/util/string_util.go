package util

import (
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strings"

	"backend-common-lib/errs"
)

func ConvertDashToEmpty(s string) string {
	if s == "-" {
		return ""
	}
	return fmt.Sprintf("%%%s%%", s)
}

var reAcronym = regexp.MustCompile(`([A-Z]+)([A-Z][a-z])`)
var reCamel = regexp.MustCompile(`([a-z0-9])([A-Z])`)

func CamelToSnake(s string) string {
	s = reAcronym.ReplaceAllString(s, "${1}_${2}")
	s = reCamel.ReplaceAllString(s, "${1}_${2}")
	return strings.ToLower(s)
}

func ParseFieldFromJSON[T any](responseBody []byte, field string) (T, error) {
	result := new(T)
	var rawData json.RawMessage
	if field == "" {
		if err := json.Unmarshal(responseBody, &rawData); err != nil {
			return *result, errs.NewError(http.StatusInternalServerError, err)
		}
	} else {
		var raw map[string]json.RawMessage
		if err := json.Unmarshal(responseBody, &raw); err != nil {
			return *result, errs.NewError(http.StatusInternalServerError, err)
		}
		var ok bool
		rawData, ok = raw[field]
		if !ok {
			return *result, errs.NewError(http.StatusInternalServerError, fmt.Errorf("field '%s' not found", field))
		}

	}

	if err := json.Unmarshal(rawData, &result); err != nil {
		return *result, errs.NewError(http.StatusInternalServerError, err)
	}

	return *result, nil
}
