package util

import (
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"
)

func MapExcelRowsToStruct[T any](rows [][]string) ([]T, error) {
	if len(rows) < 2 {
		return nil, errors.New("no data rows found")
	}

	headers := rows[0]
	var results []T

	for i, row := range rows[1:] {
		var item T
		val := reflect.ValueOf(&item).Elem()

		for j, cell := range row {
			if j >= len(headers) { //case no more header
				break
			}
			header := headers[j]
			if header == "" { //case header is ""
				continue
			}

			// match xlsx:"Header" tag
			for k := 0; k < val.NumField(); k++ {
				field := val.Type().Field(k)
				tag := field.Tag.Get("xlsx") //for name of column in excel file not match column name
				if tag == "" {
					tag = field.Name
				}

				if strings.EqualFold(tag, header) {
					f := val.Field(k)
					if !f.CanSet() {
						break
					}
					err := setValue(f, cell)
					if err != nil {
						return nil, fmt.Errorf("row %d column %s: %w", i+2, header, err)
					}
					break
				}
			}
		}

		results = append(results, item)
	}

	return results, nil
}

func setValue(f reflect.Value, val string) error {
	val = strings.TrimSpace(val)
	if val == "" { // handle case in excel if is "" set nil to pointer
		if f.Kind() == reflect.Ptr {
			f.Set(reflect.Zero(f.Type()))
		}
		return nil
	}

	switch f.Kind() {
	case reflect.String:
		f.SetString(val)
	case reflect.Int:
		i, err := strconv.Atoi(val)
		if err != nil {
			return err
		}
		f.SetInt(int64(i))
	case reflect.Float64:
		num, err := strconv.ParseFloat(val, 64)
		if err != nil {
			return err
		}
		f.SetFloat(num)
	case reflect.Bool:
		b := strings.EqualFold(val, "true") || val == "1"
		f.SetBool(b)
	case reflect.Struct:
		if f.Type() == reflect.TypeOf(time.Time{}) {
			layouts := []string{
				"02/01/2006",       // DD/MM/YYYY
				"2006-01-02",       // YYYY-MM-DD
				"02-01-2006",       // DD-MM-YYYY
				"15:04",            // HH:mm (24h)
				"3:04 PM",          // HH:mm AM/PM
				"15:04:05",         // HH:mm:ss
				"02/01/2006 15:04", // DD/MM/YYYY HH:mm
				"2006-01-02 15:04", // YYYY-MM-DD HH:mm
			}

			var parsed time.Time
			var err error
			for _, layout := range layouts {
				parsed, err = time.Parse(layout, val)
				if err == nil {
					f.Set(reflect.ValueOf(parsed))
					return nil
				}
			}

			return fmt.Errorf("failed to parse '%s' as time.Time: %w", val, err)
		}
	case reflect.Ptr:
		switch f.Type().Elem().Kind() {
		case reflect.Int:
			i, err := strconv.Atoi(val)
			if err != nil {
				return err
			}
			ptr := reflect.New(f.Type().Elem())
			ptr.Elem().SetInt(int64(i))
			f.Set(ptr)
		case reflect.String:
			ptr := reflect.New(f.Type().Elem())
			ptr.Elem().SetString(val)
			f.Set(ptr)
		}
	}
	return nil
}
