package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type BaseResponse struct {
	Status  int         `json:"status"`
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type PaginationData struct {
	Page    int         `json:"page"`
	PerPage int         `json:"perPage"`
	Items   interface{} `json:"items"`
}

func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, BaseResponse{
		Status:  http.StatusOK,
		Code:    "0000",
		Message: "Successfully",
		Data:    data,
	})
}

func Error(c *gin.Context, status int, code string, message string) {
	c.JSON(status, BaseResponse{
		Status:  status,
		Code:    code,
		Message: message,
		Data:    []interface{}{},
	})
}
