package constant

type ErrorCode int

const (
	NotFound           ErrorCode = 404 // Error Data Notfound
	BadRequest         ErrorCode = 400 // Error Invalid Model or Model is null
	Invalidate         ErrorCode = 410 // Error Invalidate data (Handle)
	Conflict           ErrorCode = 411 // Error Conflict data conflict (Handle)
	Integration        ErrorCode = 412 // Error When Integrate External Service
	Invalid            ErrorCode = 413 // Error Not complete
	Unauthorized       ErrorCode = 401 // Error Unauthorized
	UnHandleError      ErrorCode = 500 // Internal Server Error, UnHandle Error
	Expired            ErrorCode = 414 // Token Expired
	Forbidden          ErrorCode = 403
	ValueInvalidate    ErrorCode = 1000
	ValueCannotBeNull  ErrorCode = 1001
	ValueIsRequired    ErrorCode = 1002
	ValueNotInScope    ErrorCode = 1003
	ValueInvalidFormat ErrorCode = 1004
)

func (e ErrorCode) GetDefaultErrorMsg() string {
	switch e {
	case NotFound:
		return "Not resource matched"
	case BadRequest:
		return "Invalid request"
	case Invalidate:
		return "Invalid request"
	case Integration:
		return "Something went wrong"
	case Conflict:
		return "Invalid data request"
	case Invalid:
		return "Not complete"
	case Unauthorized:
		return "Unauthorized"
	case UnHandleError:
		return "Something went wrong"
	default:
		return "Something went wrong"
	}
}

type configParamConst struct {
	MODULE_SYSTEM_ADMIN string

	FLOOR_STATUS                 string
	CONFIG_FEATURE_LOT           string
	SALE_CHANNEL                 string
	AUCTION_STATUS               string
	DAYS_BEFORE_DUE              string
	NATIONALITY                  string
	DROPDOWN_PROXY_BID_STATUS    string
	SERVICE_TYPE                 string
	AUCTION_COLLATERAL           string
	DISPLAY_LOCATION             string
	CAMPAIGN_EVENT_TYPE          string
	AUCTION_TYPE                 string
	DROPDOWN_HELP_REQUEST_STATUS string
}

var ConfigParamConst = configParamConst{
	MODULE_SYSTEM_ADMIN: "SYSTEM_ADMIN",

	FLOOR_STATUS:                 "FLOOR_STATUS",
	CONFIG_FEATURE_LOT:           "CONFIG_FEATURE_LOT",
	SALE_CHANNEL:                 "SALE_CHANNEL",
	AUCTION_STATUS:               "AUCTION_STATUS",
	DAYS_BEFORE_DUE:              "DAYS_BEFORE_DUE",
	NATIONALITY:                  "DROPDOWN_NATIONALITY",
	DROPDOWN_PROXY_BID_STATUS:    "DROPDOWN_PROXY_BID_STATUS",
	SERVICE_TYPE:                 "SERVICE_TYPE",
	AUCTION_COLLATERAL:           "AUCTION_COLLATERAL",
	DISPLAY_LOCATION:             "DISPLAY_LOCATION",
	CAMPAIGN_EVENT_TYPE:          "CAMPAIGN_EVENT_TYPE",
	AUCTION_TYPE:                 "AUCTION_TYPE",
	DROPDOWN_HELP_REQUEST_STATUS: "DROPDOWN_HELP_REQUEST_STATUS",
}

type actionMode struct {
	Create string
	Update string
}

var ActionMode = actionMode{
	Create: "create",
	Update: "update",
}

const (
	ActionStatusWaitingEn   = "Waiting"
	ActionStatusWaitingTh   = "รออนุมัติ"
	ActionStatusApprovedEn  = "Approved"
	ActionStatusApprovedTh  = "อนุมัติแล้ว"
	ActionStatusRejectedEn  = "Rejected"
	ActionStatusRejectedTh  = "ไม่อนุมัติ"
	ActionApprove           = "approve"
	ActionReject            = "reject"
	ProxyBidCancelTypeAdmin = "ADMIN"
	ProxyStatusActiveId     = 1
	ProxyStatusCancelId     = 3
	LotLineErpDeleted       = "ไม่พบข้อมูลจาก ERP"
)

const (
	DateFormatYMD     = "2006-01-02"          // yyyy-mm-dd
	DateFormatDMY     = "02/01/2006"          // dd/mm/yyyy
	TimeFormat        = "15:04:05"            // HH:mm:ss
	DateTimeFormat    = "2006-01-02 15:04:05" // yyyy-mm-dd HH:mm:ss
	DateTimeFormatDMY = "02/01/2006 15:04"    // yyyy-mm-dd HH:mm
	TimeFormatShort   = "15:04"               // yyyy-mm-dd HH:mm

)
