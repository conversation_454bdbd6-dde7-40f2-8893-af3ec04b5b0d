package errs

import (
	"errors"
	"fmt"
	"path/filepath"
	"runtime"

	"backend-common-lib/constant"

	"gorm.io/gorm"
)

type ErrContext struct {
	Code     constant.ErrorCode
	HttpCode int
	Msg      string
	I18nKey  string
	File     string
	Line     int
	error
}

func (e ErrContext) Error() string {
	if e.error != nil {
		return fmt.Sprintf("[%d] %s (%s:%d): %v", e.Code, e.Msg, e.File, e.Line, e.error)
	}
	return fmt.Sprintf("[%d] %s (%s:%d)", e.Code, e.Msg, e.File, e.Line)
}

func NewError(statusCode int, err error) *ErrContext {
	_, file, line, _ := runtime.Caller(1)
	return &ErrContext{
		HttpCode: statusCode,
		Code:     constant.ErrorCode(statusCode),
		Msg:      constant.ErrorCode(statusCode).GetDefaultErrorMsg(),
		File:     filepath.Base(file),
		Line:     line,
		error:    err,
	}
}

func NewBusinessError(statusCode int, code constant.ErrorCode, msg, i18nKey string) *ErrContext {
	_, file, line, _ := runtime.Caller(1)
	return &ErrContext{
		Code:     constant.ErrorCode(code),
		HttpCode: statusCode,
		Msg:      msg,
		File:     filepath.Base(file),
		Line:     line,
		I18nKey:  i18nKey,
	}
}

func IsGormNotFound(err error) bool {
	if err == nil {
		return false
	}
	return errors.Is(err, gorm.ErrRecordNotFound)
}
