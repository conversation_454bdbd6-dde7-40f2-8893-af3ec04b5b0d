package model

type Employee<PERSON><PERSON><PERSON>oin struct {
	ID        int    `gorm:"primaryKey" column:"id" json:"id"`
	FirstName string `column:"first_name" json:"firstName" example:"<PERSON><PERSON><PERSON>"`
	LastName  string `column:"last_name" json:"lastName" example:"Hernandez"`
}

func (EmployeeForJoin) TableName() string {
	return "employee"
}

type AssetTypeForJoin struct {
	ID            int    `gorm:"primaryKey" column:"id" json:"id"`
	AssetTypeCode string `column:"asset_type_code" json:"assetTypeCode"`
	DescriptionTh string `column:"description_th" json:"descriptionTh"`
	DescriptionEn string `column:"description_en" json:"descriptionEn"`
}

func (AssetTypeForJoin) TableName() string {
	return "master_asset_type"
}

type AssetGroupForJoin struct {
	ID             int    `gorm:"primaryKey" column:"id" json:"id"`
	AssetGroupCode string `column:"asset_group_code" json:"assetGroupCode"`
	AssetTypeCode  string `column:"asset_type_code" json:"assetTypeCode"`
	DescriptionTh  string `column:"description_th" json:"descriptionTh"`
	DescriptionEn  string `column:"description_en" json:"descriptionEn"`
}

func (AssetGroupForJoin) TableName() string {
	return "master_asset_group"
}

type AssetLocationFloorForJoin struct {
	ID                int     `gorm:"primaryKey" column:"id" json:"id"`
	LocationFloorCode *string `column:"location_floor_code" json:"locationFloorCode"`
	Floor             *string `column:"floor" json:"floor"`
	DescriptionTH     *string `column:"description_th" json:"descriptionTh"`
	DescriptionEN     *string `column:"description_en" json:"descriptionEn"`
}

func (AssetLocationFloorForJoin) TableName() string {
	return "master_asset_location_floor"
}

type BranchForJoin struct {
	ID            int     `gorm:"primaryKey" column:"id" json:"id"`
	BranchCode    *string `column:"branch_code" json:"branchCode"`
	DescriptionTh *string `column:"description_th" json:"descriptionTh"`
	DescriptionEn *string `column:"description_en" json:"descriptionEn"`
}

func (BranchForJoin) TableName() string {
	return "master_branch"
}

type CustomerGroupForJoin struct {
	ID            int    `column:"id" json:"id"`
	DescriptionTh string `column:"description_th" json:"descriptionTh"`
}

func (CustomerGroupForJoin) TableName() string {
	return "master_customer_group"
}

type EventForJoin struct {
	ID            int     `gorm:"primaryKey" column:"id" json:"id"`
	CompanyCode   *string `column:"company_code" json:"companyCode" example:"Buyer"`
	EventCode     *string `column:"event_code" json:"eventCode" example:"Buyer"`
	DescriptionTh *string `column:"description_th" json:"descriptionTh"`
	DescriptionEn *string `column:"description_en" json:"descriptionEn"`
}

func (EventForJoin) TableName() string {
	return "master_event"
}

type BuyerForJoin struct {
	ID              int     `gorm:"primaryKey" column:"id" json:"id"`
	BidderId        string  `column:"bidder_id" json:"bidderId" example:"1"`
	CustomerGroupId *int    `column:"customer_group_id" json:"customerGroupId"`
	FirstName       *string `column:"first_name" json:"firstname"`
	LastName        *string `column:"last_name" json:"lastname"`
	MiddleName      *string `column:"middle_name" json:"middleName"`
	PhoneNumber     string  `column:"phone_number" json:"phoneNumber"`
}

func (BuyerForJoin) TableName() string {
	return "buyer"
}

type MasterBranchForJoin struct {
	ID            int     `gorm:"primaryKey" column:"id" json:"id"`
	BranchCode    *string `column:"branch_code" json:"branchCode"`
	DescriptionTh *string `column:"description_th" json:"descriptionTh"`
	DescriptionEn *string `column:"description_en" json:"descriptionEn"`
}

func (MasterBranchForJoin) TableName() string {
	return "master_branch"
}

type LotAssetTypeForJoin struct {
	ID          int `gorm:"primaryKey" column:"id" json:"id"`
	LotId       int `gorm:"column:lot_id" json:"lotId"`
	AssetTypeId int `gorm:"column:asset_type_id" json:"assetTypeId"`
}

func (LotAssetTypeForJoin) TableName() string {
	return "lot_asset_type"
}

type ConfigParameterForJoin struct {
	ID              int     `column:"id" json:"id"`
	ParameterModule *string `column:"parameter_module" json:"parameterModule"`
	ParameterName   *string `column:"parameter_name" json:"parameterName"`
	ValueString     *string `column:"value_string" json:"valueString"`
	ValueInt        *int    `column:"value_int" json:"valueInt"`
	ValueString2    *string `column:"value_string2" json:"valueString2"`
}

func (ConfigParameterForJoin) TableName() string {
	return "config_parameters"
}

type PrefixNameForJoin struct {
	ID            int     `column:"id" json:"id"`
	DescriptionTh *string `column:"description_th" json:"descriptionTh"`
	DescriptionEn *string `column:"description_en" json:"descriptionEn"`
}

func (PrefixNameForJoin) TableName() string {
	return "master_prefix_name"
}

type RoleForJoin struct {
	Id       int    `gorm:"primaryKey;column:id" json:"id"`
	RoleName string `column:"role_name" json:"roleName"`
}

func (RoleForJoin) TableName() string {
	return "role"
}

type VendorGroupForJoin struct {
	ID              int     `gorm:"primaryKey" column:"id" json:"id"`
	VendorGroupCode *string `column:"vendor_group_code" json:"vendorGroupCode"`
	DescriptionTh   *string `column:"description_th" json:"descriptionTh"`
	DescriptionEn   *string `column:"description_en" json:"descriptionEn"`
}

func (VendorGroupForJoin) TableName() string {
	return "master_vendor_group"
}
