package model

import (
	"time"

	"backend-common-lib/constant"
	errorcontext "backend-common-lib/errs"
)

type Response[T any] struct {
	Data             *T
	Success          bool
	ResponseDateTime time.Time
	Error            *ErrorHandlerResp
}

func SuccessResponse[T any](data *T) Response[T] {
	return Response[T]{
		Data:             data,
		Success:          true,
		ResponseDateTime: time.Now(),
	}
}

func ErrorResponseWithDefaultMsg(error error) Response[string] {
	return Response[string]{
		Success:          false,
		ResponseDateTime: time.Now(),
		Error: &ErrorHandlerResp{
			Code:    int(constant.UnHandleError),
			Message: constant.UnHandleError.GetDefaultErrorMsg(),
		},
	}

}

func ErrorContextResponse(error *errorcontext.ErrContext) Response[string] {
	if error.Msg == "" {
		error.Msg = error.Code.GetDefaultErrorMsg()
	}
	return Response[string]{
		Success:          false,
		ResponseDateTime: time.Now(),
		Error: &ErrorHandlerResp{
			Code:    int(error.Code),
			Message: error.Msg,
			I18nKey: error.I18nKey,
		},
	}

}
