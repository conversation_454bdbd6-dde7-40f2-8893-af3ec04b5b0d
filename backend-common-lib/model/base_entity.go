package model

import (
	"time"
)

type BaseEntity struct {
	Id          int        `gorm:"primaryKey" column:"id" json:"id" ignore:"true"`
	CreatedBy   *int       `column:"created_by" json:"createdBy" ignore:"true"`
	UpdatedBy   *int       `column:"updated_by" json:"updatedBy" ignore:"true"`
	CreatedDate time.Time  `column:"created_date" json:"createdDate" example:"2020-01-01" ignore:"true"`
	UpdatedDate *time.Time `column:"updated_date" json:"updatedDate" example:"2020-01-01" ignore:"true"`
}
