package model

import (
	"time"
)

type BaseDto struct {
	Id          int        `json:"id" ignore:"true"`
	CreatedBy   *string    `json:"createdBy" ignore:"true"`
	UpdatedBy   *string    `json:"updatedBy" ignore:"true"`
	CreatedDate time.Time  `json:"createdDate" example:"2020-01-01" ignore:"true"`
	UpdatedDate *time.Time `json:"updatedDate" example:"2020-01-01" ignore:"true"`
}

type BaseDtoActionBy struct {
	Id       int  `json:"id" ignore:"true"`
	ActionBy *int `json:"actionBy" ignore:"true"`
}

type DropdownDto struct {
	Id      int    `json:"id"`
	Value   string `json:"value"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
}

type ConfigParameterDropdownDto struct {
	Id            int     `json:"id"`
	ParameterName *string `json:"parameterName"`
	LabelTh       *string `json:"labelTh"`
	LabelEn       *string `json:"labelEn"`
	Value         *int    `json:"value"`
}

type AuctionDropdownDto struct {
	Id      int    `json:"id"`
	Value   string `json:"value"`
	LabelTh string `json:"labelTh"`
	LabelEn string `json:"labelEn"`
	Event   string `json:"event"`
}

type AuctionTagsDropdownDto struct {
	Id               int      `json:"id"`
	Value            string   `json:"value"`
	LabelTh          string   `json:"labelTh"`
	LabelEn          string   `json:"labelEn"`
	AssetTypeNamesTh []string `json:"assetTypeNamesTh"`
	AssetTypeNamesEn []string `json:"assetTypeNamesEn"`
}
