package model

import (
	"strings"
	"time"
)

// Error
type (
	InvalidateField struct {
		Error       bool
		FailedField string
		Tag         string
		Msg         string
		Value       interface{}
	}
	ErrorHandlerResp struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		I18n<PERSON>ey string      `json:"i18nKey"`
		Ext     interface{} `json:"ext"`
	}
)

// Common
type (
	PagingRequest struct {
		PageNumber int    `json:"pageNumber" example:"1"`
		PageLimit  int    `json:"pageLimit" example:"20"`
		SortBy     string `json:"sortBy"`
		SortOrder  string `json:"sortOrder" example:"asc"` // asc or desc
	}
	PagingModel[T any] struct {
		Data   []T `json:"data"`
		Total  int `json:"total"`
		Limit  int `json:"limit"`
		Offset int `json:"offset"`
		Page   int `json:"page"`
	}
)

func NewPagingEmptyModel[T any]() PagingModel[T] {
	return PagingModel[T]{
		Data:   []T{},
		Total:  0,
		Limit:  0,
		Offset: 0,
		Page:   1,
	}
}

type ErpDate struct {
	time.Time
}

func (cd *ErpDate) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), `"`)
	if s == "" {
		return nil
	}
	t, err := time.Parse("2006/01/02", s)
	if err != nil {
		return err
	}
	cd.Time = t
	return nil
}

func (cd *ErpDate) ToTime() *time.Time {
	if cd == nil {
		return nil
	}
	return &cd.Time
}
