package cache

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
)

type RedisContext interface {
	SetWith<PERSON>ey(key string, value interface{}) error
	SetWithKeyAndExp(key string, value interface{}, expiration time.Duration) error
	Get<PERSON><PERSON><PERSON><PERSON>(key string) (string, error)
	DelWith<PERSON>ey(key string) error
	Publish(channel string, message interface{}) error
	Subscribe(channel string, fn func(*redis.Message))
}

type redisContext struct {
	context context.Context
	client  *redis.Client
}

func NewRedisContext(opt *redis.Options) RedisContext {
	client := redis.NewClient(opt)
	ctx := context.Background()
	return &redisContext{
		context: ctx,
		client:  client,
	}
}

func (r *redisContext) SetWithKey(key string, value interface{}) error {
	err := r.client.Set(r.context, key, value, 0).Err()
	if err != nil {
		return err
	}
	return nil
}

func (r *redisContext) SetWithKeyAndExp(key string, value interface{}, expiration time.Duration) error {
	err := r.client.Set(r.context, key, value, expiration).Err()
	if err != nil {
		return err
	}
	return nil
}

func (r *redisContext) GetWithKey(key string) (string, error) {
	exists, err := r.client.Exists(r.context, key).Result()
	if err != nil {
		return "", err
	}
	if exists > 0 {
		value, err := r.client.Get(r.context, key).Result()
		if err != nil {
			return "", err
		}
		return value, nil
	}
	return "", nil
}

func (r *redisContext) DelWithKey(key string) error {
	exists, err := r.client.Exists(r.context, key).Result()
	if err != nil {
		return err
	}
	if exists > 0 {
		err := r.client.Del(r.context, key).Err()
		if err != nil {
			return err
		}
		return nil
	}
	return nil
}

func (r *redisContext) Publish(channel string, message interface{}) error {
	if err := r.client.Publish(r.context, channel, message).Err(); err != nil {
		return err
	}
	return nil
}

func (r *redisContext) Subscribe(channel string, fn func(*redis.Message)) {
	subscriber := r.client.Subscribe(r.context, channel)
	for {
		msg, err := subscriber.ReceiveMessage(r.context)
		if err != nil {
			break
		} else {
			fn(msg)
		}
	}
}
