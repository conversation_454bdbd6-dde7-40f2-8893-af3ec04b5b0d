package services

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"time"

	"backend-common-lib/errs"
)

type HttpMethod string

const (
	HttpGET    HttpMethod = "GET"
	HttpPOST   HttpMethod = "POST"
	HttpPUT    HttpMethod = "PUT"
	HttpPATCH  HttpMethod = "PATCH"
	HttpDELETE HttpMethod = "DELETE"
)

type HttpService interface {
	DoRequest(method HttpMethod, url string, headers map[string]string, body any) ([]byte, error)
	DoRequestWithLongTimeout(method HttpMethod, url string, headers map[string]string, body any) ([]byte, error)

	Get(url string, headers map[string]string) ([]byte, error)
	Post(url string, headers map[string]string, body any) ([]byte, error)
	Put(url string, headers map[string]string, body any) ([]byte, error)
	Delete(url string, headers map[string]string, body any) ([]byte, error)
}

type httpService struct {
	shortClient *http.Client
	longClient  *http.Client
}

func NewHttpService() HttpService {
	return &httpService{
		shortClient: newHttpClient(5 * time.Second),
		longClient:  newHttpClient(245 * time.Second),
	}
}

func newHttpClient(timeout time.Duration) *http.Client {
	return &http.Client{
		Transport: &http.Transport{
			MaxIdleConns:       10,
			IdleConnTimeout:    30 * time.Second,
			DisableCompression: true,
		},
		Timeout: timeout,
	}
}

func (s *httpService) DoRequest(method HttpMethod, url string, headers map[string]string, body any) ([]byte, error) {
	return doHttpRequest(s.shortClient, method, url, headers, body)
}

func (s *httpService) DoRequestWithLongTimeout(method HttpMethod, url string, headers map[string]string, body any) ([]byte, error) {
	return doHttpRequest(s.longClient, method, url, headers, body)
}

func (s *httpService) Get(url string, headers map[string]string) ([]byte, error) {
	return s.DoRequest(HttpGET, url, headers, nil)
}

func (s *httpService) Post(url string, headers map[string]string, body any) ([]byte, error) {
	return s.DoRequest(HttpPOST, url, headers, body)
}

func (s *httpService) Put(url string, headers map[string]string, body any) ([]byte, error) {
	return s.DoRequest(HttpPUT, url, headers, body)
}

func (s *httpService) Delete(url string, headers map[string]string, body any) ([]byte, error) {
	return s.DoRequest(HttpDELETE, url, headers, body)
}

func doHttpRequest(client *http.Client, method HttpMethod, url string, headers map[string]string, body any) ([]byte, error) {
	var bodyReader io.Reader
	if body != nil {
		b, err := json.Marshal(body)
		if err != nil {
			return nil, errs.NewError(http.StatusInternalServerError, err)
		}
		bodyReader = bytes.NewBuffer(b)
	}

	req, err := http.NewRequest(string(method), url, bodyReader)
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	req.Header.Set("Content-Type", "application/json")
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	return respBody, nil
}
