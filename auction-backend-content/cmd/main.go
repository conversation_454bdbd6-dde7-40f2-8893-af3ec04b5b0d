package main

import (
	"fmt"
	"log"

	"content-service/internal/bootstrap"
	"content-service/pkg/config"
	"content-service/pkg/db"
)

func main() {
	config.LoadConfig()

	database := db.ConnectDB()
	// db.<PERSON><PERSON><PERSON>(database)
	// db.Seed(database)

	port := config.Cfg.App.HttpPort
	appCtx := bootstrap.SetupApp(database, &config.Cfg)

	// Start task worker
	// worker.StartTaskWorker(appCtx.Redis)

	log.Printf("🚀 Starting %s on :%d", config.Cfg.App.AppName, port)
	log.Fatal(appCtx.App.Listen(fmt.Sprintf(":%d", port)))
}
