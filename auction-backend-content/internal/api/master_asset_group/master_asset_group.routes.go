package masterassetgroup

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_asset_group"
	service "content-service/internal/service/master_asset_group"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterAssetGroupRepository(db)
	service := service.NewMasterAssetGroupService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("asset-groups")
	route.Post("/", h.SearchMasterAssetGroupFilter)
	route.Put("/status/:id", h.UpdateMasterAssetGroupStatus)
	route.Post("/sync", h.SyncMasterAssetGroupFromErp)
	route.Get("/", h.FindMasterAssetGroupAll)
}
