package masterprefixname

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_prefix_name"
	service "content-service/internal/service/master_prefix_name"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterPrefixNameRepository(db)
	service := service.NewMasterPrefixNameService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}
func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("prefix-names")

	route.Post("", h.SearchMasterPrefixNameFilter)
	route.Put("/status/:id", h.UpdateMasterPrefixNameStatus)
	route.Post("/sync", h.SyncMasterPrefixNameFromErp)
}
