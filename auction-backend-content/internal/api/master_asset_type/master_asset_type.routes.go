package masterassettype

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_asset_type"
	service "content-service/internal/service/master_asset_type"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterAssetTypeRepository(db)
	service := service.NewMasterAssetTypeService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("asset-types")
	route.Post("/", h.SearchMasterAssetTypeFilter)
	route.Put("/status/:id", h.UpdateMasterAssetTypeStatus)
	route.Post("/sync", h.SyncMasterAssetTypeFromErp)
	route.Get("/", h.FindMasterAssetTypeAll)
}
