package mastervatcode

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_vat_code"
	service "content-service/internal/service/master_vat_code"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterVatCodeRepository(db)
	service := service.NewMasterVatCodeService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("vat-codes")
	route.Post("/", h.SearchMasterVatCodeFilter)
	route.Put("/status/:id", h.UpdateMasterVatCodeStatus)
	route.Post("/sync", h.SyncMasterVatCodeFromErp)
}
