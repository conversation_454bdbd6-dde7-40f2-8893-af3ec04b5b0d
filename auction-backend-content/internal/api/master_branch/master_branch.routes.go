package masterbranch

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_branch"
	service "content-service/internal/service/master_branch"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterBranchRepository(db)
	service := service.NewMasterBranchService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("branches")
	route.Get("/all", h.GetMasterBranchAll)
	route.Post("/", h.SearchMasterBranchFilter)
	route.Put("/status/:id", h.UpdateMasterBranchStatus)
	route.Post("/sync", h.SyncMasterBranchFromErp)

}
