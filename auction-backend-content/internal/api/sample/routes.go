package sample_test

import (
	"content-service/internal/worker"

	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
)

func SetupRoutes(r fiber.Router, rdb *redis.Client) {

	r.Get("/alert/:message", func(c *fiber.Ctx) error {
		message := c.Params("message")

		err := worker.ScheduleTask(rdb, message, 5)
		if err != nil {
			return c.Status(500).SendString("Failed to schedule alert: " + err.Error())
		}

		return c.SendString("Alert scheduled: " + message)
	})

}
