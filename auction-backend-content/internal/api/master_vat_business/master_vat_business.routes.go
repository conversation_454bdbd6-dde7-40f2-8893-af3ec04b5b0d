package mastervatbusiness

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_vat_business"
	service "content-service/internal/service/master_vat_business"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterVatBusinessRepository(db)
	service := service.NewMasterVatBusinessService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("vat-businesses")
	route.Post("/", h.SearchMasterVatBusinessFilter)
	route.Put("/status/:id", h.UpdateMasterVatBusinessStatus)
	route.Post("/sync", h.SyncMasterVatBusinessFromErp)
}
