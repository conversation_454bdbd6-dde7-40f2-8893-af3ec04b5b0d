package masterdepartment

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_department"
	service "content-service/internal/service/master_department"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterDepartmentRepository(db)
	service := service.NewMasterDepartmentService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("departments")
	route.Post("/", h.SearchMasterDepartmentFilter)
	route.Put("/status/:id", h.UpdateMasterDepartmentStatus)
	route.Post("/sync", h.SyncMasterDepartmentFromErp)
}
