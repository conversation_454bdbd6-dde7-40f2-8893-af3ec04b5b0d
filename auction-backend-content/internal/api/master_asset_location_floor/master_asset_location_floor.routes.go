package masterassetlocationfloor

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_asset_location_floor"
	service "content-service/internal/service/master_asset_location_floor"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterAssetLocationFloorRepository(db)
	service := service.NewMasterAssetLocationFloorService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("asset-location-floors")
	route.Post("/", h.SearchMasterAssetLocationFloorFilter)
	route.Put("/status/:id", h.UpdateMasterAssetLocationFloorStatus)
	route.Post("/sync", h.SyncMasterAssetLocationFloorFromErp)
}
