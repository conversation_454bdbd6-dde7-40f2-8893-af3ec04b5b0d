package mastercountry

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_country"
	service "content-service/internal/service/master_country"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterCountryRepository(db)
	service := service.NewMasterCountryService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}
func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("countries")

	route.Post("", h.SearchMasterCountryFilter)
	route.Put("/status/:id", h.UpdateMasterCountryStatus)
	route.Post("/sync", h.SyncMasterCountryFromErp)
}
