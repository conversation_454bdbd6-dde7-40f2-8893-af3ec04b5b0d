package masterreason

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_reason"
	service "content-service/internal/service/master_reason"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterReasonRepository(db)
	service := service.NewMasterReasonService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}
func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("reasons")

	route.Post("", h.SearchMasterReasonFilter)
	route.Put("/status/:id", h.UpdateMasterReasonStatus)
	route.Post("/sync", h.SyncMasterReasonFromErp)
}
