package masterdistrict

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_district"
	service "content-service/internal/service/master_district"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterDistrictRepository(db)
	service := service.NewMasterDistrictService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("districts")
	route.Post("/", h.SearchMasterDistrictFilter)
	route.Put("/status/:id", h.UpdateMasterDistrictStatus)
	route.Post("/sync", h.SyncMasterDistrictFromErp)
}
