package feature

import (
	"backend-common-lib/model"
	"content-service/internal/global"
	service "content-service/internal/service/feature"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.FeatureService
	ErpConfig global.ErpConfig
}

func (h *Handler) GetFeatureAll(c *fiber.Ctx) error {

	res, err := h.Service.FindFeatureAll()
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
