package policyconsent

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/policy_consent"
	service "content-service/internal/service/policy_consent"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewPolicyConsentRepository(db)
	service := service.NewPolicyConsentService(repo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("policy-consents")
	route.Post("/:type/search", h.GetPolicyConsent)
	route.Get("/:type/:id", h.GetPolicyConsentByID)
	route.Post("/:type", h.CreatePolicyConsent)
	route.Put("/:type/:id", h.UpdatePolicyConsent)
	route.Put("/:type/status/:id", h.UpdatePolicyConsentStatus)
	route.Delete("/:type/:id", h.DeletePolicyConsent)
	route.Post("/:type/validate", h.ValidateOverlapStartDate)
}
