package mastersalechannel

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_sale_channel"
	service "content-service/internal/service/master_sale_channel"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterSaleChannelRepository(db)
	service := service.NewMasterSaleChannelService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}
func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("sale-channels")

	route.Post("", h.SearchMasterSaleChannelFilter)
	route.Put("/status/:id", h.UpdateMasterSaleChannelStatus)
	route.Post("/sync", h.SyncMasterSaleChannelFromErp)
}
