package mastervendorgroup

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_vendor_group"
	service "content-service/internal/service/master_vendor_group"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterVendorGroupRepository(db)
	service := service.NewMasterVendorGroupService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("vendor-groups")
	route.Post("/", h.SearchMasterVendorGroupFilter)
	route.Put("/status/:id", h.UpdateMasterVendorGroupStatus)
	route.Post("/sync", h.SyncMasterVendorGroupFromErp)
}
