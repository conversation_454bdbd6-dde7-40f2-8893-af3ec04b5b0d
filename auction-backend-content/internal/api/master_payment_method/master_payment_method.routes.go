package masterpaymentmethod

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_payment_method"
	service "content-service/internal/service/master_payment_method"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterPaymentMethodRepository(db)
	service := service.NewMasterPaymentMethodService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}
func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("payment-methods")

	route.Post("", h.SearchMasterPaymentMethodFilter)
	route.Put("/status/:id", h.UpdateMasterPaymentMethodStatus)
	route.Post("/sync", h.SyncMasterPaymentMethodFromErp)
}
