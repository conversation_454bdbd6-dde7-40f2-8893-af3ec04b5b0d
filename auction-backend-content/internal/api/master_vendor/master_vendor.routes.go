package mastervendor

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_vendor"
	service "content-service/internal/service/master_vendor"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterVendorRepository(db)
	service := service.NewMasterVendorService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("vendors")
	route.Post("/", h.SearchMasterVendorFilter)
	route.Put("/status/:id", h.UpdateMasterVendorStatus)
	route.Post("/sync", h.SyncMasterVendorFromErp)
}
