package masterassetgroup

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_register_type_car"
	service "content-service/internal/service/master_register_type_car"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterRegisterTypeCarRepository(db)
	service := service.NewMasterRegisterTypeCarService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("register-type-cars")
	route.Post("/", h.SearchMasterRegisterTypeCarFilter)
	route.Put("/status/:id", h.UpdateMasterRegisterTypeCarStatus)
	route.Post("/sync", h.SyncMasterRegisterTypeCarFromErp)
}
