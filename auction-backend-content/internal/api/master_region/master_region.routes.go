package masterregion

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_region"
	service "content-service/internal/service/master_region"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterRegionRepository(db)
	service := service.NewMasterRegionService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}
func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("regions")

	route.Post("", h.SearchMasterRegionFilter)
	route.Put("/status/:id", h.UpdateMasterRegionStatus)
	route.Post("/sync", h.SyncMasterRegionFromErp)
}
