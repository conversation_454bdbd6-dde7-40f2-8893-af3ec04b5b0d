package masterassetgroup

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_seller_offer"
	service "content-service/internal/service/master_seller_offer"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterSellerOfferRepository(db)
	service := service.NewMasterSellerOfferService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("seller-offers")
	route.Post("/", h.SearchMasterSellerOfferFilter)
	route.Put("/status/:id", h.UpdateMasterSellerOfferStatus)
	route.Post("/sync", h.SyncMasterSellerOfferFromErp)
}
