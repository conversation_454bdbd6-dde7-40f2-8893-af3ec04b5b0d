package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_payment_method"
)

type masterPaymentMethodService struct {
	Repo repository.MasterPaymentMethodRepository
}

type MasterPaymentMethodService interface {
	SearchMasterPaymentMethodFilter(req dto.MasterPaymentMethodPageReqDto) (dto.MasterPaymentMethodPageRespDto[dto.MasterPaymentMethodDto], error)
	UpdateMasterPaymentMethodStatus(req dto.MasterPaymentMethodUpdateReqDto) error
	SyncMasterPaymentMethodFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterPaymentMethodService(repo repository.MasterPaymentMethodRepository) MasterPaymentMethodService {
	return &masterPaymentMethodService{Repo: repo}
}
