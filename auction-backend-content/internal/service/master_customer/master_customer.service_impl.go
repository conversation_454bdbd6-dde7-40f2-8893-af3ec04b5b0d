package service

import (
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_customer"
	"fmt"
	"net/http"

	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterCustomerService) SearchMasterCustomerFilter(req dto.MasterCustomerPageReqDto) (dto.MasterCustomerPageRespDto[dto.MasterCustomerDto], error) {
	resp := dto.MasterCustomerPageRespDto[dto.MasterCustomerDto]{}
	result, err := s.Repo.FindMasterCustomerWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterCustomerWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterCustomerDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterCustomerDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterCustomerLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterCustomerPageRespDto[dto.MasterCustomerDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterCustomerService) UpdateMasterCustomerStatus(req dto.MasterCustomerUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterCustomerFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterCustomerService) SyncMasterCustomerFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getCustomerFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getCustomerFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncCustomer(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterCustomerService) getCustomerFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterCustomerDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErpWithLongTimeout[dto.MasterCustomerSyncErpRespDto](
		erpConfig.CustomerUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterCustomerDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.CustomerNo)] = dto.MasterCustomerDto{
			CompanyCode:          e.CompanyCode,
			CustomerNo:           e.CustomerNo,
			CustomerName:         e.CustomerName,
			CustomerGroup:        e.CustomerGroup,
			PrefixNameCode:       e.PrefixNameCode,
			VatRegistrationNo:    e.VatRegistrationNo,
			TaxBranch:            e.TaxBranch,
			BlackList:            e.BlackList,
			BlackListDate:        e.BlackListDate,
			BlackListDescription: e.BlackListDescription,
			Blocked:              e.Blocked,
			CustomerType:         e.CustomerType,
			PhoneNo:              e.PhoneNo,
			SMSPhoneNo:           e.SMSPhoneNo,
			Email:                e.Email,
			DayOfBirth:           e.DayOfBirth,
			CustomerGrade:        e.CustomerGrade,
			CustomerCreditGroup:  e.CustomerCreditGroup,
			TypeOfDelivery:       e.TypeOfDelivery,
			CustomerAccGroup:     e.CustomerAccGroup,
			RoomNo:               e.RoomNo,
			Address:              e.Address,
			Address2:             e.Address2,
			Building:             e.Building,
			Floor:                e.Floor,
			HouseNo:              e.HouseNo,
			MooNo:                e.MooNo,
			Village:              e.Village,
			Alley:                e.Alley,
			Street:               e.Street,
			SubDistrict:          e.SubDistrict,
			District:             e.District,
			Country:              e.Country,
			City:                 e.City,
			PostCode:             e.PostCode,
			Nationality:          e.Nationality,
			BidderId:             e.BidderId,
			ObjectiveOfAuction:   e.ObjectiveOfAuction,
			IsActive:             e.Status == "Active",
		}
		allKeys[util.Val(e.CustomerNo)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterCustomerService) getCustomerFromDb(allKeys map[string]struct{}) (map[string]entity.MasterCustomer, error) {
	dbList, err := s.Repo.FindMasterCustomerAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterCustomer)
	for _, e := range dbList {
		dbMap[util.Val(e.CustomerNo)] = e
		allKeys[util.Val(e.CustomerNo)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterCustomerService) syncCustomer(actionBy *int, erpMap map[string]dto.MasterCustomerDto, dbMap map[string]entity.MasterCustomer, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterCustomerRepository(tx)
		var toInsert []entity.MasterCustomer
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.CustomerNo = erp.CustomerNo
				temp.CustomerName = erp.CustomerName
				temp.CustomerGroup = erp.CustomerGroup
				temp.PrefixNameCode = erp.PrefixNameCode
				temp.VatRegistrationNo = erp.VatRegistrationNo
				temp.TaxBranch = erp.TaxBranch
				temp.BlackList = erp.BlackList
				temp.BlackListDate = erp.BlackListDate
				temp.BlackListDescription = erp.BlackListDescription
				temp.Blocked = erp.Blocked
				temp.CustomerType = erp.CustomerType
				temp.PhoneNo = erp.PhoneNo
				temp.SMSPhoneNo = erp.SMSPhoneNo
				temp.Email = erp.Email
				temp.DayOfBirth = erp.DayOfBirth
				temp.CustomerGrade = erp.CustomerGrade
				temp.CustomerCreditGroup = erp.CustomerCreditGroup
				temp.TypeOfDelivery = erp.TypeOfDelivery
				temp.CustomerAccGroup = erp.CustomerAccGroup
				temp.RoomNo = erp.RoomNo
				temp.Address = erp.Address
				temp.Address2 = erp.Address2
				temp.Building = erp.Building
				temp.Floor = erp.Floor
				temp.HouseNo = erp.HouseNo
				temp.MooNo = erp.MooNo
				temp.Village = erp.Village
				temp.Alley = erp.Alley
				temp.Street = erp.Street
				temp.SubDistrict = erp.SubDistrict
				temp.District = erp.District
				temp.Country = erp.Country
				temp.City = erp.City
				temp.PostCode = erp.PostCode
				temp.Nationality = erp.Nationality
				temp.BidderId = erp.BidderId
				temp.ObjectiveOfAuction = erp.ObjectiveOfAuction
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if changes := util.CompareStructDeep(db, temp, ""); len(changes) > 0 {
					if err := repo.UpdateMasterCustomerAllFields(&temp); err != nil {
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterCustomer{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:          erp.CompanyCode,
					CustomerNo:           erp.CustomerNo,
					CustomerName:         erp.CustomerName,
					CustomerGroup:        erp.CustomerGroup,
					PrefixNameCode:       erp.PrefixNameCode,
					VatRegistrationNo:    erp.VatRegistrationNo,
					TaxBranch:            erp.TaxBranch,
					BlackList:            erp.BlackList,
					BlackListDate:        erp.BlackListDate,
					BlackListDescription: erp.BlackListDescription,
					Blocked:              erp.Blocked,
					CustomerType:         erp.CustomerType,
					PhoneNo:              erp.PhoneNo,
					SMSPhoneNo:           erp.SMSPhoneNo,
					Email:                erp.Email,
					DayOfBirth:           erp.DayOfBirth,
					CustomerGrade:        erp.CustomerGrade,
					CustomerCreditGroup:  erp.CustomerCreditGroup,
					TypeOfDelivery:       erp.TypeOfDelivery,
					CustomerAccGroup:     erp.CustomerAccGroup,
					RoomNo:               erp.RoomNo,
					Address:              erp.Address,
					Address2:             erp.Address2,
					Building:             erp.Building,
					Floor:                erp.Floor,
					HouseNo:              erp.HouseNo,
					MooNo:                erp.MooNo,
					Village:              erp.Village,
					Alley:                erp.Alley,
					Street:               erp.Street,
					SubDistrict:          erp.SubDistrict,
					District:             erp.District,
					Country:              erp.Country,
					City:                 erp.City,
					PostCode:             erp.PostCode,
					Nationality:          erp.Nationality,
					BidderId:             erp.BidderId,
					ObjectiveOfAuction:   erp.ObjectiveOfAuction,
					IsActive:             erp.IsActive,
					IsDeletedByErp:       false,
					LatestSyncDate:       currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterCustomerAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterCustomerListWithBatches(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		_, err := repo.UpdatesMasterCustomerFieldsWhere(map[string]interface{}{"latest_sync_date": currentDateTime, "updated_date": currentDateTime, "updated_by": actionBy}, "is_deleted_by_erp = ?", false)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		return nil
	})
}
