package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_asset_group"
)

type masterAssetGroupService struct {
	Repo repository.MasterAssetGroupRepository
}

type MasterAssetGroupService interface {
	SearchMasterAssetGroupFilter(req dto.MasterAssetGroupPageReqDto) (dto.MasterAssetGroupPageRespDto[dto.MasterAssetGroupDto], error)
	UpdateMasterAssetGroupStatus(req dto.MasterAssetGroupUpdateReqDto) error
	SyncMasterAssetGroupFromErp(ActionBy *int, erpConfig global.ErpConfig) error
	FindMasterAssetGroupAll() ([]entity.MasterAssetGroup, error)
}

func NewMasterAssetGroupService(repo repository.MasterAssetGroupRepository) MasterAssetGroupService {
	return &masterAssetGroupService{Repo: repo}
}
