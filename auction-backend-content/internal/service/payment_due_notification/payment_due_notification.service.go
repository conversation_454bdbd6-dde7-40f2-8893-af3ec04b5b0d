package service

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/payment_due_notification"
	repositoryExcludedBuyer "content-service/internal/repository/payment_due_notification_excluded_buyer"
)

type paymentDueNotificationService struct {
	Repo              repository.PaymentDueNotificationRepository
	ExcludedBuyerRepo repositoryExcludedBuyer.PaymentDueNotificationExcludedBuyerRepository
}

type PaymentDueNotificationService interface {
	GetPaymentDueNotification(req model.PagingRequest) (model.PagingModel[dto.PaymentDueNotificationDto], error)
	GetPaymentDueNotificationByID(id int) (dto.PaymentDueNotificationDto, error)
	CreatePaymentDueNotification(req dto.PaymentDueNotificationDto, actionBy *int) error
	UpdatePaymentDueNotification(req dto.PaymentDueNotificationDto, actionBy *int) error
	UpdatePaymentDueNotificationStatus(req dto.PaymentDueNotificationDto, actionBy *int) error
	DeletePaymentDueNotification(id int) error
}

func NewPaymentDueNotificationService(
	repo repository.PaymentDueNotificationRepository,
	repoExcludedBuyerRepo repositoryExcludedBuyer.PaymentDueNotificationExcludedBuyerRepository,
) PaymentDueNotificationService {
	return &paymentDueNotificationService{
		Repo:              repo,
		ExcludedBuyerRepo: repoExcludedBuyerRepo,
	}
}
