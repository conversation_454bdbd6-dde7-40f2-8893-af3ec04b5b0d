package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_district"
)

type masterDistrictService struct {
	Repo repository.MasterDistrictRepository
}

type MasterDistrictService interface {
	SearchMasterDistrictFilter(req dto.MasterDistrictPageReqDto) (dto.MasterDistrictPageRespDto[dto.MasterDistrictDto], error)
	UpdateMasterDistrictStatus(req dto.MasterDistrictUpdateReqDto) error
	SyncMasterDistrictFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterDistrictService(repo repository.MasterDistrictRepository) MasterDistrictService {
	return &masterDistrictService{Repo: repo}
}
