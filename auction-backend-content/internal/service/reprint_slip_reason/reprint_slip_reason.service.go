package service

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/reprint_slip_reason"
)

type reprintSlipReasonService struct {
	Repo repository.ReprintSlipReasonRepository
}

type ReprintSlipReasonService interface {
	GetAllReprintSlipReason(req dto.ReprintSlipReasonPageReqDto) (model.PagingModel[dto.ReprintSlipReasonDto], error)
	GetReprintSlipReasonById(id int) (dto.ReprintSlipReasonDto, error)

	CreateReprintSlipReason(req dto.ReprintSlipReasonUpdateReqDto) error
	DeleteReprintSlipReasonByID(id int) error

	UpdateReprintSlipReason(req dto.ReprintSlipReasonUpdateReqDto) error
	UpdateReprintSlipReasonStatus(req dto.ReprintSlipReasonUpdateReqDto) error
}

func NewReprintSlipReasonService(repo repository.ReprintSlipReasonRepository) ReprintSlipReasonService {
	return &reprintSlipReasonService{Repo: repo}
}
