package service

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"fmt"
	"net/http"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"gorm.io/gorm"
)

func validateReprintSlipReason(reprintSlipReason *dto.ReprintSlipReasonUpdateReqDto) *errs.ErrContext {
	if reprintSlipReason.Reason == nil || *reprintSlipReason.Reason == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("reason is required"))
	}
	return nil
}

func (s *reprintSlipReasonService) GetAllReprintSlipReason(req dto.ReprintSlipReasonPageReqDto) (model.PagingModel[dto.ReprintSlipReasonDto], error) {
	resp := model.PagingModel[dto.ReprintSlipReasonDto]{}
	result, err := s.Repo.FindAllReprintSlipReason(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountAllReprintSlipReason(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.ReprintSlipReasonDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.ReprintSlipReasonDto](v)
	}

	//NOTE - Response
	resp = *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit)

	return resp, nil
}

func (s *reprintSlipReasonService) GetReprintSlipReasonById(id int) (dto.ReprintSlipReasonDto, error) {
	resp := dto.ReprintSlipReasonDto{}
	result, err := s.Repo.FindReprintSlipReasonById(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return resp, errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", id), "")
		}
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	resp = util.MapToWithCreatedByAndUpdatedBy[dto.ReprintSlipReasonDto](result)
	return resp, nil
}

func (s *reprintSlipReasonService) CreateReprintSlipReason(req dto.ReprintSlipReasonUpdateReqDto) error {
	//NOTE - Check Required fields
	if err := validateReprintSlipReason(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	entityReprintSlipReason := util.MapToPtr[entity.ReprintSlipReason](req)
	if entityReprintSlipReason == nil {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("Invalid input data"))
	}

	//NOTE - check duplicate
	exist, err := s.Repo.FindByReason(*entityReprintSlipReason.Reason)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "reason is duplicated", "error.reprintSlipReasons.duplicateReason")
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	now := util.Now()
	entityReprintSlipReason.BaseEntity = &model.BaseEntity{
		CreatedBy:   req.ActionBy,
		CreatedDate: now,
		UpdatedBy:   req.ActionBy,
		UpdatedDate: &now,
	}

	if err := s.Repo.InsertReprintSlipReason(*entityReprintSlipReason); err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	return nil
}

func (s *reprintSlipReasonService) UpdateReprintSlipReason(req dto.ReprintSlipReasonUpdateReqDto) error {
	//NOTE - Check Required fields
	if err := validateReprintSlipReason(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	//NOTE - check duplicate
	exist, err := s.Repo.FindDuplicateReasonExcludingId(*req.Reason, req.Id)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "reason is duplicated", "error.reprintSlipReasons.duplicateReason")
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	now := util.Now()
	fieldsToUpdate := map[string]interface{}{
		"reason":       req.Reason,
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": &now,
	}

	affectedRows, err := s.Repo.UpdatesReprintSlipReasonFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}
	return nil
}

func (s *reprintSlipReasonService) UpdateReprintSlipReasonStatus(req dto.ReprintSlipReasonUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesReprintSlipReasonFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *reprintSlipReasonService) DeleteReprintSlipReasonByID(id int) error {
	affectedRows, err := s.Repo.DeleteReprintSlipReasonByID(id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", id), "")
	}
	return nil
}
