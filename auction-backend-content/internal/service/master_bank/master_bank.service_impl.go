package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_bank"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterBankService) SearchMasterBankFilter(req dto.MasterBankPageReqDto) (dto.MasterBankPageRespDto[dto.MasterBankDto], error) {
	resp := dto.MasterBankPageRespDto[dto.MasterBankDto]{}
	result, err := s.Repo.FindMasterBankWithFilter(req)
	if err != nil {
		return resp, errs.NewError(fiber.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterBankWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterBankDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterBankDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(fiber.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterBankPageRespDto[dto.MasterBankDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterBankService) UpdateMasterBankStatus(req dto.MasterBankUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterBankFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterBankService) SyncMasterBankFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getBankFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getBankFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncBank(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterBankService) getBankFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterBankDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterBankSyncErpRespDto](
		erpConfig.BankUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterBankDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		bankCode := util.Val(e.BankAccountCode)
		erpMap[bankCode] = dto.MasterBankDto{
			CompanyCode:     e.CompanyCode,
			BankAccountCode: e.BankAccountCode,
			DescriptionTh:   e.DescriptionTh,
			DescriptionEn:   e.DescriptionEn,
			IsActive:        e.Status == "Active",
		}
		allKeys[bankCode] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterBankService) getBankFromDb(allKeys map[string]struct{}) (map[string]entity.MasterBank, error) {
	dbList, err := s.Repo.FindMasterBankAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterBank)
	for _, e := range dbList {
		bankAccountCode := util.Val(e.BankAccountCode)
		dbMap[bankAccountCode] = e
		allKeys[bankAccountCode] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterBankService) syncBank(actionBy *int, erpMap map[string]dto.MasterBankDto, dbMap map[string]entity.MasterBank, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterBankRepository(tx)
		var toInsert []entity.MasterBank
		currentDateTime := util.Now()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.BankAccountCode = erp.BankAccountCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = &currentDateTime

				if err := repo.UpdateMasterBankAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterBank{
					BaseEntity: &model.BaseEntity{
						CreatedDate: currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: &currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:     erp.CompanyCode,
					BankAccountCode: erp.BankAccountCode,
					DescriptionTh:   erp.DescriptionTh,
					DescriptionEn:   erp.DescriptionEn,
					IsActive:        erp.IsActive,
					IsDeletedByErp:  false,
					LatestSyncDate:  &currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = &currentDateTime

				if err := repo.UpdateMasterBankAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterBankList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
