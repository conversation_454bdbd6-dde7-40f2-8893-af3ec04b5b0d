package service

import (
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/password_expiry"
)

type passwordExpiryService struct {
	Repo repository.PasswordExpiryRepository
}

type PasswordExpiryService interface {
	GetPasswordExpiry() ([]dto.PasswordExpiryDto, error)
	UpdatePasswordExpiry(req dto.PasswordExpiryReqDto) error
}

func NewPasswordExpiryService(repo repository.PasswordExpiryRepository) PasswordExpiryService {
	return &passwordExpiryService{Repo: repo}
}
