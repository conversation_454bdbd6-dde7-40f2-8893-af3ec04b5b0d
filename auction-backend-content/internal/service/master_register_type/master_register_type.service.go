package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_register_type"
)

type masterRegisterTypeService struct {
	Repo repository.MasterRegisterTypeRepository
}

type MasterRegisterTypeService interface {
	SearchMasterRegisterTypeFilter(req dto.MasterRegisterTypePageReqDto) (dto.MasterRegisterTypePageRespDto[dto.MasterRegisterTypeDto], error)
	UpdateMasterRegisterTypeStatus(req dto.MasterRegisterTypeUpdateReqDto) error
	SyncMasterRegisterTypeFromErp(ActionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterRegisterTypeService(repo repository.MasterRegisterTypeRepository) MasterRegisterTypeService {
	return &masterRegisterTypeService{Repo: repo}
}
