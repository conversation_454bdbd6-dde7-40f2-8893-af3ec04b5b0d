package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_holiday"
)

type masterHolidayService struct {
	Repo repository.MasterHolidayRepository
}

type MasterHolidayService interface {
	SearchMasterHolidayFilter(req dto.MasterHolidayPageReqDto) (dto.MasterHolidayPageRespDto[dto.MasterHolidayDto], error)
	UpdateMasterHolidayStatus(req dto.MasterHolidayUpdateReqDto) error
	SyncMasterHolidayFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterHolidayService(repo repository.MasterHolidayRepository) MasterHolidayService {
	return &masterHolidayService{Repo: repo}
}
