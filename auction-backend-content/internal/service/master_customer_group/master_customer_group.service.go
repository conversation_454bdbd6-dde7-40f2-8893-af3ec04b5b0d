package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_customer_group"
)

type masterCustomerGroupService struct {
	Repo repository.MasterCustomerGroupRepository
}

type MasterCustomerGroupService interface {
	SearchMasterCustomerGroupFilter(req dto.MasterCustomerGroupPageReqDto) (dto.MasterCustomerGroupPageRespDto[dto.MasterCustomerGroupDto], error)
	UpdateMasterCustomerGroupStatus(req dto.MasterCustomerGroupUpdateReqDto) error
	SyncMasterCustomerGroupFromErp(actionBy *int, erpConfig global.ErpConfig) error
	FindMasterCustomerGroupAll() ([]entity.MasterCustomerGroup, error)
}

func NewMasterCustomerGroupService(repo repository.MasterCustomerGroupRepository) MasterCustomerGroupService {
	return &masterCustomerGroupService{Repo: repo}
}
