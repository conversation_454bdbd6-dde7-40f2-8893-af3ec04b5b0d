package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_cost_revenue"
)

type masterCostRevenueService struct {
	Repo repository.MasterCostRevenueRepository
}

type MasterCostRevenueService interface {
	SearchMasterCostRevenueFilter(req dto.MasterCostRevenuePageReqDto) (dto.MasterCostRevenuePageRespDto[dto.MasterCostRevenueDto], error)
	UpdateMasterCostRevenueStatus(req dto.MasterCostRevenueUpdateReqDto) error
	SyncMasterCostRevenueFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterCostRevenueService(repo repository.MasterCostRevenueRepository) MasterCostRevenueService {
	return &masterCostRevenueService{Repo: repo}
}
