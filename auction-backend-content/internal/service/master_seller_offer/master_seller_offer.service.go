package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_seller_offer"
)

type masterSellerOfferService struct {
	Repo repository.MasterSellerOfferRepository
}

type MasterSellerOfferService interface {
	SearchMasterSellerOfferFilter(req dto.MasterSellerOfferPageReqDto) (dto.MasterSellerOfferPageRespDto[dto.MasterSellerOfferDto], error)
	UpdateMasterSellerOfferStatus(req dto.MasterSellerOfferUpdateReqDto) error
	SyncMasterSellerOfferFromErp(ActionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterSellerOfferService(repo repository.MasterSellerOfferRepository) MasterSellerOfferService {
	return &masterSellerOfferService{Repo: repo}
}
