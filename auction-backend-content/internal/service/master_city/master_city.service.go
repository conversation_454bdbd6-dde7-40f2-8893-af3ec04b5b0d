package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_city"
)

type masterCityService struct {
	Repo repository.MasterCityRepository
}

type MasterCityService interface {
	SearchMasterCityFilter(req dto.MasterCityPageReqDto) (dto.MasterCityPageRespDto[dto.MasterCityDto], error)
	UpdateMasterCityStatus(req dto.MasterCityUpdateReqDto) error
	SyncMasterCityFromErp(ActionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterCityService(repo repository.MasterCityRepository) MasterCityService {
	return &masterCityService{Repo: repo}
}
