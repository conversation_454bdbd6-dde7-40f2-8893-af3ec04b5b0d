package service

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/proxy_bid_cancel_reason"
)

type proxyBidCancelReasonService struct {
	Repo repository.ProxyBidCancelReasonRepository
}

type ProxyBidCancelReasonService interface {
	GetProxyBidCancelReason(req model.PagingRequest) (model.PagingModel[dto.ProxyBidCancelReasonDto], error)
	GetProxyBidCancelReasonByID(id int) (dto.ProxyBidCancelReasonDto, error)
	CreateProxyBidCancelReason(req dto.ProxyBidCancelReasonReqDto) error
	UpdateProxyBidCancelReason(req dto.ProxyBidCancelReasonReqDto) error
	UpdateProxyBidCancelReasonStatus(req dto.ProxyBidCancelReasonReqDto) error
	DeleteProxyBidCancelReason(id int) error
}

func NewProxyBidCancelReasonService(repo repository.ProxyBidCancelReasonRepository) ProxyBidCancelReasonService {
	return &proxyBidCancelReasonService{Repo: repo}
}
