package service

import (
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_department"
	"fmt"
	"net/http"

	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterDepartmentService) SearchMasterDepartmentFilter(req dto.MasterDepartmentPageReqDto) (dto.MasterDepartmentPageRespDto[dto.MasterDepartmentDto], error) {
	resp := dto.MasterDepartmentPageRespDto[dto.MasterDepartmentDto]{}
	result, err := s.Repo.FindMasterDepartmentWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterDepartmentWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterDepartmentDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterDepartmentDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterDepartmentLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterDepartmentPageRespDto[dto.MasterDepartmentDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterDepartmentService) UpdateMasterDepartmentStatus(req dto.MasterDepartmentUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterDepartmentFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterDepartmentService) SyncMasterDepartmentFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getDepartmentFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getDepartmentFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncDepartment(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterDepartmentService) getDepartmentFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterDepartmentDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterDepartmentSyncErpRespDto](
		erpConfig.DepartmentUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterDepartmentDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.DepartmentCode)] = dto.MasterDepartmentDto{
			DepartmentCode:   e.DepartmentCode,
			DepartmentNameTh: e.DepartmentNameTh,
			DepartmentNameEn: e.DepartmentNameEn,
			IsActive:         e.Status == "Active",
		}
		allKeys[util.Val(e.DepartmentCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterDepartmentService) getDepartmentFromDb(allKeys map[string]struct{}) (map[string]entity.MasterDepartment, error) {
	dbList, err := s.Repo.FindMasterDepartmentAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterDepartment)
	for _, e := range dbList {
		dbMap[util.Val(e.DepartmentCode)] = e
		allKeys[util.Val(e.DepartmentCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterDepartmentService) syncDepartment(actionBy *int, erpMap map[string]dto.MasterDepartmentDto, dbMap map[string]entity.MasterDepartment, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterDepartmentRepository(tx)
		var toInsert []entity.MasterDepartment
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.DepartmentCode = erp.DepartmentCode
				temp.DepartmentNameTh = erp.DepartmentNameTh
				temp.DepartmentNameEn = erp.DepartmentNameEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterDepartmentAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterDepartment{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					DepartmentCode:   erp.DepartmentCode,
					DepartmentNameTh: erp.DepartmentNameTh,
					DepartmentNameEn: erp.DepartmentNameEn,
					IsActive:         erp.IsActive,
					IsDeletedByErp:   false,
					LatestSyncDate:   currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterDepartmentAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterDepartmentList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
