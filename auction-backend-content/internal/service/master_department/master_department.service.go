package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_department"
)

type masterDepartmentService struct {
	Repo repository.MasterDepartmentRepository
}

type MasterDepartmentService interface {
	SearchMasterDepartmentFilter(req dto.MasterDepartmentPageReqDto) (dto.MasterDepartmentPageRespDto[dto.MasterDepartmentDto], error)
	UpdateMasterDepartmentStatus(req dto.MasterDepartmentUpdateReqDto) error
	SyncMasterDepartmentFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterDepartmentService(repo repository.MasterDepartmentRepository) MasterDepartmentService {
	return &masterDepartmentService{Repo: repo}
}
