package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"fmt"
	"net/http"

	"gorm.io/gorm"
)

func (s *floorSpecialRemarkService) GetFloorSpecialRemark(req dto.FloorSpecialRemarkSearchReqDto) (model.PagingModel[dto.FloorSpecialRemarkDto], error) {
	resp := model.PagingModel[dto.FloorSpecialRemarkDto]{}

	//NOTE - Get List
	result, err := s.Repo.FindFloorSpecialRemark(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountFloorSpecialRemark(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.FloorSpecialRemarkDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.FloorSpecialRemarkDto](v)
	}

	//NOTE - Response
	resp = *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit)

	return resp, nil
}

func (s *floorSpecialRemarkService) GetFloorSpecialRemarkByID(id int) (dto.FloorSpecialRemarkDto, error) {
	resp := dto.FloorSpecialRemarkDto{}
	result, err := s.Repo.FindFloorSpecialRemarkByID(id)
	if err != nil {
		if errs.IsGormNotFound(err) {
			return dto.FloorSpecialRemarkDto{}, errs.NewError(http.StatusNotFound, err)
		}
		return dto.FloorSpecialRemarkDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	resp = util.MapToWithCreatedByAndUpdatedBy[dto.FloorSpecialRemarkDto](result)
	return resp, nil
}

func validateFloorSpecialRemark(floorSpecialRemark dto.FloorSpecialRemarkReqDto) error {
	if floorSpecialRemark.Remark == nil || util.Val(floorSpecialRemark.Remark) == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("remark is required"))
	}
	return nil
}

func (s *floorSpecialRemarkService) CreateFloorSpecialRemark(req dto.FloorSpecialRemarkReqDto) error {
	now := util.Now()

	//NOTE - Check Required fields
	err := validateFloorSpecialRemark(req)
	if err != nil {
		return err
	}

	//NOTE - Check for duplicate customer_group_id
	exist, err := s.Repo.FindByRemark(util.Val(req.Remark), req.Id)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "remark is duplicated", "error.floorSpecialRemark.duplicateRemark")
	} else if err != nil && !errs.IsGormNotFound(err) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Create Payment Due Notification
	entityFloorSpecialRemark := util.MapToPtr[entity.FloorSpecialRemark](req)
	if entityFloorSpecialRemark == nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "Invalid request data", "")
	}

	entityFloorSpecialRemark.BaseEntity = &model.BaseEntity{
		CreatedBy:   req.ActionBy,
		CreatedDate: now,
		UpdatedBy:   req.ActionBy,
		UpdatedDate: &now,
	}

	if err := s.Repo.InsertFloorSpecialRemark(entityFloorSpecialRemark); err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	return nil
}

func (s *floorSpecialRemarkService) UpdateFloorSpecialRemark(req dto.FloorSpecialRemarkReqDto) error {
	//NOTE - Check Required fields
	if err := validateFloorSpecialRemark(req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	//NOTE - check duplicate
	exist, err := s.Repo.FindByRemark(util.Val(req.Remark), req.Id)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "remark is duplicated", "error.floorSpecialRemark.duplicateRemark")
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	now := util.Now()
	fieldsToUpdate := map[string]interface{}{
		"remark":       req.Remark,
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": &now,
	}

	affectedRows, err := s.Repo.UpdateFloorSpecialRemark(req.Id, fieldsToUpdate)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}
	return nil
}

func (s *floorSpecialRemarkService) UpdateFloorSpecialRemarkStatus(req dto.FloorSpecialRemarkReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdateFloorSpecialRemark(req.Id, fieldsToUpdate)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *floorSpecialRemarkService) DeleteFloorSpecialRemark(id int) error {
	affectedRows, err := s.Repo.DeleteFloorSpecialRemark(id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", id), "")
	}
	return nil
}
