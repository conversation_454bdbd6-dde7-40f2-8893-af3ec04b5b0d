package service

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/additional_service"
	serviceAssetRepo "content-service/internal/repository/additional_service_asset"
	serviceCustomerGroupRepo "content-service/internal/repository/additional_service_customer_group"
	serviceDisplayRepo "content-service/internal/repository/additional_service_display"
	serviceProviderRepo "content-service/internal/repository/additional_service_provider"
)

type additionalServiceService struct {
	Repo                 repository.AdditionalServiceRepository
	ServiceAssetRepo     serviceAssetRepo.AdditionalServiceAssetRepository
	ServiceCustomerGroup serviceCustomerGroupRepo.AdditionalServiceCustomerGroupRepository
	ServiceProviderRepo  serviceProviderRepo.AdditionalServiceProviderRepository
	ServiceDisplayRepo   serviceDisplayRepo.AdditionalServiceDisplayRepository
}

type AdditionalServiceService interface {
	GetAdditionalService(req model.PagingRequest) (model.PagingModel[dto.AdditionalServiceDto], error)
	// GetAdditionalServiceByID(id int) (dto.AdditionalServiceDto, error)
	// CreateAdditionalService(req dto.AdditionalServiceReqDto) error
	// UpdateAdditionalService(req dto.AdditionalServiceReqDto) error
	// UpdateAdditionalServiceStatus(req dto.AdditionalServiceReqDto) error
	// DeleteAdditionalService(id int) error
}

func NewAdditionalServiceService(
	repo repository.AdditionalServiceRepository,
	serviceAssetRepo serviceAssetRepo.AdditionalServiceAssetRepository,
	serviceCustomerGroupRepo serviceCustomerGroupRepo.AdditionalServiceCustomerGroupRepository,
	serviceProviderRepo serviceProviderRepo.AdditionalServiceProviderRepository,
	serviceDisplayRepo serviceDisplayRepo.AdditionalServiceDisplayRepository,
) AdditionalServiceService {
	return &additionalServiceService{
		Repo:                 repo,
		ServiceAssetRepo:     serviceAssetRepo,
		ServiceCustomerGroup: serviceCustomerGroupRepo,
		ServiceProviderRepo:  serviceProviderRepo,
		ServiceDisplayRepo:   serviceDisplayRepo,
	}
}
