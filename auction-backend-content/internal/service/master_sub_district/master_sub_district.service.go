package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_sub_district"
)

type masterSubDistrictService struct {
	Repo repository.MasterSubDistrictRepository
}

type MasterSubDistrictService interface {
	SearchMasterSubDistrictFilter(req dto.MasterSubDistrictPageReqDto) (dto.MasterSubDistrictPageRespDto[dto.MasterSubDistrictDto], error)
	UpdateMasterSubDistrictStatus(req dto.MasterSubDistrictUpdateReqDto) error
	SyncMasterSubDistrictFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterSubDistrictService(repo repository.MasterSubDistrictRepository) MasterSubDistrictService {
	return &masterSubDistrictService{Repo: repo}
}
