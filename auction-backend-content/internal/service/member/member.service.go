package service

import (
	"content-service/internal/model/dto"
	buyerRepository "content-service/internal/repository/buyer"
)

type memberService struct {
	BuyerRepo buyerRepository.BuyerRepository
}

type MemberService interface {
	SearchMemberWithFilter(req dto.MemberSearchReqDto) (dto.MemberPageRespDto[dto.MemberSearchRespDto], error)
	UpdateMemberStatus(req dto.UpdateMemberStatusReqDto) error
}

func NewMemberService(buyerRepo buyerRepository.BuyerRepository,
) MemberService {
	return &memberService{BuyerRepo: buyerRepo}
}
