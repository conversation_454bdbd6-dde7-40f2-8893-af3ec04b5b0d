package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_event"
)

type masterEventService struct {
	Repo repository.MasterEventRepository
}

type MasterEventService interface {
	GetMasterEventAll() (dto.MasterEventListDto, error)
	SearchMasterEventFilter(req dto.MasterEventPageReqDto) (dto.MasterEventPageRespDto[dto.MasterEventDto], error)
	UpdateMasterEventStatus(req dto.MasterEventUpdateReqDto) error
	SyncMasterEventFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterEventService(repo repository.MasterEventRepository) MasterEventService {
	return &masterEventService{Repo: repo}
}
