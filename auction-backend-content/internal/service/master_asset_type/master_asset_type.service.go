package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_asset_type"
)

type masterAssetTypeService struct {
	Repo repository.MasterAssetTypeRepository
}

type MasterAssetTypeService interface {
	SearchMasterAssetTypeFilter(req dto.MasterAssetTypePageReqDto) (dto.MasterAssetTypePageRespDto[dto.MasterAssetTypeDto], error)
	UpdateMasterAssetTypeStatus(req dto.MasterAssetTypeUpdateReqDto) error
	SyncMasterAssetTypeFromErp(actionBy *int, erpConfig global.ErpConfig) error
	FindMasterAssetTypeAll() ([]entity.MasterAssetType, error)
}

func NewMasterAssetTypeService(repo repository.MasterAssetTypeRepository) MasterAssetTypeService {
	return &masterAssetTypeService{Repo: repo}
}
