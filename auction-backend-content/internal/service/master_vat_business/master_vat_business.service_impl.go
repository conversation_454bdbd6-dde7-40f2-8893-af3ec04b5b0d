package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_vat_business"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterVatBusinessService) SearchMasterVatBusinessFilter(req dto.MasterVatBusinessPageReqDto) (dto.MasterVatBusinessPageRespDto[dto.MasterVatBusinessDto], error) {
	resp := dto.MasterVatBusinessPageRespDto[dto.MasterVatBusinessDto]{}
	result, err := s.Repo.FindMasterVatBusinessWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterVatBusinessWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterVatBusinessDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterVatBusinessDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterVatBusinessLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Set response
	resp = dto.MasterVatBusinessPageRespDto[dto.MasterVatBusinessDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterVatBusinessService) UpdateMasterVatBusinessStatus(req dto.MasterVatBusinessUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterVatBusinessFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterVatBusinessService) SyncMasterVatBusinessFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getVatBusinessFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getVatBusinessFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncVatBusiness(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterVatBusinessService) getVatBusinessFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterVatBusinessDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterVatBusinessSyncErpRespDto](
		erpConfig.VatBusinessUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterVatBusinessDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.VatBusinessCode)] = dto.MasterVatBusinessDto{
			CompanyCode:       e.CompanyCode,
			VatBusinessCode:   e.VatBusinessCode,
			IsActive:          e.Status == "Active",
			Description:       e.Description,
			TaxEstablishment:  e.TaxEstablishment,
			TaxBranchNo:       e.TaxBranchNo,
			CompanyName:       e.CompanyName,
			CompanyName2:      e.CompanyName2,
			CompanyAddress:    e.CompanyAddress,
			CompanyAddress2:   e.CompanyAddress2,
			CompanyAddress3:   e.CompanyAddress3,
			VatRegistrationNo: e.VatRegistrationNo,
			PhoneNo:           e.PhoneNo,
			FaxNo:             e.FaxNo,
		}
		allKeys[util.Val(e.VatBusinessCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterVatBusinessService) getVatBusinessFromDb(allKeys map[string]struct{}) (map[string]entity.MasterVatBusiness, error) {
	dbList, err := s.Repo.FindMasterVatBusinessAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterVatBusiness)
	for _, e := range dbList {
		dbMap[util.Val(e.VatBusinessCode)] = e
		allKeys[util.Val(e.VatBusinessCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterVatBusinessService) syncVatBusiness(actionBy *int, erpMap map[string]dto.MasterVatBusinessDto, dbMap map[string]entity.MasterVatBusiness, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterVatBusinessRepository(tx)
		var toInsert []entity.MasterVatBusiness
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.VatBusinessCode = erp.VatBusinessCode
				temp.Description = erp.Description
				temp.TaxEstablishment = erp.TaxEstablishment
				temp.TaxBranchNo = erp.TaxBranchNo
				temp.CompanyName = erp.CompanyName
				temp.CompanyName2 = erp.CompanyName2
				temp.CompanyAddress = erp.CompanyAddress
				temp.CompanyAddress2 = erp.CompanyAddress2
				temp.CompanyAddress3 = erp.CompanyAddress3
				temp.VatRegistrationNo = erp.VatRegistrationNo
				temp.PhoneNo = erp.PhoneNo
				temp.FaxNo = erp.FaxNo
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterVatBusinessAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterVatBusiness{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					VatBusinessCode:   erp.VatBusinessCode,
					Description:       erp.Description,
					TaxEstablishment:  erp.TaxEstablishment,
					TaxBranchNo:       erp.TaxBranchNo,
					CompanyName:       erp.CompanyName,
					CompanyName2:      erp.CompanyName2,
					CompanyAddress:    erp.CompanyAddress,
					CompanyAddress2:   erp.CompanyAddress2,
					CompanyAddress3:   erp.CompanyAddress3,
					VatRegistrationNo: erp.VatRegistrationNo,
					PhoneNo:           erp.PhoneNo,
					FaxNo:             erp.FaxNo,
					CompanyCode:       erp.CompanyCode,
					IsDeletedByErp:    false,
					LatestSyncDate:    currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterVatBusinessAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterVatBusinessList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
