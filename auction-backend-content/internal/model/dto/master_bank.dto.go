package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterBankPageReqDto struct {
	BankAccountCode *string `json:"bankAccountCode" example:"Buyer"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	IsActive        *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterBankPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterBankDto struct {
	model.BaseDto
	CompanyCode     *string `json:"companyCode" example:"Buyer"`
	BankAccountCode *string `json:"bankAccountCode" example:"Buyer"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	IsActive        bool    `json:"isActive"`
	IsDeletedByErp  bool    `json:"isDeletedByErp"`
}

type MasterBankSyncErpRespDto struct {
	CompanyCode     *string `json:"CompanyCode" example:"Buyer"`
	BankAccountCode *string `json:"Bank_Account_Code" example:"Buyer"`
	DescriptionTh   *string `json:"Description_TH"`
	DescriptionEn   *string `json:"Description_EN"`
	Status          string  `json:"Status"`
}

type MasterBankUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
