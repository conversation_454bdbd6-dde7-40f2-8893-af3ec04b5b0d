package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterCustomerDto struct {
	model.BaseDto
	CompanyCode          *string    `json:"companyCode"`
	CustomerNo           *string    `json:"customerNo"`
	CustomerGroup        *string    `json:"customerGroup"`
	PrefixNameCode       *string    `json:"prefixNameCode"`
	CustomerName         *string    `json:"customerName"`
	VatRegistrationNo    *string    `json:"vatRegistrationNo"`
	TaxBranch            *string    `json:"taxBranch"`
	BlackList            bool       `json:"blackList"`
	BlackListDate        *time.Time `json:"blackListDate"`
	BlackListDescription *string    `json:"blackListDescription"`
	Blocked              bool       `json:"blocked"`
	CustomerType         *string    `json:"customerType"`
	PhoneNo              *string    `json:"phoneNo"`
	SMSPhoneNo           *string    `json:"smsPhoneNo"`
	Email                *string    `json:"email"`
	DayOfBirth           *time.Time `json:"dayOfBirth"`
	CustomerGrade        *string    `json:"customerGrade"`
	CustomerCreditGroup  *string    `json:"customerCreditGroup"`
	TypeOfDelivery       *string    `json:"typeOfDelivery"`
	CustomerAccGroup     *string    `json:"customerAccGroup"`
	RoomNo               *string    `json:"roomNo"`
	Address              *string    `json:"address"`
	Address2             *string    `json:"address2"`
	Building             *string    `json:"building"`
	Floor                *string    `json:"floor"`
	HouseNo              *string    `json:"houseNo"`
	MooNo                *string    `json:"mooNo"`
	Village              *string    `json:"village"`
	Alley                *string    `json:"alley"`
	Street               *string    `json:"street"`
	SubDistrict          *string    `json:"subDistrict"`
	District             *string    `json:"district"`
	Country              *string    `json:"country"`
	City                 *string    `json:"city"`
	PostCode             *string    `json:"postCode"`
	Nationality          *string    `json:"nationality"`
	BidderId             *string    `json:"bidderId"`
	ObjectiveOfAuction   *string    `json:"objectiveOfAuction"`
	IsActive             bool       `json:"isActive"`
	IsDeletedByErp       bool       `json:"isDeletedByErp"`
}

type MasterCustomerPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterCustomerListDto struct {
	MasterCustomerList []MasterCustomerDto `json:"data"`
}

type MasterCustomerSyncErpRespDto struct {
	CompanyCode          *string    `json:"CompanyCode"`
	CustomerNo           *string    `json:"Customer_No"`
	CustomerGroup        *string    `json:"Customer_Group"`
	PrefixNameCode       *string    `json:"Prefix_Name_Code"`
	CustomerName         *string    `json:"Customer_Name"`
	VatRegistrationNo    *string    `json:"Vat_Registration_No"`
	TaxBranch            *string    `json:"Tax_Branch"`
	BlackList            bool       `json:"Black_List"`
	BlackListDate        *time.Time `json:"Black_List_Date"`
	BlackListDescription *string    `json:"Black_List_Description"`
	Blocked              bool       `json:"Blocked"`
	CustomerType         *string    `json:"Customer_Type"`
	PhoneNo              *string    `json:"Phone_No"`
	SMSPhoneNo           *string    `json:"SMS_Phone_No"`
	Email                *string    `json:"Email"`
	DayOfBirth           *time.Time `json:"Day_Of_Birth"`
	CustomerGrade        *string    `json:"Customer_Grade"`
	CustomerCreditGroup  *string    `json:"Customer_Credit_Group"`
	TypeOfDelivery       *string    `json:"Type_Of_Delivery"`
	CustomerAccGroup     *string    `json:"Customer_Acc_Group"`
	RoomNo               *string    `json:"Room_No"`
	Address              *string    `json:"Address"`
	Address2             *string    `json:"Address2"`
	Building             *string    `json:"Building"`
	Floor                *string    `json:"Floor"`
	HouseNo              *string    `json:"House_No"`
	MooNo                *string    `json:"Moo_No"`
	Village              *string    `json:"Village"`
	Alley                *string    `json:"Alley"`
	Street               *string    `json:"Street"`
	SubDistrict          *string    `json:"Sub_District"`
	District             *string    `json:"District"`
	Country              *string    `json:"Country"`
	City                 *string    `json:"City"`
	PostCode             *string    `json:"Post_Code"`
	Nationality          *string    `json:"Nationality"`
	BidderId             *string    `json:"Bidder_Id"`
	ObjectiveOfAuction   *string    `json:"Objective_Of_Auction"`
	Status               string     `json:"Status"`
}

type MasterCustomerPageReqDto struct {
	CustomerNo        *string `json:"customerNo"`
	CustomerName      *string `json:"customerName"`
	CustomerGroup     *string `json:"customerGroup"`
	VatRegistrationNo *string `json:"vatRegistrationNo"`
	PrefixNameCode    *string `json:"prefixNameCode"`
	CustomerType      *string `json:"customerType"`
	PhoneNo           *string `json:"phoneNo"`
	BidderId          *string `json:"bidderId"`
	Nationality       *string `json:"nationality"`
	CustomerGrade     *string `json:"customerGrade"`
	IsActive          *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterCustomerUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
