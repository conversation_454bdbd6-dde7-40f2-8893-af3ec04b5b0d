package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterHolidayPageReqDto struct {
	Date          *time.Time `json:"date"`
	Day           *string    `json:"day"`
	DescriptionTh *string    `json:"descriptionTh"`
	IsActive      *bool      `json:"isActive"`
	model.PagingRequest
}

type MasterHolidayPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterHolidayDto struct {
	model.BaseDto
	CompanyCode    *string    `json:"companyCode" example:"Buyer"`
	Date           *time.Time `json:"date"`
	Day            *string    `json:"day"`
	DescriptionTh  *string    `json:"descriptionTh"`
	IsActive       bool       `json:"isActive"`
	IsDeletedByErp bool       `json:"isDeletedByErp"`
}

type MasterHolidaySyncErpRespDto struct {
	CompanyCode   *string        `json:"CompanyCode"`
	Date          *model.ErpDate `json:"Date"`
	Day           *string        `json:"day"`
	DescriptionTh *string        `json:"Description"`
	Status        string         `json:"Status"`
}

type MasterHolidayUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
