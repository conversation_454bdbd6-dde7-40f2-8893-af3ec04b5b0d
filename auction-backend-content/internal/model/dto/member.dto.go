package dto

import (
	"backend-common-lib/model"
)

type MemberSearchReqDto struct {
	CustomerGroupId *int    `json:"customerGroupId"`
	Username        *string `json:"username"`
	BidderId        *string `json:"bidderId"`
	Name            *string `json:"name"`
	TaxId           *string `json:"taxId"`
	model.PagingRequest
}

type MemberSearchRespDto struct {
	Id            int     `json:"id"`
	BidderId      *string `json:"bidderId"`
	PaddleNo      *string `json:"paddleNo"`
	Username      *string `json:"username"`
	TaxId         *string `json:"taxId"`
	Prefix        *string `json:"prefix"`
	Name          *string `json:"name"`
	CustomerGroup *string `json:"customerGroup"`
	IsActive      bool    `json:"isActive"`
	RegisChannel  *string `json:"regisChannel"`
	GenerateLabel *string `json:"generateLabel"`
	PhoneNumber   *string `json:"phoneNumber"`
	IsBlock       bool    `json:"isBlock"`
	IsBlacklist   bool    `json:"isBlacklist"`
}

type MemberPageRespDto[T any] struct {
	model.PagingModel[T]
}

type UpdateMemberStatusReqDto struct {
	model.BaseDtoActionBy
	IsBlock     *bool   `json:"isBlock"`
	IsBlacklist *bool   `json:"isBlacklist"`
	Reason      *string `json:"reason"`
}
