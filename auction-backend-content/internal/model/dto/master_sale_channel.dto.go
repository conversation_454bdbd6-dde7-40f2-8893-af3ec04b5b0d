package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterSaleChannelDto struct {
	model.BaseDto
	CompanyCode                       *string `json:"companyCode"`
	SaleChannelCode                   *string `json:"saleChannelCode"`
	DescriptionTh                     *string `json:"descriptionTh"`
	DescriptionEn                     *string `json:"descriptionEn"`
	AuctionExcludeValidateReturnBoard bool    `json:"auctionExcludeValidateReturnBoard"`
	AllowRefund                       bool    `json:"allowRefund"`
	IsActive                          bool    `json:"isActive"`
	IsDeletedByErp                    bool    `json:"isDeletedByErp"`
}

type MasterSaleChannelPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterSaleChannelListDto struct {
	MasterSaleChannelList []MasterSaleChannelDto `json:"data"`
}

type MasterSaleChannelSyncErpRespDto struct {
	CompanyCode                       *string `json:"CompanyCode"`
	SaleChannelCode                   *string `json:"Sales_Channel_Code"`
	AuctionExcludeValidateReturnBoard bool    `json:"Auction_Exclude_Validate_Return_Board"`
	AllowRefund                       bool    `json:"Allow_Refund"`
	DescriptionTh                     *string `json:"Description_TH"`
	DescriptionEn                     *string `json:"Description_EN"`
	Status                            string  `json:"Status"`
}

type MasterSaleChannelPageReqDto struct {
	SaleChannelCode *string `json:"saleChannelCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	IsActive        *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterSaleChannelUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
