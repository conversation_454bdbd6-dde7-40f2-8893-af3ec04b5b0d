package dto

import "backend-common-lib/model"

type PaymentDueNotificationDto struct {
	model.BaseDto
	CustomerGroupDesc *string              `json:"customerGroupDesc" example:"VIP"`
	CustomerGroupId   *int                 `json:"customerGroupId" example:"1"`
	DaysBeforeDue     *int                 `json:"daysBeforeDue" example:"7"`
	DaysBeforeDueTh   *string              `json:"daysBeforeDueTh" example:"7 วัน"`
	DaysBeforeDueEn   *string              `json:"daysBeforeDueEn" example:"7 days"`
	ExcludedBidder    []*ExcludedBidderDto `json:"excludedBidder"`
	IsActive          bool                 `json:"isActive"`
}

type ExcludedBidderDto struct {
	BuyerId  int    `json:"buyerId"`
	BidderId string `json:"bidderId"`
}
