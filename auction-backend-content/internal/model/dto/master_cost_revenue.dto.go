package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterCostRevenueDto struct {
	model.BaseDto
	CompanyCode           *string `json:"companyCode"`
	CostRevenueCode       *string `json:"costRevenueCode"`
	DescriptionTh         *string `json:"descriptionTh"`
	DescriptionEn         *string `json:"descriptionEn"`
	ReportingGroup        *string `json:"reportingGroup"`
	IsRefund              bool    `json:"isRefund"`
	IsPriceIncludingVat   bool    `json:"isPriceIncludingVat"`
	VatCode               *string `json:"vatCode"`
	WhtProdCode           *string `json:"whtProdCode"`
	Type                  *string `json:"type"`
	GroupHeader           bool    `json:"groupHeader"`
	Wht3Percent           *int    `json:"wht3Percent"`
	UnitPrice             bool    `json:"unitPrice"`
	ExcludeSendAr         bool    `json:"excludeSendAr"`
	InterfaceGuid         *string `json:"interfaceGuid"`
	InterfaceStatusCreate *string `json:"interfaceStatusCreate"`
	InterfaceStatusUpdate *string `json:"interfaceStatusUpdate"`
	ApiVatCode            *string `json:"apiVatCode"`
	IsActive              bool    `json:"isActive"`
	IsDeletedByErp        bool    `json:"isDeletedByErp"`
}

type MasterCostRevenuePageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterCostRevenueSyncErpRespDto struct {
	CompanyCode           *string `json:"CompanyCode"`
	CostRevenueCode       *string `json:"Cost_Revenue_Code"`
	DescriptionTh         *string `json:"Description_TH"`
	DescriptionEn         *string `json:"Description_EN"`
	ReportingGroup        *string `json:"Reporting_Group"`
	IsRefund              bool    `json:"Refund"`
	IsPriceIncludingVat   bool    `json:"Price_Including_VAT"`
	VatCode               *string `json:"VAT_Code"`
	WhtProdCode           *string `json:"WHT_Prod_Code"`
	Type                  *string `json:"Type"`
	GroupHeader           bool    `json:"Group_Header"`
	Wht3Percent           *int    `json:"WHT_3_Percent"`
	UnitPrice             *int    `json:"Unit_Price"`
	ExcludeSendAr         bool    `json:"Exclude_Send_AR"`
	InterfaceGuid         *string `json:"Interface_GUID"`
	InterfaceStatusCreate *string `json:"Interface_Status_Create"`
	InterfaceStatusUpdate *string `json:"Interface_Status_Update"`
	ApiVatCode            *string `json:"API_VAT_Code"`
	Status                string  `json:"Status"`
}

type MasterCostRevenuePageReqDto struct {
	CostRevenueCode *string `json:"costRevenueCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	VatCode         *string `json:"vatCode"`
	Type            *string `json:"type"`
	IsActive        *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterCostRevenueUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
