package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterAuctionTypeDto struct {
	model.BaseDto
	Code           *int    `json:"code" example:"1"`
	Name           *string `json:"name" example:"Buyer"`
	Description    *string `json:"description" example:"Buyer"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterAuctionTypeListDto struct {
	MasterAuctionTypeList []MasterAuctionTypeDto `json:"MasterAuctionTypeList"`
}

type MasterAuctionTypePageReqDto struct {
	model.PagingRequest
	Code        *int    `json:"code" example:"1"`
	Name        *string `json:"name" example:"Buyer"`
	Description *string `json:"description" example:"Buyer"`
	IsActive    *bool   `json:"isActive"`
}

type MasterAuctionTypePageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterAuctionTypeSyncErpRespDto struct {
	Code        *int    `json:"code" example:"1"`
	Name        *string `json:"name" example:"Buyer"`
	Description *string `json:"description" example:"Buyer"`
	Status      string  `json:"status" example:"success"`
}

type MasterAuctionTypeSyncErpReqDto struct {
	Code        *int    `json:"Code" example:"1"`
	Name        *string `json:"Name" example:"Buyer"`
	Description *string `json:"Description" example:"Buyer"`
	Status      string  `json:"Status" example:"success"`
}

type MasterAuctionTypeUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
