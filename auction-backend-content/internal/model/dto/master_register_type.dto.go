package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterRegisterTypeDto struct {
	model.BaseDto
	CompanyCode    *string `json:"companyCode"`
	AttributeCode  *string `json:"attributeCode"`
	AttributeName  *string `json:"attributeName"`
	LineNo         *int    `json:"lineNo"`
	OptionTh       *string `json:"optionTH"`
	OptionEn       *string `json:"optionEN"`
	Status         string  `json:"status"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterRegisterTypePageReqDto struct {
	LineNo   *int    `json:"lineNo"`
	OptionTh *string `json:"optionTh"`
	OptionEn *string `json:"optionEn"`
	IsActive *bool   `json:"isActive"`
	Status   *string `json:"status"`
	model.PagingRequest
}

type MasterRegisterTypePageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterRegisterTypeSyncErpRespDto struct {
	CompanyCode   *string `json:"CompanyCode"`
	AttributeCode *string `json:"Attribute_Code"`
	AttributeName *string `json:"Attribute_Name"`
	LineNo        *int    `json:"Line_No"`
	OptionTh      *string `json:"Option_TH"`
	OptionEn      *string `json:"Option_EN"`
	Status        string  `json:"Status"`
}

type MasterRegisterTypeUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
