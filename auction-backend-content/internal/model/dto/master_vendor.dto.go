package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterVendorDto struct {
	model.BaseDto
	PrefixNameCode    *string `json:"prefixNameCode"`
	VendorNo          *string `json:"vendorNo"`
	VendorName        *string `json:"vendorName"`
	VendorGroup       *string `json:"vendorGroup"`
	VatRegistrationNo *string `json:"vatRegistrationNo"`
	IsActive          bool    `json:"isActive"`
	IsDeletedByErp    bool    `json:"isDeletedByErp"`
}

type MasterVendorPageReqDto struct {
	PrefixNameCode    *string `json:"prefixNameCode"`
	VendorNo          *string `json:"vendorNo"`
	VendorName        *string `json:"vendorName"`
	VendorGroup       *string `json:"vendorGroup"`
	IsActive          *bool   `json:"isActive"`
	VatRegistrationNo *string `json:"vatRegistrationNo"`
	model.PagingRequest
}

type MasterVendorPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate"`
}

type VendorListDto struct {
	VendorList []MasterVendorDto `json:"data"`
}

type VendorDropdownDto struct {
	Id          int    `json:"id"`
	VendorNo    string `json:"vendorNo"`
	VendorName  string `json:"vendorName"`
	VendorGroup string `json:"vendorGroup"`
}

type MasterVendorSyncErpRespDto struct {
	PrefixNameCode    *string `json:"Prefix_Name_Code"`
	VendorNo          *string `json:"Vendor_No"`
	VendorName        *string `json:"Vendor_Name"`
	VendorGroup       *string `json:"Vendor_Group"`
	VatRegistrationNo *string `json:"Vat_Registration_No"`
	Status            string  `json:"Status"`
}

type MasterVendorUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
