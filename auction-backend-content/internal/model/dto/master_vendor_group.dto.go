package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterVendorGroupDto struct {
	model.BaseDto
	VendorGroupCode *string `json:"vendorGroupCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	IsActive        bool    `json:"isActive"`
	IsDeletedByErp  bool    `json:"isDeletedByErp"`
}

type MasterVendorGroupPageReqDto struct {
	VendorGroupCode *string `json:"vendorGroupCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	IsActive        *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterVendorGroupPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate"`
}

type MasterVendorGroupListDto struct {
	MasterVendorGroupList []MasterVendorGroupDto `json:"data"`
}

type MasterVendorGroupSyncErpRespDto struct {
	VendorGroupCode *string `json:"Vendor_Group_Code"`
	DescriptionTh   *string `json:"Description_TH"`
	DescriptionEn   *string `json:"Description_EN"`
	Status          string  `json:"Status"`
}

type MasterVendorGroupUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
