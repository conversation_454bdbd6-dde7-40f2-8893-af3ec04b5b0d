package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterAssetLocationFloorDto struct {
	model.BaseDto
	LocationFloorCode *string `json:"locationFloorCode"`
	Floor             *string `json:"floor"`
	DescriptionTH     *string `json:"descriptionTh"`
	DescriptionEN     *string `json:"descriptionEn"`
	BranchCode        *string `json:"branchCode"`
	IsActive          bool    `json:"isActive"`
	IsDeletedByErp    bool    `json:"isDeletedByErp"`
}

type MasterAssetLocationFloorPageReqDto struct {
	LocationFloorCode *string `json:"locationFloorCode"`
	Floor             *string `json:"floor"`
	DescriptionTh     *string `json:"descriptionTh"`
	DescriptionEn     *string `json:"descriptionEn"`
	BranchCode        *string `json:"branchCode"`
	IsActive          *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterAssetLocationFloorPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate"`
}

type MasterAssetLocationFloorSyncErpRespDto struct {
	CompanyCode       *string `json:"CompanyCode"`
	LocationFloorCode *string `json:"Location_Code"`
	Floor             *string `json:"Floor"`
	DescriptionTh     *string `json:"Description_TH"`
	DescriptionEn     *string `json:"Description_EN"`
	BranchCode        *string `json:"Branch_Code"`
	Status            string  `json:"Status"`
}

type MasterAssetLocationFloorUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
