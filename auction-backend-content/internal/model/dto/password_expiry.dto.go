package dto

import (
	"backend-common-lib/model"
)

type PasswordExpiryDto struct {
	model.BaseDto
	UserType               *string `json:"userType"`
	PasswordExpiryDays     *int    `json:"passwordExpiryDays"`
	PinExpiryDays          *int    `json:"pinExpiryDays"`
	NotifyBeforeExpiryDays *int    `json:"notifyBeforeExpiryDays"`
}

type PasswordExpiryReqDto struct {
	PasswordExpiryDto
	ActionBy *int `json:"actionBy"`
}
