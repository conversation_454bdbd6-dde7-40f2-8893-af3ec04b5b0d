package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterSubDistrictDto struct {
	model.BaseDto
	RegionCode      *string `json:"regionCode"`
	CityCode        *string `json:"cityCode"`
	DistrictCode    *string `json:"districtCode"`
	SubDistrictCode *string `json:"subDistrictCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	IsActive        bool    `json:"isActive"`
	IsDeletedByErp  bool    `json:"isDeletedByErp"`
}

type MasterSubDistrictPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterSubDistrictListDto struct {
	MasterSubDistrictList []MasterSubDistrictDto `json:"data"`
}

type MasterSubDistrictSyncErpRespDto struct {
	RegionCode      *string `json:"Region_Code"`
	CityCode        *string `json:"City_Code"`
	DistrictCode    *string `json:"District"`
	SubDistrictCode *string `json:"Sub_District"`
	DescriptionTh   *string `json:"Description_TH"`
	DescriptionEn   *string `json:"Description_EN"`
	Status          string  `json:"Status"`
}

type MasterSubDistrictPageReqDto struct {
	RegionCode      *string `json:"regionCode"`
	CityCode        *string `json:"cityCode"`
	DistrictCode    *string `json:"districtCode"`
	SubDistrictCode *string `json:"subDistrictCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	IsActive        *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterSubDistrictUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
