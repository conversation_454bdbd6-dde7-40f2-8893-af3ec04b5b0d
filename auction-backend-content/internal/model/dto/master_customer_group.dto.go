package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterCustomerGroupDto struct {
	model.BaseDto
	CustomerGroupCode *string `json:"customerGroupCode"`
	DescriptionTh     *string `json:"descriptionTh"`
	IsActive          bool    `json:"isActive"`
	IsDeletedByErp    bool    `json:"isDeletedByErp"`
}

type MasterCustomerGroupPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterCustomerGroupListDto struct {
	MasterCustomerGroupList []MasterCustomerGroupDto `json:"data"`
}

type MasterCustomerGroupSyncErpRespDto struct {
	CustomerGroupCode *string `json:"Customer_Group_Code"`
	Description       *string `json:"Description"`
	Status            string  `json:"Status"`
}

type MasterCustomerGroupPageReqDto struct {
	CustomerGroupCode *string `json:"customerGroupCode"`
	DescriptionTh     *string `json:"descriptionTh"`
	IsActive          *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterCustomerGroupUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
