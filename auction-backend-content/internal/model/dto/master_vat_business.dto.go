package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterVatBusinessDto struct {
	model.BaseDto
	CompanyCode       *string `json:"companyCode"`
	VatBusinessCode   *string `json:"vatBusinessCode"`
	Description       *string `json:"description"`
	TaxEstablishment  *string `json:"taxEstablishment"`
	TaxBranchNo       *string `json:"taxBranchNo"`
	CompanyName       *string `json:"companyName"`
	CompanyName2      *string `json:"companyName2"`
	CompanyAddress    *string `json:"companyAddress"`
	CompanyAddress2   *string `json:"companyAddress2"`
	CompanyAddress3   *string `json:"companyAddress3"`
	VatRegistrationNo *string `json:"vatRegistrationNo"`
	PhoneNo           *string `json:"phoneNo"`
	FaxNo             *string `json:"faxNo"`
	IsActive          bool    `json:"isActive"`
	IsDeletedByErp    bool    `json:"isDeletedByErp"`
}

type MasterVatBusinessListDto struct {
	MasterVatBusinessList []MasterVatBusinessDto `json:"data"`
}

type MasterVatBusinessPageReqDto struct {
	VatBusinessCode  *string `json:"vatBusinessCode"`
	Description      *string `json:"description"`
	TaxEstablishment *string `json:"taxEstablishment"`
	IsActive         *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterVatBusinessPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterVatBusinessSyncErpRespDto struct {
	CompanyCode       *string `json:"CompanyCode"`
	VatBusinessCode   *string `json:"Code"`
	Description       *string `json:"Description"`
	TaxEstablishment  *string `json:"Tax_Establishment"`
	TaxBranchNo       *string `json:"Tax_Branch_No"`
	CompanyName       *string `json:"Company_Name"`
	CompanyName2      *string `json:"Company_Name_2"`
	CompanyAddress    *string `json:"Company_Address"`
	CompanyAddress2   *string `json:"Company_Address_2"`
	CompanyAddress3   *string `json:"Company_Address_3"`
	VatRegistrationNo *string `json:"VAT_Registration_No"`
	PhoneNo           *string `json:"Phone_No"`
	FaxNo             *string `json:"Fax_No"`
	Status            string  `json:"Status"`
}

type MasterVatBusinessUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
