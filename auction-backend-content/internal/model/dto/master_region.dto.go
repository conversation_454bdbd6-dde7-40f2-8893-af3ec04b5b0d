package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterRegionPageReqDto struct {
	RegionCode    *string `json:"regionCode"`
	CountryCode   *string `json:"countryCode"`
	DescriptionTh *string `json:"descriptionTh"`
	DescriptionEn *string `json:"descriptionEn"`
	IsActive      *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterRegionPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterRegionDto struct {
	model.BaseDto
	CompanyCode    *string `json:"companyCode"`
	RegionCode     *string `json:"regionCode"`
	CountryCode    *string `json:"countryCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterRegionSyncErpRespDto struct {
	CompanyCode   *string `json:"CompanyCode"`
	RegionCode    *string `json:"Region_Code"`
	CountryCode   *string `json:"Country_Code"`
	DescriptionTh *string `json:"Description_TH"`
	DescriptionEn *string `json:"Description_EN"`
	Status        string  `json:"Status"`
}

type MasterRegionUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
