package dto

import (
	"backend-common-lib/model"

	"time"
)

type MasterCityDto struct {
	model.BaseDto
	CityCode       *string `json:"cityCode"`
	CountryCode    *string `json:"companyCode"`
	RegionCode     *string `json:"regionCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	Initial        *string `json:"initial"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterCityPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterCityPageReqDto struct {
	CityCode      *string `json:"cityCode"`
	RegionCode    *string `json:"regionCode"`
	DescriptionTh *string `json:"descriptionTh"`
	DescriptionEn *string `json:"descriptionEn"`
	Initial       *string `json:"initial"`
	IsActive      *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterCitySyncErpRespDto struct {
	CountryCode   *string `json:"Country_Code"`
	RegionCode    *string `json:"Region_Code"`
	CityCode      *string `json:"City_Code"`
	DescriptionTh *string `json:"Description_TH"`
	DescriptionEn *string `json:"Description_EN"`
	Initial       *string `json:"Initial_Name"`
	Status        string  `json:"Status"`
}

type MasterCityUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
