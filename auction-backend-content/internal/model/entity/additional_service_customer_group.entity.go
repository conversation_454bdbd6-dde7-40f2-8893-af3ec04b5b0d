package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type AdditionalServiceCustomerGroup struct {
	ID                  *int                        `column:"id" json:"id"`
	AdditionalServiceId *int                        `column:"additional_service_id" json:"additionalServiceId"`
	CustomerGroupId     *int                        `column:"customer_group_id" json:"customerGroupId"`
	DeletedDate         *gorm.DeletedAt             `gorm:"column:deleted_date" json:"deletedDate"`
	CustomerGroup       *model.CustomerGroupForJoin `gorm:"foreignKey:CustomerGroupId"`
}

func (AdditionalServiceCustomerGroup) TableName() string {
	return "additional_service_customer_group"
}
