package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type PaymentDueNotificationExcludedBuyer struct {
	ID                       int                 `column:"id" json:"id" example:"1"`
	PaymentDueNotificationId int                 `column:"payment_due_notification_id" json:"paymentDueNotificationId" example:"1"`
	BuyerId                  int                 `column:"buyer_id" json:"buyerId" example:"1"`
	DeletedAt                *gorm.DeletedAt     `gorm:"column:deleted_at" json:"deletedAt"`
	Buyer                    *model.BuyerForJoin `gorm:"foreignKey:BuyerId"`
}

func (PaymentDueNotificationExcludedBuyer) TableName() string {
	return "payment_due_notification_buyer"
}
