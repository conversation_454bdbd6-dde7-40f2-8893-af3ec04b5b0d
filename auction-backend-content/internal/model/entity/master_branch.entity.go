package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterBranch struct {
	*model.BaseEntity
	BranchCode        *string                `column:"branch_code" json:"branchCode"`
	DescriptionTh     *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn     *string                `column:"description_en" json:"descriptionEn"`
	CountryCode       *string                `column:"country_code" json:"countryCode"`
	RegionCode        *string                `column:"region_code" json:"regionCode"`
	CityCode          *string                `column:"city_code" json:"cityCode"`
	PostCode          *string                `column:"post_code" json:"postCode"`
	VATBusinessCode   *string                `column:"vat_business_code" json:"vatBusinessCode"`
	AssetLocationCode *string                `column:"asset_location_code" json:"assetLocationCode"`
	IsActive          bool                   `column:"is_active" json:"isActive"`
	CompanyCode       *string                `column:"company_code" json:"companyCode"`
	IsDeletedByErp    bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate    *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser       *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser       *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterBranch) TableName() string {
	return "master_branch"
}
