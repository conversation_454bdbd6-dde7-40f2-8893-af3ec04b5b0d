package entity

import (
	"backend-common-lib/model"
	"time"
)

type AuctionAsset struct {
	*model.BaseEntity
	AuctionID          int        `json:"auctionId" db:"auction_id" example:"101"`
	AssetTypeID        int        `json:"assetTypeId" db:"asset_type_id" example:"1"`
	AssetGroupID       *int       `json:"assetGroupId" db:"asset_group_id" example:"5"` // Nullable int
	IsDeposit          bool       `json:"isDeposit" db:"is_deposit" example:"true"`
	IsCredit           bool       `json:"isCredit" db:"is_credit" example:"false"`
	IsAdditional       bool       `json:"isAdditional" db:"is_additional" example:"false"`
	IsItemLimit        bool       `json:"isItemLimit" db:"is_item_limit" example:"true"`
	IncrementalUnit    string     `json:"incrementalUnit" db:"incremental_unit" example:"unit"`
	ReferenceIncrement string     `json:"referenceIncrement" db:"reference_increment" example:"REF-XYZ"`
	DeletedDate        *time.Time `json:"deletedDate" db:"deleted_date"` // Nullable timestamp, typically for soft deletes
}

func (AuctionAsset) TableName() string {
	return "auction_asset"
}
