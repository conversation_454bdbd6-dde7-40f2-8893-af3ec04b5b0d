package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type Auction struct {
	*model.BaseEntity
	AuctionTypeCode        string              `column:"auction_type_code" json:"auctionTypeCode" example:"0"`
	AuctionName            string              `column:"auction_name" json:"auctionName" example:"<PERSON>"`
	Description            string              `column:"description" json:"description" example:"JohnDoe"`
	IsActive               *bool               `column:"is_active" json:"isActive" example:"true"`
	StartDate              time.Time           `column:"start_date" json:"startDate" example:"01/01/2020"`
	EndDate                time.Time           `column:"end_date" json:"endDate" example:"01/01/2020"`
	EventId                int                 `column:"event_id" json:"eventId"`
	IsAutoCollateralRefund bool                `column:"is_auto_collateral_refund" json:"isAutoCollateralRefund"`
	DeletedDate            *gorm.DeletedAt     `column:"deleted_date" json:"deletedDate"`
	Event                  *model.EventForJoin `gorm:"foreignKey:EventId;references:ID"`
}

func (Auction) TableName() string {
	return "auction"
}
