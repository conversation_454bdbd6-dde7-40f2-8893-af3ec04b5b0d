package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterSellerOffer struct {
	*model.BaseEntity
	SellerOfferCode *string                `column:"seller_offer_code" json:"sellerOfferCode"`
	DescriptionTh   *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn   *string                `column:"description_en" json:"descriptionEn"`
	Split           bool                   `column:"split" json:"split"`
	Invoice1        *int                   `column:"invoice1" json:"invoice1"`
	Invoice2        *int                   `column:"invoice2" json:"invoice2"`
	VendorCode      *string                `column:"vendor_code" json:"vendorCode"`
	IsActive        bool                   `column:"is_active" json:"isActive"`
	CompanyCode     *string                `column:"company_code" json:"companyCode"`
	IsDeletedByErp  bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate  *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser     *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser     *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterSellerOffer) TableName() string {
	return "master_seller_offer"
}
