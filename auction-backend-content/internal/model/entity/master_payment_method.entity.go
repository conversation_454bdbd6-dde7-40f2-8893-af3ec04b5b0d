package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterPaymentMethod struct {
	*model.BaseEntity
	CompanyCode               *string                `column:"company_code" json:"companyCode" example:"Buyer"`
	PaymentMethodCode         *string                `column:"event_code" json:"eventCode" example:"Buyer"`
	DescriptionTh             *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn             *string                `column:"description_en" json:"descriptionEn"`
	Type                      *string                `column:"type" json:"type"`
	IsExcludeCalSellerPayment bool                   `column:"is_exclude_cal_seller_payment" json:"isExcludeCalSellerPayment"`
	IsActive                  bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp            bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate            *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser               *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser               *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterPaymentMethod) TableName() string {
	return "master_payment_method"
}
