package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterHoliday struct {
	*model.BaseEntity
	CompanyCode    *string                `column:"company_code" json:"companyCode"`
	Date           *time.Time             `column:"date" json:"date"`
	Day            *string                `column:"day" json:"day"`
	DescriptionTh  *string                `column:"description_th" json:"descriptionTh"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterHoliday) TableName() string {
	return "master_holiday"
}
