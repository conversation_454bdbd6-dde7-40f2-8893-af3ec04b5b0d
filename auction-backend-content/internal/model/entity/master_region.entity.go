package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterRegion struct {
	*model.BaseEntity
	CountryCode    *string                `column:"country_code" json:"countryCode"`
	RegionCode     *string                `column:"region_code" json:"regionCode"`
	DescriptionTh  *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn  *string                `column:"description_en" json:"descriptionEn"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	CompanyCode    *string                `column:"company_code" json:"companyCode"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterRegion) TableName() string {
	return "master_region"
}
