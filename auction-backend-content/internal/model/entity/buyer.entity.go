package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type Buyer struct {
	*model.BaseEntity
	Username             *string                     `column:"username" json:"username"`
	KeyPwd               *string                     `column:"key_pwd" json:"key_pwd"`
	PasswordHash         *string                     `column:"password_hash" json:"password_hash"`
	RoleID               *int                        `column:"role_id" json:"role_id"`
	FirstName            *string                     `column:"first_name" json:"firstname"`
	LastName             *string                     `column:"last_name" json:"lastname"`
	BidderId             *string                     `column:"bidder_id" json:"bidder_id"`
	LoginFailCount       *int                        `column:"login_fail_count" json:"login_fail_count"`
	LatestLogin          *time.Time                  `column:"latest_login" json:"latest_login" example:"2020-01-01"`
	CustomerGroupId      *int                        `column:"customer_group_id" json:"customerGroupId"`
	PrefixNameId         int                         `column:"prefix_name_id" json:"prefixNameId"`
	MiddleName           *string                     `column:"middle_name" json:"middleName"`
	PhoneNumber          string                      `column:"phone_number" json:"phoneNumber"`
	TaxId                *string                     `column:"tax_id" json:"taxId"`
	PaddleNo             *string                     `column:"paddle_no" json:"paddleNo"`
	RegisChannel         *string                     `column:"regis_channel" json:"regisChannel"`
	GenerateLabel        *string                     `column:"generate_label" json:"generateLabel"`
	IsActive             bool                        `column:"is_active" json:"isActive"`
	IsBlock              bool                        `column:"is_block" json:"isBlock"`
	IsBlacklist          bool                        `column:"is_blacklist" json:"isBlacklist"`
	Reason               *string                     `column:"reason" json:"reason"`
	DeletedDate          *gorm.DeletedAt             `column:"deleted_date" json:"deletedDate"`
	CustomerGroupForJoin *model.CustomerGroupForJoin `gorm:"foreignKey:CustomerGroupId;references:ID"`
	Prefix               *string                     `column:"prefix" json:"prefix"`
}

func (Buyer) TableName() string {
	return "buyer"
}

type BuyerForJoin struct {
	ID       int     `gorm:"primaryKey" column:"id" json:"id"`
	BidderId *string `column:"bidder_id" json:"bidderId" example:"1"`
}

func (BuyerForJoin) TableName() string {
	return "buyer"
}
