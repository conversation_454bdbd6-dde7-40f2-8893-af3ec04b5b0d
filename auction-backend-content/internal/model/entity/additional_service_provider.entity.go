package entity

import (
	"gorm.io/gorm"
)

type AdditionalServiceProvider struct {
	ID                  *int            `column:"id" json:"id"`
	AdditionalServiceId *int            `column:"additional_service_id" json:"additionalServiceId"`
	ProviderName        *string         `column:"provider_name" json:"providerName"`
	ProviderLink        *string         `column:"provider_link" json:"providerLink"`
	DeletedDate         *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
}

func (AdditionalServiceProvider) TableName() string {
	return "additional_service_provider"
}
