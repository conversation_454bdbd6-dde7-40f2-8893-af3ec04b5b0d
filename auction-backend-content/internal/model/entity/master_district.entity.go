package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterDistrict struct {
	*model.BaseEntity
	RegionCode     *string                `column:"region_code" json:"regionCode"`
	CityCode       *string                `column:"city_code" json:"cityCode"`
	DistrictCode   *string                `column:"district_code" json:"districtCode" ignore:"true"`
	DescriptionTh  *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn  *string                `column:"description_en" json:"descriptionEn"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp" ignore:"true"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate" ignore:"true"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID"`
}

func (MasterDistrict) TableName() string {
	return "master_district"
}
