package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterCustomerTypeRepositoryImpl) buildMasterCustomerTypeQuery(req dto.MasterCustomerTypePageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterCustomerType{})
	query = util.JoinUsers("master_customer_type")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_customer_type")
	}
	return query
}

func (r *masterCustomerTypeRepositoryImpl) FindMasterCustomerTypeWithFilter(req dto.MasterCustomerTypePageReqDto) ([]entity.MasterCustomerType, error) {
	var result []entity.MasterCustomerType

	query := r.buildMasterCustomerTypeQuery(req)
	query.Order("customer_type_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil

}

func (r *masterCustomerTypeRepositoryImpl) CountMasterCustomerTypeWithFilter(req dto.MasterCustomerTypePageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterCustomerTypeQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterCustomerTypeRepositoryImpl) FindMasterCustomerTypeLatestSyncDate() (*time.Time, error) {
	var result entity.MasterCustomerType
	err := r.DB.
		Model(&entity.MasterCustomerType{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterCustomerTypeRepositoryImpl) UpdatesMasterCustomerTypeFieldsWhere(field map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterCustomerType{}).Where(whereClause, args...).Updates(field)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterCustomerTypeRepositoryImpl) FindMasterCustomerTypeAll() ([]entity.MasterCustomerType, error) {
	var result []entity.MasterCustomerType
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterCustomerTypeRepositoryImpl) UpdateMasterCustomerTypeAllFields(e *entity.MasterCustomerType) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCustomerTypeRepositoryImpl) InsertMasterCustomerTypeList(data []entity.MasterCustomerType) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCustomerTypeRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
