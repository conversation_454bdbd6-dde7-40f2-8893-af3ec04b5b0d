package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterAssetTypeRepositoryImpl) buildMasterAssetTypeQuery(req dto.MasterAssetTypePageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterAssetType{})
	query = util.JoinUsers("master_asset_type")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_asset_type")
	}
	return query
}

func (r *masterAssetTypeRepositoryImpl) FindMasterAssetTypeWithFilter(req dto.MasterAssetTypePageReqDto) ([]entity.MasterAssetType, error) {
	var results []entity.MasterAssetType

	query := r.buildMasterAssetTypeQuery(req)
	query.Order("asset_type_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterAssetTypeRepositoryImpl) CountMasterAssetTypeWithFilter(req dto.MasterAssetTypePageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterAssetTypeQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterAssetTypeRepositoryImpl) FindMasterAssetTypeLatestSyncDate() (*time.Time, error) {
	var result entity.MasterAssetType
	err := r.DB.
		Model(&entity.MasterAssetType{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterAssetTypeRepositoryImpl) UpdatesMasterAssetTypeFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterAssetType{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterAssetTypeRepositoryImpl) FindMasterAssetTypeAll() ([]entity.MasterAssetType, error) {
	var result []entity.MasterAssetType
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterAssetTypeRepositoryImpl) UpdateMasterAssetTypeAllFields(e *entity.MasterAssetType) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterAssetTypeRepositoryImpl) InsertMasterAssetTypeList(data []entity.MasterAssetType) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterAssetTypeRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
