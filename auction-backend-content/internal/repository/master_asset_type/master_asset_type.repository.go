package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterAssetTypeRepositoryImpl struct {
	DB *gorm.DB
}

type MasterAssetTypeRepository interface {
	FindMasterAssetTypeWithFilter(req dto.MasterAssetTypePageReqDto) ([]entity.MasterAssetType, error)
	CountMasterAssetTypeWithFilter(req dto.MasterAssetTypePageReqDto) (int64, error)
	FindMasterAssetTypeLatestSyncDate() (*time.Time, error)
	UpdatesMasterAssetTypeFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterAssetTypeAll() ([]entity.MasterAssetType, error)
	UpdateMasterAssetTypeAllFields(e *entity.MasterAssetType) error
	InsertMasterAssetTypeList(data []entity.MasterAssetType) error

	GetDB() *gorm.DB
}

func NewMasterAssetTypeRepository(db *gorm.DB) MasterAssetTypeRepository {
	return &masterAssetTypeRepositoryImpl{DB: db}
}
