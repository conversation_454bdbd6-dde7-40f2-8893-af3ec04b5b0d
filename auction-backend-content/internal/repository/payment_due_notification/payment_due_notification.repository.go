package repository

import (
	"backend-common-lib/model"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type paymentDueNotificationRepositoryImpl struct {
	DB *gorm.DB
}

type PaymentDueNotificationRepository interface {
	FindAllPaymentDueNotification(req model.PagingRequest) ([]entity.PaymentDueNotification, error)
	CountAllPaymentDueNotification() (int64, error)

	FindPaymentDueNotificationByID(id int) (*entity.PaymentDueNotification, error)

	InsertPaymentDueNotification(entityPaymentDue *entity.PaymentDueNotification) error
	FindByCustomerGroupId(customerGroupId int) ([]entity.PaymentDueNotification, error)

	UpdatePaymentDueNotification(id int, fieldToUpdate map[string]interface{}) (int64, error)

	DeletePaymentDueNotification(id int) (int64, error)

	GetDB() *gorm.DB
}

func NewPaymentDueNotificationRepository(db *gorm.DB) PaymentDueNotificationRepository {
	return &paymentDueNotificationRepositoryImpl{DB: db}
}
