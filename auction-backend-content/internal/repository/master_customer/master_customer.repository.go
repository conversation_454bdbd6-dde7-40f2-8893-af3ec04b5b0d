package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterCustomerRepositoryImpl struct {
	DB *gorm.DB
}

type MasterCustomerRepository interface {
	FindMasterCustomerWithFilter(req dto.MasterCustomerPageReqDto) ([]entity.MasterCustomer, error)
	CountMasterCustomerWithFilter(req dto.MasterCustomerPageReqDto) (int64, error)
	FindMasterCustomerLatestSyncDate() (*time.Time, error)
	UpdatesMasterCustomerFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterCustomerAll() ([]entity.MasterCustomer, error)
	UpdateMasterCustomerAllFields(e *entity.MasterCustomer) error
	InsertMasterCustomerListWithBatches(data []entity.MasterCustomer) error

	GetDB() *gorm.DB
}

func NewMasterCustomerRepository(db *gorm.DB) MasterCustomerRepository {
	return &masterCustomerRepositoryImpl{DB: db}
}
