package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterSaleChannelRepositoryImpl) buildMasterSaleChannelQuery(req dto.MasterSaleChannelPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterSaleChannel{})
	query = util.JoinUsers("master_sale_channel")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_sale_channel")
	}
	return query
}

func (r *masterSaleChannelRepositoryImpl) FindMasterSaleChannelWithFilter(req dto.MasterSaleChannelPageReqDto) ([]entity.MasterSaleChannel, error) {
	var results []entity.MasterSaleChannel

	query := r.buildMasterSaleChannelQuery(req)
	query.Order("sale_channel_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterSaleChannelRepositoryImpl) CountMasterSaleChannelWithFilter(req dto.MasterSaleChannelPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterSaleChannelQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterSaleChannelRepositoryImpl) FindMasterSaleChannelLatestSyncDate() (*time.Time, error) {
	var result entity.MasterSaleChannel
	err := r.DB.
		Model(&entity.MasterSaleChannel{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterSaleChannelRepositoryImpl) UpdatesMasterSaleChannelFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterSaleChannel{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterSaleChannelRepositoryImpl) FindMasterSaleChannelAll() ([]entity.MasterSaleChannel, error) {
	var result []entity.MasterSaleChannel
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterSaleChannelRepositoryImpl) UpdateMasterSaleChannelAllFields(e *entity.MasterSaleChannel) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterSaleChannelRepositoryImpl) InsertMasterSaleChannelList(data []entity.MasterSaleChannel) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterSaleChannelRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
