package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterSaleChannelRepositoryImpl struct {
	DB *gorm.DB
}

type MasterSaleChannelRepository interface {
	FindMasterSaleChannelWithFilter(req dto.MasterSaleChannelPageReqDto) ([]entity.MasterSaleChannel, error)
	CountMasterSaleChannelWithFilter(req dto.MasterSaleChannelPageReqDto) (int64, error)
	FindMasterSaleChannelLatestSyncDate() (*time.Time, error)
	UpdatesMasterSaleChannelFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterSaleChannelAll() ([]entity.MasterSaleChannel, error)
	UpdateMasterSaleChannelAllFields(e *entity.MasterSaleChannel) error
	InsertMasterSaleChannelList(data []entity.MasterSaleChannel) error

	GetDB() *gorm.DB
}

func NewMasterSaleChannelRepository(db *gorm.DB) MasterSaleChannelRepository {
	return &masterSaleChannelRepositoryImpl{DB: db}
}
