package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterVendorRepositoryImpl) buildMasterVendorQuery(req dto.MasterVendorPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterVendor{})
	query = util.JoinUsers("master_vendor")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_vendor")
	}
	return query
}

func (r *masterVendorRepositoryImpl) FindMasterVendorWithFilter(req dto.MasterVendorPageReqDto) ([]entity.MasterVendor, error) {
	var results []entity.MasterVendor

	query := r.buildMasterVendorQuery(req)
	query.Order("vendor_no asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterVendorRepositoryImpl) CountMasterVendorWithFilter(req dto.MasterVendorPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterVendorQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterVendorRepositoryImpl) FindMasterVendorLatestSyncDate() (*time.Time, error) {
	var result entity.MasterVendor
	err := r.DB.
		Model(&entity.MasterVendor{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterVendorRepositoryImpl) UpdatesMasterVendorFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterVendor{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterVendorRepositoryImpl) FindMasterVendorAll() ([]entity.MasterVendor, error) {
	var result []entity.MasterVendor
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterVendorRepositoryImpl) UpdateMasterVendorAllFields(e *entity.MasterVendor) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterVendorRepositoryImpl) InsertMasterVendorList(data []entity.MasterVendor) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterVendorRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
