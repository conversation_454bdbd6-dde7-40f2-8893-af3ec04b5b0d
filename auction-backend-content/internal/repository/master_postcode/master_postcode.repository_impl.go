package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterPostcodeRepositoryImpl) buildMasterPostcodeQuery(req dto.MasterPostcodePageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterPostcode{})
	query = util.JoinUsers("master_post_code")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_post_code")

	}
	return query
}

func (r *masterPostcodeRepositoryImpl) FindMasterPostcodeWithFilter(req dto.MasterPostcodePageReqDto) ([]entity.MasterPostcode, error) {
	var results []entity.MasterPostcode

	query := r.buildMasterPostcodeQuery(req)
	query = query.Order("region_code asc,city_code asc,district_code asc,sub_district_code asc,post_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterPostcodeRepositoryImpl) CountMasterPostcodeWithFilter(req dto.MasterPostcodePageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterPostcodeQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterPostcodeRepositoryImpl) FindLatestSyncDate() (*time.Time, error) {
	var result entity.MasterPostcode
	err := r.DB.
		Model(&entity.MasterPostcode{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterPostcodeRepositoryImpl) UpdatesMasterPostcodeFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterPostcode{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterPostcodeRepositoryImpl) FindMasterPostcodeAll() ([]entity.MasterPostcode, error) {
	var result []entity.MasterPostcode
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterPostcodeRepositoryImpl) UpdateMasterPostcodeAllFields(e *entity.MasterPostcode) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterPostcodeRepositoryImpl) InsertMasterPostcodeListWithBatches(data []entity.MasterPostcode) error {
	if err := r.DB.CreateInBatches(&data, 500).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterPostcodeRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
