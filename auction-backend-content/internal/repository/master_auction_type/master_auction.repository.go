package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterAuctionTypeRepositoryImpl struct {
	DB *gorm.DB
}

type MasterAuctionTypeRepository interface {
	FindMasterAuctionTypeWithFilter(req dto.MasterAuctionTypePageReqDto) ([]entity.MasterAuctionType, error)
	CountMasterAuctionTypeWithFilter(req dto.MasterAuctionTypePageReqDto) (int64, error)
	FindLatestSyncDate() (*time.Time, error)

	UpdatesMasterAuctionTypeFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)

	FindMasterAuctionTypeAll() ([]entity.MasterAuctionType, error)
	UpdateMasterAuctionTypeAllFields(e *entity.MasterAuctionType) error
	InsertMasterAuctionTypeList(data []entity.MasterAuctionType) error

	GetDB() *gorm.DB
	FindById(id int) (*entity.MasterAuctionType, error)
}

func NewMasterAuctionTypeRepository(db *gorm.DB) MasterAuctionTypeRepository {
	return &masterAuctionTypeRepositoryImpl{DB: db}
}
