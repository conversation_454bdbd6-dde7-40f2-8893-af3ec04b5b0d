package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterRegionRepositoryImpl) buildMasterRegionQuery(req dto.MasterRegionPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterRegion{})
	query = util.JoinUsers("master_region")(query)
	//NOTE - apply filters
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_region")

	}
	return query
}

func (r *masterRegionRepositoryImpl) FindMasterRegionWithFilter(req dto.MasterRegionPageReqDto) ([]entity.MasterRegion, error) {
	var results []entity.MasterRegion

	query := r.buildMasterRegionQuery(req)
	query = query.Order("country_code asc, region_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterRegionRepositoryImpl) CountMasterRegionWithFilter(req dto.MasterRegionPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterRegionQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterRegionRepositoryImpl) FindLatestSyncDate() (*time.Time, error) {
	var result entity.MasterRegion
	err := r.DB.
		Model(&entity.MasterRegion{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterRegionRepositoryImpl) UpdatesMasterRegionFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterRegion{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterRegionRepositoryImpl) FindMasterRegionAll() ([]entity.MasterRegion, error) {
	var result []entity.MasterRegion
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterRegionRepositoryImpl) UpdateMasterRegionAllFields(e *entity.MasterRegion) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterRegionRepositoryImpl) InsertMasterRegionList(data []entity.MasterRegion) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterRegionRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
