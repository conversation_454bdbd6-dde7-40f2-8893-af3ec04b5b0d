package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterCustomerSellerOfferRepositoryImpl) buildMasterCustomerSellerOfferQuery(req dto.MasterCustomerSellerOfferPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterCustomerSellerOffer{})
	query = util.JoinUsers("master_customer_seller_offer")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_customer_seller_offer")
	}
	return query
}

func (r *masterCustomerSellerOfferRepositoryImpl) FindMasterCustomerSellerOfferWithFilter(req dto.MasterCustomerSellerOfferPageReqDto) ([]entity.MasterCustomerSellerOffer, error) {
	var results []entity.MasterCustomerSellerOffer

	query := r.buildMasterCustomerSellerOfferQuery(req)
	query.Order("seller_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterCustomerSellerOfferRepositoryImpl) CountMasterCustomerSellerOfferWithFilter(req dto.MasterCustomerSellerOfferPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterCustomerSellerOfferQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterCustomerSellerOfferRepositoryImpl) FindMasterCustomerSellerOfferLatestSyncDate() (*time.Time, error) {
	var result entity.MasterCustomerSellerOffer
	err := r.DB.
		Model(&entity.MasterCustomerSellerOffer{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterCustomerSellerOfferRepositoryImpl) UpdatesMasterCustomerSellerOfferFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterCustomerSellerOffer{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterCustomerSellerOfferRepositoryImpl) FindMasterCustomerSellerOfferAll() ([]entity.MasterCustomerSellerOffer, error) {
	var result []entity.MasterCustomerSellerOffer
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterCustomerSellerOfferRepositoryImpl) UpdateMasterCustomerSellerOfferAllFields(e *entity.MasterCustomerSellerOffer) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCustomerSellerOfferRepositoryImpl) InsertMasterCustomerSellerOfferList(data []entity.MasterCustomerSellerOffer) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCustomerSellerOfferRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
