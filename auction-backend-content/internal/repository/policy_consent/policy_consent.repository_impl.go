package repository

import (
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/constant"
	"content-service/internal/model/entity"
	"fmt"
	"time"

	"gorm.io/gorm"
)

func (r *policyConsentRepositoryImpl) buildPolicyConsentQuery(consentType *string) *gorm.DB {
	query := r.DB.Model(&entity.PolicyConsent{}).Where("consent_type = ?", consentType)
	query = util.JoinUsers("policy_consent")(query)
	return query
}

func (r *policyConsentRepositoryImpl) FindAllPolicyConsent(req model.PagingRequest, consentType *string) ([]entity.PolicyConsent, error) {
	var results []entity.PolicyConsent

	query := r.buildPolicyConsentQuery(consentType)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplySortFromMapField(query, snakeSort, req.SortOrder, "policy_consent", constant.SortingPolicyConsent, true)
	}
	query.Order("is_active desc ,version_major asc,version_minor asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *policyConsentRepositoryImpl) CountAllPolicyConsent(consentType *string) (int64, error) {
	var count int64
	query := r.buildPolicyConsentQuery(consentType)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *policyConsentRepositoryImpl) FindPolicyConsentById(id int) (entity.PolicyConsent, error) {
	var result entity.PolicyConsent
	query := r.DB.Model(&entity.PolicyConsent{})
	query = util.JoinUsers("policy_consent")(query)

	query = query.Where("id = ?", id)

	if err := query.First(&result).Error; err != nil {
		return result, err
	}

	return result, nil
}

func (r *policyConsentRepositoryImpl) FindMaxVersionByFilters(filters map[string]interface{}) (*entity.PolicyConsent, error) {
	var result entity.PolicyConsent

	query := r.DB.Model(&entity.PolicyConsent{})

	for key, value := range filters {
		if value != nil {
			query = query.Where(fmt.Sprintf("%s = ?", key), value)
		}
	}
	query = query.Order("version_major DESC").Order("version_minor DESC")

	err := query.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *policyConsentRepositoryImpl) InsertPolicyConsent(tx *gorm.DB, data *entity.PolicyConsent) error {
	if err := tx.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *policyConsentRepositoryImpl) InsertPolicyConsentLog(tx *gorm.DB, data *entity.PolicyConsentLog) error {
	if err := tx.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *policyConsentRepositoryImpl) UpdatesPolicyConsentFieldsWhere(tx *gorm.DB, fields map[string]interface{}, whereClause string, args ...interface{}) (int64, *entity.PolicyConsent, error) {
	var updatedRecord entity.PolicyConsent
	result := tx.Model(&entity.PolicyConsent{}).Where(whereClause, args...).Updates(fields).Scan(&updatedRecord)
	if result.Error != nil {
		return 0, nil, result.Error
	}
	return result.RowsAffected, &updatedRecord, nil
}

func (r *policyConsentRepositoryImpl) DeletePolicyConsentByID(tx *gorm.DB, id int) (int64, error) {
	result := tx.Where("id = ?", id).Delete(&entity.PolicyConsent{})
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *policyConsentRepositoryImpl) FindNextClosestStartDateInRange(inputStart, inputEnd *time.Time, consentType *string, excludedId ...int) (*entity.PolicyConsent, error) {
	var result entity.PolicyConsent

	query := r.DB.Model(&entity.PolicyConsent{}).
		Where("start_date >= ? and consent_type = ?", inputStart, consentType)

	if inputEnd != nil {
		query = query.Where("start_date <= ?", *inputEnd)
	}
	if len(excludedId) > 0 {
		query = query.Where("id NOT IN ?", excludedId)
	}

	err := query.Order("start_date ASC").Limit(1).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *policyConsentRepositoryImpl) FindEndDateInRange(inputStart, inputEnd *time.Time, consentType *string, excludedId ...int) ([]*entity.PolicyConsent, error) {
	var result []*entity.PolicyConsent
	query := r.DB.Model(&entity.PolicyConsent{}).
		Where("(end_date >= ? or end_date IS NULL) and consent_type = ?", inputStart, consentType)

	if inputEnd != nil {
		query = query.Where("start_date <= ?", inputEnd)
	}
	if len(excludedId) > 0 {
		query = query.Where("id NOT IN ?", excludedId)
	}

	err := query.Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r *policyConsentRepositoryImpl) FindUnscopedPolicyConsentById(id int) (entity.PolicyConsent, error) {
	var result entity.PolicyConsent
	query := r.DB.Unscoped().Model(&entity.PolicyConsent{})

	query = query.Where("id = ?", id)

	if err := query.First(&result).Error; err != nil {
		return result, err
	}

	return result, nil
}

func (r *policyConsentRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
