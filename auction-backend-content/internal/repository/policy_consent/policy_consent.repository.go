package repository

import (
	"backend-common-lib/model"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type policyConsentRepositoryImpl struct {
	DB *gorm.DB
}

type PolicyConsentRepository interface {
	FindAllPolicyConsent(req model.PagingRequest, consentType *string) ([]entity.PolicyConsent, error)
	CountAllPolicyConsent(consentType *string) (int64, error)

	FindPolicyConsentById(id int) (entity.PolicyConsent, error)
	FindMaxVersionByFilters(filters map[string]interface{}) (*entity.PolicyConsent, error)

	InsertPolicyConsent(tx *gorm.DB, data *entity.PolicyConsent) error
	InsertPolicyConsentLog(tx *gorm.DB, data *entity.PolicyConsentLog) error
	UpdatesPolicyConsentFieldsWhere(tx *gorm.DB, fields map[string]interface{}, whereClause string, args ...interface{}) (int64, *entity.PolicyConsent, error)
	DeletePolicyConsentByID(tx *gorm.DB, id int) (int64, error)
	FindUnscopedPolicyConsentById(id int) (entity.PolicyConsent, error)

	FindNextClosestStartDateInRange(inputStart, inputEnd *time.Time, consentType *string, excludedId ...int) (*entity.PolicyConsent, error)
	FindEndDateInRange(inputStart, inputEnd *time.Time, consentType *string, excludedId ...int) ([]*entity.PolicyConsent, error)

	GetDB() *gorm.DB
}

func NewPolicyConsentRepository(db *gorm.DB) PolicyConsentRepository {
	return &policyConsentRepositoryImpl{DB: db}
}
