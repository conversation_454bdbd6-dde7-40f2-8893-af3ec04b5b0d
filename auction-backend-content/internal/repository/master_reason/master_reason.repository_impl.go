package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterReasonRepositoryImpl) buildMasterReasonQuery(req dto.MasterReasonPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterReason{})
	query = util.JoinUsers("master_reason")(query)
	//NOTE - apply filters
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_reason")

	}
	return query
}

func (r *masterReasonRepositoryImpl) FindMasterReasonWithFilter(req dto.MasterReasonPageReqDto) ([]entity.MasterReason, error) {
	var results []entity.MasterReason

	query := r.buildMasterReasonQuery(req)
	query = query.Order("reason_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterReasonRepositoryImpl) CountMasterReasonWithFilter(req dto.MasterReasonPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterReasonQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterReasonRepositoryImpl) FindLatestSyncDate() (*time.Time, error) {
	var result entity.MasterReason
	err := r.DB.
		Model(&entity.MasterReason{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterReasonRepositoryImpl) UpdatesMasterReasonFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterReason{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterReasonRepositoryImpl) FindMasterReasonAll() ([]entity.MasterReason, error) {
	var result []entity.MasterReason
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterReasonRepositoryImpl) UpdateMasterReasonAllFields(e *entity.MasterReason) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterReasonRepositoryImpl) InsertMasterReasonList(data []entity.MasterReason) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterReasonRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
