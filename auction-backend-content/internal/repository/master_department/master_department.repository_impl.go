package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterDepartmentRepositoryImpl) buildMasterDepartmentQuery(req dto.MasterDepartmentPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterDepartment{})
	query = util.JoinUsers("master_department")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_department")
	}
	return query
}

func (r *masterDepartmentRepositoryImpl) FindMasterDepartmentWithFilter(req dto.MasterDepartmentPageReqDto) ([]entity.MasterDepartment, error) {
	var results []entity.MasterDepartment

	query := r.buildMasterDepartmentQuery(req)
	query.Order("department_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterDepartmentRepositoryImpl) CountMasterDepartmentWithFilter(req dto.MasterDepartmentPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterDepartmentQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterDepartmentRepositoryImpl) FindMasterDepartmentLatestSyncDate() (*time.Time, error) {
	var result entity.MasterDepartment
	err := r.DB.
		Model(&entity.MasterDepartment{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterDepartmentRepositoryImpl) UpdatesMasterDepartmentFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterDepartment{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterDepartmentRepositoryImpl) FindMasterDepartmentAll() ([]entity.MasterDepartment, error) {
	var result []entity.MasterDepartment
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterDepartmentRepositoryImpl) UpdateMasterDepartmentAllFields(e *entity.MasterDepartment) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterDepartmentRepositoryImpl) InsertMasterDepartmentList(data []entity.MasterDepartment) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterDepartmentRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
