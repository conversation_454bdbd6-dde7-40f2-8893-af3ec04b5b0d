package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterDepartmentRepositoryImpl struct {
	DB *gorm.DB
}

type MasterDepartmentRepository interface {
	FindMasterDepartmentWithFilter(req dto.MasterDepartmentPageReqDto) ([]entity.MasterDepartment, error)
	CountMasterDepartmentWithFilter(req dto.MasterDepartmentPageReqDto) (int64, error)
	FindMasterDepartmentLatestSyncDate() (*time.Time, error)
	UpdatesMasterDepartmentFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterDepartmentAll() ([]entity.MasterDepartment, error)
	UpdateMasterDepartmentAllFields(e *entity.MasterDepartment) error
	InsertMasterDepartmentList(data []entity.MasterDepartment) error

	GetDB() *gorm.DB
}

func NewMasterDepartmentRepository(db *gorm.DB) MasterDepartmentRepository {
	return &masterDepartmentRepositoryImpl{DB: db}
}
