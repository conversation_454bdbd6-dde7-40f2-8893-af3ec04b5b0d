package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterBankRepositoryImpl struct {
	DB *gorm.DB
}

type MasterBankRepository interface {
	FindMasterBankWithFilter(req dto.MasterBankPageReqDto) ([]entity.MasterBank, error)
	CountMasterBankWithFilter(req dto.MasterBankPageReqDto) (int64, error)
	FindLatestSyncDate() (*time.Time, error)

	UpdatesMasterBankFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)

	FindMasterBankAll() ([]entity.MasterBank, error)
	UpdateMasterBankAllFields(e *entity.MasterBank) error
	InsertMasterBankList(data []entity.MasterBank) error

	GetDB() *gorm.DB
}

func NewMasterBankRepository(db *gorm.DB) MasterBankRepository {
	return &masterBankRepositoryImpl{DB: db}
}
