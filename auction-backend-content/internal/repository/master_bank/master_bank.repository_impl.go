package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterBankRepositoryImpl) buildMasterBankQuery(req dto.MasterBankPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterBank{})
	query = util.JoinUsers("master_bank")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_bank")

	}
	return query
}

func (r *masterBankRepositoryImpl) FindMasterBankWithFilter(req dto.MasterBankPageReqDto) ([]entity.MasterBank, error) {
	var results []entity.MasterBank

	query := r.buildMasterBankQuery(req)
	query = query.Order("bank_account_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterBankRepositoryImpl) CountMasterBankWithFilter(req dto.MasterBankPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterBankQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterBankRepositoryImpl) FindLatestSyncDate() (*time.Time, error) {
	var result entity.MasterBank
	err := r.DB.
		Model(&entity.MasterBank{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterBankRepositoryImpl) UpdatesMasterBankFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterBank{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterBankRepositoryImpl) FindMasterBankAll() ([]entity.MasterBank, error) {
	var result []entity.MasterBank
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterBankRepositoryImpl) UpdateMasterBankAllFields(e *entity.MasterBank) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterBankRepositoryImpl) InsertMasterBankList(data []entity.MasterBank) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterBankRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
