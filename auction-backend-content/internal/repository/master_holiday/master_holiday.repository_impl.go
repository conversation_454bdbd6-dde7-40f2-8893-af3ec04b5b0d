package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterHolidayRepositoryImpl) buildMasterHolidayQuery(req dto.MasterHolidayPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterHoliday{})
	query = util.JoinUsers("master_holiday")(query)
	//NOTE - apply filters
	query = util.ApplyFiltersFromStruct(query, req)
	if req.Date != nil {
		query = query.Where("date = ?", req.Date)
	}

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_holiday")

	}
	return query
}

func (r *masterHolidayRepositoryImpl) FindMasterHolidayWithFilter(req dto.MasterHolidayPageReqDto) ([]entity.MasterHoliday, error) {
	var results []entity.MasterHoliday

	query := r.buildMasterHolidayQuery(req)
	query = query.Order("date desc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterHolidayRepositoryImpl) CountMasterHolidayWithFilter(req dto.MasterHolidayPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterHolidayQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterHolidayRepositoryImpl) FindLatestSyncDate() (*time.Time, error) {
	var result entity.MasterHoliday
	err := r.DB.
		Model(&entity.MasterHoliday{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterHolidayRepositoryImpl) UpdatesMasterHolidayFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterHoliday{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterHolidayRepositoryImpl) FindMasterHolidayAll() ([]entity.MasterHoliday, error) {
	var result []entity.MasterHoliday
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterHolidayRepositoryImpl) UpdateMasterHolidayAllFields(e *entity.MasterHoliday) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterHolidayRepositoryImpl) InsertMasterHolidayList(data []entity.MasterHoliday) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterHolidayRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
