package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterCostRevenueRepositoryImpl) buildMasterCostRevenueQuery(req dto.MasterCostRevenuePageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterCostRevenue{})
	query = util.JoinUsers("master_cost_revenue")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_cost_revenue")
	}
	return query
}

func (r *masterCostRevenueRepositoryImpl) FindMasterCostRevenueWithFilter(req dto.MasterCostRevenuePageReqDto) ([]entity.MasterCostRevenue, error) {
	var results []entity.MasterCostRevenue

	query := r.buildMasterCostRevenueQuery(req)
	query.Order("cost_revenue_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterCostRevenueRepositoryImpl) CountMasterCostRevenueWithFilter(req dto.MasterCostRevenuePageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterCostRevenueQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterCostRevenueRepositoryImpl) FindMasterCostRevenueLatestSyncDate() (*time.Time, error) {
	var result entity.MasterCostRevenue
	err := r.DB.
		Model(&entity.MasterCostRevenue{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterCostRevenueRepositoryImpl) UpdatesMasterCostRevenueFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterCostRevenue{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterCostRevenueRepositoryImpl) FindMasterCostRevenueAll() ([]entity.MasterCostRevenue, error) {
	var result []entity.MasterCostRevenue
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterCostRevenueRepositoryImpl) UpdateMasterCostRevenueAllFields(e *entity.MasterCostRevenue) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCostRevenueRepositoryImpl) InsertMasterCostRevenueList(data []entity.MasterCostRevenue) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCostRevenueRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
