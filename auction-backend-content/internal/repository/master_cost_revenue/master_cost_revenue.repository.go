package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterCostRevenueRepositoryImpl struct {
	DB *gorm.DB
}

type MasterCostRevenueRepository interface {
	FindMasterCostRevenueWithFilter(req dto.MasterCostRevenuePageReqDto) ([]entity.MasterCostRevenue, error)
	CountMasterCostRevenueWithFilter(req dto.MasterCostRevenuePageReqDto) (int64, error)
	FindMasterCostRevenueLatestSyncDate() (*time.Time, error)
	UpdatesMasterCostRevenueFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterCostRevenueAll() ([]entity.MasterCostRevenue, error)
	UpdateMasterCostRevenueAllFields(e *entity.MasterCostRevenue) error
	InsertMasterCostRevenueList(data []entity.MasterCostRevenue) error

	GetDB() *gorm.DB
}

func NewMasterCostRevenueRepository(db *gorm.DB) MasterCostRevenueRepository {
	return &masterCostRevenueRepositoryImpl{DB: db}
}
