package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterSellerOfferRepositoryImpl) buildMasterSellerOfferQuery(req dto.MasterSellerOfferPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterSellerOffer{})
	query = util.JoinUsers("master_seller_offer")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_seller_offer")
	}
	return query
}

func (r *masterSellerOfferRepositoryImpl) FindMasterSellerOfferWithFilter(req dto.MasterSellerOfferPageReqDto) ([]entity.MasterSellerOffer, error) {
	var results []entity.MasterSellerOffer

	query := r.buildMasterSellerOfferQuery(req)
	query.Order("seller_offer_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterSellerOfferRepositoryImpl) CountMasterSellerOfferWithFilter(req dto.MasterSellerOfferPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterSellerOfferQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterSellerOfferRepositoryImpl) FindMasterSellerOfferLatestSyncDate() (*time.Time, error) {
	var result entity.MasterSellerOffer
	err := r.DB.
		Model(&entity.MasterSellerOffer{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterSellerOfferRepositoryImpl) UpdatesMasterSellerOfferFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterSellerOffer{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterSellerOfferRepositoryImpl) FindMasterSellerOfferAll() ([]entity.MasterSellerOffer, error) {
	var result []entity.MasterSellerOffer
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterSellerOfferRepositoryImpl) UpdateMasterSellerOfferAllFields(e *entity.MasterSellerOffer) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterSellerOfferRepositoryImpl) InsertMasterSellerOfferList(data []entity.MasterSellerOffer) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterSellerOfferRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
