package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterBranchRepositoryImpl) buildMasterBranchQuery(req dto.MasterBranchPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterBranch{})
	query = util.JoinUsers("master_branch")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_branch")
	}
	return query
}

func (r *masterBranchRepositoryImpl) FindMasterBranchWithFilter(req dto.MasterBranchPageReqDto) ([]entity.MasterBranch, error) {
	var results []entity.MasterBranch

	query := r.buildMasterBranchQuery(req)
	query.Order("branch_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterBranchRepositoryImpl) FindById(id int) (*entity.MasterBranch, error) {
	var result entity.MasterBranch
	if err := r.DB.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *masterBranchRepositoryImpl) CountMasterBranchWithFilter(req dto.MasterBranchPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterBranchQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterBranchRepositoryImpl) FindMasterBranchLatestSyncDate() (*time.Time, error) {
	var result entity.MasterBranch
	err := r.DB.
		Model(&entity.MasterBranch{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterBranchRepositoryImpl) UpdatesMasterBranchFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterBranch{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterBranchRepositoryImpl) FindMasterBranchAll() ([]entity.MasterBranch, error) {
	var result []entity.MasterBranch
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterBranchRepositoryImpl) UpdateMasterBranchAllFields(e *entity.MasterBranch) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterBranchRepositoryImpl) InsertMasterBranchList(data []entity.MasterBranch) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterBranchRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
