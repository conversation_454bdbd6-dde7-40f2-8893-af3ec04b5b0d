package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterBranchRepositoryImpl struct {
	DB *gorm.DB
}

type MasterBranchRepository interface {
	FindMasterBranchWithFilter(req dto.MasterBranchPageReqDto) ([]entity.MasterBranch, error)
	CountMasterBranchWithFilter(req dto.MasterBranchPageReqDto) (int64, error)
	FindMasterBranchLatestSyncDate() (*time.Time, error)
	UpdatesMasterBranchFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterBranchAll() ([]entity.MasterBranch, error)
	UpdateMasterBranchAllFields(e *entity.MasterBranch) error
	InsertMasterBranchList(data []entity.MasterBranch) error
	FindById(id int) (*entity.MasterBranch, error)

	GetDB() *gorm.DB
}

func NewMasterBranchRepository(db *gorm.DB) MasterBranchRepository {
	return &masterBranchRepositoryImpl{DB: db}
}
