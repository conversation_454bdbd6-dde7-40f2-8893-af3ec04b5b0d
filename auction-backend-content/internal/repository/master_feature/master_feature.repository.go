package repository

import (
	"gorm.io/gorm"
)

type masterFeatureRepositoryImpl struct {
	DB *gorm.DB
}

type MasterFeatureRepository interface {
	// FindMasterFeatureWithFilter(req dto.MasterFeaturePageReqDto) ([]entity.MasterFeature, error)
	// CountMasterFeatureWithFilter(req dto.MasterFeaturePageReqDto) (int64, error)
	// FindLatestSyncDate() (*time.Time, error)

	// UpdatesMasterFeatureFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)

	// FindMasterFeatureAll() ([]entity.MasterFeature, error)
	// UpdateMasterFeatureAllFields(e *entity.MasterFeature) error
	// InsertMasterFeatureList(data []entity.MasterFeature) error

	GetDB() *gorm.DB
}

func NewMasterFeatureRepository(db *gorm.DB) MasterFeatureRepository {
	return &masterFeatureRepositoryImpl{DB: db}
}
