package repository

import (
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *proxyBidCancelReasonRepositoryImpl) buildProxyBidCancelReasonQuery() *gorm.DB {
	query := r.DB.Model(&entity.ProxyBidCancelReason{})
	query = util.JoinUsers("proxy_bid_cancel_reason")(query)
	return query
}

func (r *proxyBidCancelReasonRepositoryImpl) FindAllProxyBidCancelReason(req model.PagingRequest) ([]entity.ProxyBidCancelReason, error) {
	var results []entity.ProxyBidCancelReason

	query := r.buildProxyBidCancelReasonQuery()
	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "proxy_bid_cancel_reason")
	}

	//NOTE - default order
	query.Order("updated_date desc ,reason asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *proxyBidCancelReasonRepositoryImpl) CountAllProxyBidCancelReason() (int64, error) {
	var count int64
	query := r.buildProxyBidCancelReasonQuery()
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *proxyBidCancelReasonRepositoryImpl) FindProxyBidCancelReasonByID(id int) (*entity.ProxyBidCancelReason, error) {
	var result entity.ProxyBidCancelReason

	query := r.buildProxyBidCancelReasonQuery()
	query = query.Where("id = ?", id)

	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *proxyBidCancelReasonRepositoryImpl) FindByReason(reason string, excludeID int) (*entity.ProxyBidCancelReason, error) {
	var results entity.ProxyBidCancelReason

	query := r.DB.Where("reason = ? AND id != ?", reason, excludeID)

	if err := query.First(&results).Error; err != nil {
		return nil, err
	}

	return &results, nil
}

func (r *proxyBidCancelReasonRepositoryImpl) InsertProxyBidCancelReason(proxyBidCancelReason *entity.ProxyBidCancelReason) error {
	if err := r.DB.Create(proxyBidCancelReason).Error; err != nil {
		return err
	}
	return nil
}

func (r *proxyBidCancelReasonRepositoryImpl) UpdateProxyBidCancelReason(id int, fieldToUpdate map[string]interface{}) (int64, error) {
	result := r.DB.Model(&entity.ProxyBidCancelReason{}).
		Where("id = ?", id).
		Updates(fieldToUpdate)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *proxyBidCancelReasonRepositoryImpl) DeleteProxyBidCancelReason(id int) (int64, error) {
	result := r.DB.Where("id = ?", id).Delete(&entity.ProxyBidCancelReason{})

	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *proxyBidCancelReasonRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
