package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterCityRepositoryImpl) buildMasterCityQuery(req dto.MasterCityPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterCity{})
	query = util.JoinUsers("master_city")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_city")
	}
	return query
}

func (r *masterCityRepositoryImpl) FindMasterCityWithFilter(req dto.MasterCityPageReqDto) ([]entity.MasterCity, error) {
	var result []entity.MasterCity

	query := r.buildMasterCityQuery(req)
	query.Order("city_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r *masterCityRepositoryImpl) CountMasterCityWithFilter(req dto.MasterCityPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterCityQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterCityRepositoryImpl) FindMasterCityLatestSyncDate() (*time.Time, error) {
	var result entity.MasterCity
	err := r.DB.
		Model(&entity.MasterCity{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterCityRepositoryImpl) UpdatesMasterCityFieldsWhere(field map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterCity{}).Where(whereClause, args...).Updates(field)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterCityRepositoryImpl) FindMasterCityAll() ([]entity.MasterCity, error) {
	var result []entity.MasterCity
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterCityRepositoryImpl) UpdateMasterCityAllFields(e *entity.MasterCity) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCityRepositoryImpl) InsertMasterCityList(data []entity.MasterCity) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCityRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
