package repository

import (
	"backend-common-lib/constant"
	"backend-common-lib/model"
	"backend-common-lib/util"
	contentConst "content-service/constant"
	"content-service/internal/model/entity"
	"fmt"

	"gorm.io/gorm"
)

func (r *additionalServiceRepositoryImpl) buildAdditionalServiceQuery() *gorm.DB {
	query := r.DB.Model(&entity.AdditionalService{}).
		Select(`additional_service.*, 
		cfg.value_string AS service_type_th,
		cfg.value_string2 AS service_type_en`)
	query = util.JoinUsers("additional_service")(query)
	query = query.
		Joins(`LEFT JOIN config_parameters AS cfg ON 
		cfg.parameter_module = ? AND 
		cfg.parameter_name = ? AND 
		cfg.value_int = additional_service.service_type_id`,
			constant.ConfigParamConst.MODULE_SYSTEM_ADMIN,
			constant.ConfigParamConst.SERVICE_TYPE,
		)

	return query
}

func (r *additionalServiceRepositoryImpl) FindAllAdditionalService(req model.PagingRequest) ([]entity.AdditionalService, error) {
	var results []entity.AdditionalService

	query := r.buildAdditionalServiceQuery()
	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplySortFromMapField(query, snakeSort, req.SortOrder, "additional_service", contentConst.SortingAdditionalService, true)
	}

	//NOTE - default order
	query.Order(fmt.Sprintf("%s %s, %s %s", "updated_date", "asc", "id", "asc"))

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *additionalServiceRepositoryImpl) CountAllAdditionalService() (int64, error) {
	var count int64
	query := r.buildAdditionalServiceQuery()
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// func (r *additionalServiceRepositoryImpl) FindAdditionalServiceByID(id int) (*entity.AdditionalService, error) {
// 	var result entity.AdditionalService

// 	query := r.buildAdditionalServiceQuery()
// 	query = query.Where("payment_due_notification.id = ?", id)

// 	if err := query.First(&result).Error; err != nil {
// 		return nil, err
// 	}

// 	return &result, nil
// }

// func (r *additionalServiceRepositoryImpl) InsertAdditionalService(entityPaymentDue *entity.AdditionalService) error {
// 	if err := r.DB.Create(entityPaymentDue).Error; err != nil {
// 		return err
// 	}
// 	return nil
// }

// func (r *additionalServiceRepositoryImpl) FindByCustomerGroupId(customerGroupId int) ([]entity.AdditionalService, error) {
// 	var results []entity.AdditionalService

// 	query := r.DB.Where("payment_due_notification.customer_group_id = ?", customerGroupId)

// 	if err := query.Find(&results).Error; err != nil {
// 		return nil, err
// 	}

// 	return results, nil
// }

// func (r *additionalServiceRepositoryImpl) UpdateAdditionalService(id int, fieldToUpdate map[string]interface{}) (int64, error) {
// 	result := r.DB.Model(&entity.AdditionalService{}).
// 		Where("id = ?", id).
// 		Updates(fieldToUpdate)
// 	if result.Error != nil {
// 		return 0, result.Error
// 	}
// 	return result.RowsAffected, nil
// }

// func (r *additionalServiceRepositoryImpl) DeleteAdditionalService(id int) (int64, error) {
// 	result := r.DB.Where("id = ?", id).Delete(&entity.AdditionalService{})

// 	if result.Error != nil {
// 		return 0, result.Error
// 	}
// 	return result.RowsAffected, nil
// }

func (r *additionalServiceRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
