package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterDistrictRepositoryImpl struct {
	DB *gorm.DB
}

type MasterDistrictRepository interface {
	FindMasterDistrictWithFilter(req dto.MasterDistrictPageReqDto) ([]entity.MasterDistrict, error)
	CountMasterDistrictWithFilter(req dto.MasterDistrictPageReqDto) (int64, error)
	FindMasterDistrictLatestSyncDate() (*time.Time, error)
	UpdatesMasterDistrictFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterDistrictAll() ([]entity.MasterDistrict, error)
	UpdateMasterDistrictAllFields(e *entity.MasterDistrict) error
	InsertMasterDistrictListWithBatches(data []entity.MasterDistrict) error

	GetDB() *gorm.DB
}

func NewMasterDistrictRepository(db *gorm.DB) MasterDistrictRepository {
	return &masterDistrictRepositoryImpl{DB: db}
}
