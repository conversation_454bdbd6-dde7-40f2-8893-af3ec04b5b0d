package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterRegisterTypeCarRepositoryImpl struct {
	DB *gorm.DB
}

type MasterRegisterTypeCarRepository interface {
	FindMasterRegisterTypeCarWithFilter(req dto.MasterRegisterTypeCarPageReqDto) ([]entity.MasterRegisterTypeCar, error)
	CountMasterRegisterTypeCarWithFilter(req dto.MasterRegisterTypeCarPageReqDto) (int64, error)
	FindMasterRegisterTypeCarLatestSyncDate() (*time.Time, error)
	UpdatesMasterRegisterTypeCarFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterRegisterTypeCarAll() ([]entity.MasterRegisterTypeCar, error)
	UpdateMasterRegisterTypeCarAllFields(e *entity.MasterRegisterTypeCar) error
	InsertMasterRegisterTypeCarList(data []entity.MasterRegisterTypeCar) error

	GetDB() *gorm.DB
}

func NewMasterRegisterTypeCarRepository(db *gorm.DB) MasterRegisterTypeCarRepository {
	return &masterRegisterTypeCarRepositoryImpl{DB: db}
}
