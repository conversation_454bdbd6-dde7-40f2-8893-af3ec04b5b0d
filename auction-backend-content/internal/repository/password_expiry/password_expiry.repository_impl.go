package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *passwordExpiryRepositoryImpl) FindAllPasswordExpiry() ([]entity.PasswordExpiry, error) {
	var results []entity.PasswordExpiry

	query := r.DB.Model(&entity.PasswordExpiry{})
	query = util.JoinUsers("password_expiry")(query)

	query.Order("user_type asc")

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *passwordExpiryRepositoryImpl) UpdatesPasswordExpiryFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, *entity.PasswordExpiry, error) {
	var updatedRecord entity.PasswordExpiry
	result := r.DB.Model(&entity.PasswordExpiry{}).Where(whereClause, args...).Updates(fields).Scan(&updatedRecord)
	if result.Error != nil {
		return 0, nil, result.Error
	}
	return result.RowsAffected, &updatedRecord, nil
}

func (r *passwordExpiryRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
