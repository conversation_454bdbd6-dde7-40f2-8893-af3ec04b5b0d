package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterCustomerGroupRepositoryImpl) buildMasterCustomerGroupQuery(req dto.MasterCustomerGroupPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterCustomerGroup{})
	query = util.JoinUsers("master_customer_group")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_customer_group")
	}
	return query
}

func (r *masterCustomerGroupRepositoryImpl) FindMasterCustomerGroupWithFilter(req dto.MasterCustomerGroupPageReqDto) ([]entity.MasterCustomerGroup, error) {
	var results []entity.MasterCustomerGroup

	query := r.buildMasterCustomerGroupQuery(req)
	query.Order("customer_group_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterCustomerGroupRepositoryImpl) CountMasterCustomerGroupWithFilter(req dto.MasterCustomerGroupPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterCustomerGroupQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterCustomerGroupRepositoryImpl) FindMasterCustomerGroupLatestSyncDate() (*time.Time, error) {
	var result entity.MasterCustomerGroup
	err := r.DB.
		Model(&entity.MasterCustomerGroup{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterCustomerGroupRepositoryImpl) UpdatesMasterCustomerGroupFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterCustomerGroup{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterCustomerGroupRepositoryImpl) FindMasterCustomerGroupAll() ([]entity.MasterCustomerGroup, error) {
	var result []entity.MasterCustomerGroup
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterCustomerGroupRepositoryImpl) UpdateMasterCustomerGroupAllFields(e *entity.MasterCustomerGroup) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCustomerGroupRepositoryImpl) InsertMasterCustomerGroupList(data []entity.MasterCustomerGroup) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCustomerGroupRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
