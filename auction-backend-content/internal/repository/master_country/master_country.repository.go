package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterCountryRepositoryImpl struct {
	DB *gorm.DB
}

type MasterCountryRepository interface {
	FindMasterCountryWithFilter(req dto.MasterCountryPageReqDto) ([]entity.MasterCountry, error)
	CountMasterCountryWithFilter(req dto.MasterCountryPageReqDto) (int64, error)
	FindLatestSyncDate() (*time.Time, error)

	UpdatesMasterCountryFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)

	FindMasterCountryAll() ([]entity.MasterCountry, error)
	UpdateMasterCountryAllFields(e *entity.MasterCountry) error
	InsertMasterCountryList(data []entity.MasterCountry) error

	GetDB() *gorm.DB
}

func NewMasterCountryRepository(db *gorm.DB) MasterCountryRepository {
	return &masterCountryRepositoryImpl{DB: db}
}
