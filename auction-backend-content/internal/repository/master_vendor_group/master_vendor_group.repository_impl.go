package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterVendorGroupRepositoryImpl) buildMasterVendorGroupQuery(req dto.MasterVendorGroupPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterVendorGroup{})
	query = util.JoinUsers("master_vendor_group")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_vendor_group")
	}
	return query
}

func (r *masterVendorGroupRepositoryImpl) FindMasterVendorGroupWithFilter(req dto.MasterVendorGroupPageReqDto) ([]entity.MasterVendorGroup, error) {
	var results []entity.MasterVendorGroup

	query := r.buildMasterVendorGroupQuery(req)
	query.Order("vendor_group_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterVendorGroupRepositoryImpl) CountMasterVendorGroupWithFilter(req dto.MasterVendorGroupPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterVendorGroupQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterVendorGroupRepositoryImpl) FindMasterVendorGroupLatestSyncDate() (*time.Time, error) {
	var result entity.MasterVendorGroup
	err := r.DB.
		Model(&entity.MasterVendorGroup{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterVendorGroupRepositoryImpl) UpdatesMasterVendorGroupFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterVendorGroup{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterVendorGroupRepositoryImpl) FindMasterVendorGroupAll() ([]entity.MasterVendorGroup, error) {
	var result []entity.MasterVendorGroup
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterVendorGroupRepositoryImpl) UpdateMasterVendorGroupAllFields(e *entity.MasterVendorGroup) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterVendorGroupRepositoryImpl) InsertMasterVendorGroupList(data []entity.MasterVendorGroup) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterVendorGroupRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
