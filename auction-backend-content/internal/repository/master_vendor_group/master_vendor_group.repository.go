package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterVendorGroupRepositoryImpl struct {
	DB *gorm.DB
}

type MasterVendorGroupRepository interface {
	FindMasterVendorGroupWithFilter(req dto.MasterVendorGroupPageReqDto) ([]entity.MasterVendorGroup, error)
	CountMasterVendorGroupWithFilter(req dto.MasterVendorGroupPageReqDto) (int64, error)
	FindMasterVendorGroupLatestSyncDate() (*time.Time, error)
	UpdatesMasterVendorGroupFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterVendorGroupAll() ([]entity.MasterVendorGroup, error)
	UpdateMasterVendorGroupAllFields(e *entity.MasterVendorGroup) error
	InsertMasterVendorGroupList(data []entity.MasterVendorGroup) error

	GetDB() *gorm.DB
}

func NewMasterVendorGroupRepository(db *gorm.DB) MasterVendorGroupRepository {
	return &masterVendorGroupRepositoryImpl{DB: db}
}
