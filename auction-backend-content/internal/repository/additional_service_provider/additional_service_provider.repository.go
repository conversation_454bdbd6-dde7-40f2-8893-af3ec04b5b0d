package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type additionalServiceProviderRepositoryImpl struct {
	DB *gorm.DB
}

type AdditionalServiceProviderRepository interface {
	GetProviderByAdditionalServiceIds(additionalServiceIds []int) (map[int][]entity.AdditionalServiceProvider, error)
	GetDB() *gorm.DB
}

func NewAdditionalServiceProviderRepository(db *gorm.DB) AdditionalServiceProviderRepository {
	return &additionalServiceProviderRepositoryImpl{DB: db}
}
