package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *additionalServiceProviderRepositoryImpl) GetProviderByAdditionalServiceIds(additionalServiceIds []int) (map[int][]entity.AdditionalServiceProvider, error) {
	var assets []entity.AdditionalServiceProvider
	if err := r.DB.
		Where("additional_service_id IN ?", additionalServiceIds).Find(&assets).Error; err != nil {
		return nil, err
	}

	result := make(map[int][]entity.AdditionalServiceProvider)
	for _, asset := range assets {
		result[util.Val(asset.AdditionalServiceId)] = append(result[util.Val(asset.AdditionalServiceId)], asset)
	}

	return result, nil
}

func (r *additionalServiceProviderRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
