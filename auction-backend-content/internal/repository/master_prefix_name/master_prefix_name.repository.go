package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterPrefixNameRepositoryImpl struct {
	DB *gorm.DB
}

type MasterPrefixNameRepository interface {
	FindMasterPrefixNameWithFilter(req dto.MasterPrefixNamePageReqDto) ([]entity.MasterPrefixName, error)
	CountMasterPrefixNameWithFilter(req dto.MasterPrefixNamePageReqDto) (int64, error)
	FindLatestSyncDate() (*time.Time, error)

	UpdatesMasterPrefixNameFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)

	FindMasterPrefixNameAll() ([]entity.MasterPrefixName, error)
	UpdateMasterPrefixNameAllFields(e *entity.MasterPrefixName) error
	InsertMasterPrefixNameList(data []entity.MasterPrefixName) error

	GetDB() *gorm.DB
}

func NewMasterPrefixNameRepository(db *gorm.DB) MasterPrefixNameRepository {
	return &masterPrefixNameRepositoryImpl{DB: db}
}
