package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterVatCodeRepositoryImpl) buildMasterVatCodeQuery(req dto.MasterVatCodePageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterVatCode{})
	query = util.JoinUsers("master_vat_code")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_vat_code")
	}
	return query
}

func (r *masterVatCodeRepositoryImpl) FindMasterVatCodeWithFilter(req dto.MasterVatCodePageReqDto) ([]entity.MasterVatCode, error) {
	var result []entity.MasterVatCode

	query := r.DB.Model(&entity.MasterVatCode{})
	query = util.JoinUsers("master_vat_code")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_vat_code")
	}

	query.Order("vat_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func (r *masterVatCodeRepositoryImpl) CountMasterVatCodeWithFilter(req dto.MasterVatCodePageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterVatCodeQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterVatCodeRepositoryImpl) FindMasterVatCodeLatestSyncDate() (*time.Time, error) {
	var result entity.MasterVatCode
	err := r.DB.
		Model(&entity.MasterVatCode{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterVatCodeRepositoryImpl) UpdatesMasterVatCodeFieldsWhere(field map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterVatCode{}).Where(whereClause, args...).Updates(field)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterVatCodeRepositoryImpl) FindMasterVatCodeAll() ([]entity.MasterVatCode, error) {
	var result []entity.MasterVatCode
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterVatCodeRepositoryImpl) UpdateMasterVatCodeAllFields(e *entity.MasterVatCode) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterVatCodeRepositoryImpl) InsertMasterVatCodeList(data []entity.MasterVatCode) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterVatCodeRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
