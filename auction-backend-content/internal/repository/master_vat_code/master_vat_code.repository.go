package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterVatCodeRepositoryImpl struct {
	DB *gorm.DB
}

type MasterVatCodeRepository interface {
	FindMasterVatCodeWithFilter(req dto.MasterVatCodePageReqDto) ([]entity.MasterVatCode, error)
	CountMasterVatCodeWithFilter(req dto.MasterVatCodePageReqDto) (int64, error)
	FindMasterVatCodeLatestSyncDate() (*time.Time, error)
	UpdatesMasterVatCodeFieldsWhere(field map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterVatCodeAll() ([]entity.MasterVatCode, error)
	UpdateMasterVatCodeAllFields(e *entity.MasterVatCode) error
	InsertMasterVatCodeList(data []entity.MasterVatCode) error
	GetDB() *gorm.DB
}

func NewMasterVatCodeRepository(db *gorm.DB) MasterVatCodeRepository {
	return &masterVatCodeRepositoryImpl{DB: db}
}
