package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterRegisterTypeRepositoryImpl) buildMasterRegisterTypeQuery(req dto.MasterRegisterTypePageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterRegisterType{})
	query = util.JoinUsers("master_register_type")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_register_type")
	}
	return query
}

func (r *masterRegisterTypeRepositoryImpl) FindMasterRegisterTypeWithFilter(req dto.MasterRegisterTypePageReqDto) ([]entity.MasterRegisterType, error) {
	var results []entity.MasterRegisterType

	query := r.buildMasterRegisterTypeQuery(req)
	query.Order("attribute_code asc, line_no asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterRegisterTypeRepositoryImpl) CountMasterRegisterTypeWithFilter(req dto.MasterRegisterTypePageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterRegisterTypeQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterRegisterTypeRepositoryImpl) FindMasterRegisterTypeLatestSyncDate() (*time.Time, error) {
	var result entity.MasterRegisterType
	err := r.DB.
		Model(&entity.MasterRegisterType{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterRegisterTypeRepositoryImpl) UpdatesMasterRegisterTypeFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterRegisterType{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterRegisterTypeRepositoryImpl) FindMasterRegisterTypeAll() ([]entity.MasterRegisterType, error) {
	var result []entity.MasterRegisterType
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterRegisterTypeRepositoryImpl) UpdateMasterRegisterTypeAllFields(e *entity.MasterRegisterType) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterRegisterTypeRepositoryImpl) InsertMasterRegisterTypeList(data []entity.MasterRegisterType) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterRegisterTypeRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
