package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *additionalServiceAssetRepositoryImpl) GetAssetByAdditionalServiceIds(additionalServiceIds []int) (map[int][]entity.AdditionalServiceAsset, error) {
	var assets []entity.AdditionalServiceAsset
	if err := r.DB.
		Preload("AssetType").
		Preload("AssetGroup").
		Where("additional_service_id IN ?", additionalServiceIds).Find(&assets).Error; err != nil {
		return nil, err
	}

	result := make(map[int][]entity.AdditionalServiceAsset)
	for _, asset := range assets {
		result[util.Val(asset.AdditionalServiceId)] = append(result[util.Val(asset.AdditionalServiceId)], asset)
	}

	return result, nil
}

func (r *additionalServiceAssetRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
