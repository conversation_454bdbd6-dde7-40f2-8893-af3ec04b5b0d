package db

import (
	"content-service/internal/model/entity"
	"log"

	"gorm.io/gorm"
)

func Seed(database *gorm.DB) {
	// Only seed if table is empty
	var count int64
	database.Model(&entity.MasterPaymentMethod{}).Count(&count)
	if count > 0 {
		log.Println("🟡 Skipping seed: fruits already have data")
		return
	}

	fruits := []entity.MasterPaymentMethod{
		// {Name: "Apple", Color: "Red"},
		// {Name: "Banana", Color: "Yellow"},
		// {Name: "Grape", Color: "Purple"},
		// {Name: "Mango", Color: "Orange"},
	}

	if err := database.Create(&fruits).Error; err != nil {
		log.Fatalf("❌ Failed to seed fruits: %v", err)
	}

	log.Println("✅ Seeded fruits successfully")
}
