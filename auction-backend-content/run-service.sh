#!/bin/bash

# ตรวจสอบว่ามีพารามิเตอร์ถูกส่งมาหรือไม่
if [ -n "$1" ]; then
  service=$1
else
  echo "Select service to run:"
  echo "1) content_service"
  echo "2) system_admin_service"
  echo "3) auction_core_service"
  echo "4) auctioneer_service"
  echo "5) user_service"
  read -p "Enter number: " service
fi

case $service in
  1)
    echo "Running content_service..."
    nodemon --watch . --ext go --exec "go run content_service/cmd/main.go" --signal SIGTERM
    ;;
  2)
    echo "Running system_admin_service..."
    nodemon --watch . --ext go --exec "go run system_admin_service/cmd/main.go" --signal SIGTERM
    ;;
  3)
    echo "Running auction_core_service..."
    nodemon --watch . --ext go --exec "go run auction_core_service/cmd/main.go" --signal SIGTERM
    ;;
  4)
    echo "Running auctioneer_service..."
    nodemon --watch . --ext go --exec "go run auctioneer_service/cmd/main.go" --signal SIGTERM
    ;;
  5)
    echo "Running user_service..."
    nodemon --watch . --ext go --exec "go run user_service/cmd/main.go" --signal SIGTERM
    ;;
  *)
    echo "Invalid selection"
    exit 1
    ;;
esac