version: '3.8'

services:
  db:
    image: postgres:14
    container_name: auction_postgres
    restart: always
    environment:
      POSTGRES_DB: auction_core
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: "!1q2w3e4r"
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5


volumes:
  pgdata:
