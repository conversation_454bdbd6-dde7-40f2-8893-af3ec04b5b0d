app :
  appName: "Auction Core Service"
  domain: "auction-core"
  httpPort: 8181
  allowOrigins: "http://localhost:4200"
public:
    - "api/signin"
dbConfig:
  host: *************
  port: 5432
  dbName: auction_core
  username: postgres
  password: "!1q2w3e4r"
service:
  pgwUrl:
  qrPayUrl:
ErpConfig: 
  token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************.BMMfNCw0dkw0Z0S0fB7Bg0O-5a8GJu7u3xBi8IwqaK8"
  lotSouUrl: "https://systemuat.auct.co.th:3443/api/v1/lot/getlot"
  lotSouLotLineUrl: "https://systemuat.auct.co.th:3443/api/v1/lot/getLotLine"

