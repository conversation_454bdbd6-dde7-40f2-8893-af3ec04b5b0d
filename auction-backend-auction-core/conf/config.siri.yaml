app :
  appName: "Auction Core Service"
  domain: "auction-core"
  httpPort: 8281
  allowOrigins: "http://localhost:4200, https://*************:4200,http://*************:8080,https://*************:4201"
  certFile: "/app/cert/dev.pem"
  keyFile: "/app/cert/dev-key.pem"
public:
    - "api/signin"
dbConfig:
  host: auction-dev-db-micro
  port: 5432
  dbName: auction_core
  username: postgres
  password: "!1q2w3e4r"
service:
  pgwUrl:
  qrPayUrl:
ErpConfig: 
  token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************.BMMfNCw0dkw0Z0S0fB7Bg0O-5a8GJu7u3xBi8IwqaK8"
  lotSouUrl: "https://systemuat.auct.co.th:3443/api/v1/lot/getlot"
  lotSouLotLineUrl: "https://systemuat.auct.co.th:3443/api/v1/lot/getLotLine"

