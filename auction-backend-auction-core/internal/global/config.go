package global

type Config struct {
	App      AppConfig `mapstructure:"app"`
	DB       DBConfig  `mapstructure:"dbConfig"`
	Erp      ErpConfig `mapstructure:"ErpConfig"`
	Internal ErpConfig `mapstructure:"InternalConfig"`
}

type AppConfig struct {
	AppName      string `mapstructure:"appName"`
	HttpPort     int    `mapstructure:"httpPort"`
	Domain       string `mapstructure:"domain"`
	AllowOrigins string `mapstructure:"allowOrigins"`
	CertFile     string `mapstructure:"certFile"`
	KeyFile      string `mapstructure:"keyFile"`
}

type DBConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	DBName   string `mapstructure:"dbName"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
}

type ErpConfig struct {
	Token            string
	LotSouUrl        string
	LotSouLotLineUrl string
}

type InternalConfig struct {
	AssetType string
}
