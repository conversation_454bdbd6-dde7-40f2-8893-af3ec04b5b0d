package dto

import (
	"backend-common-lib/model"
)

type CampaignSurveyPageRespDto[T any] struct {
	model.PagingModel[T]
}

type CampaignSurveyFilterRespDto struct {
	Id               int       `json:"id" example:"0"`
	Type             *string   `json:"type"`
	Name             *string   `json:"name"`
	Event            *string   `json:"event"`
	Finance          *string   `json:"finance"`
	AssetTypes       []*string `json:"assetTypes"`
	AssetGroups      []*string `json:"assetGroups"`
	UserGroups       []*string `json:"userGroups"`
	StartDate        *string   `json:"startDate"`
	EndDate          *string   `json:"endDate"`
	IsActive         bool      `json:"isActive"`
	DisplayLocations []*string `json:"displayLocations"`
}

type CampaignDto struct {
	model.BaseDtoActionBy
	Name          *string `json:"name"`
	EventTypeId   *int    `json:"eventTypeId"`
	EventId       *int    `json:"eventId"`
	FinanceId     *int    `json:"financeId"` //VendorGroupId
	AssetGroupIds []*int  `json:"assetGroups"`
	UserGroupIds  []*int  `json:"userGroups"`
	Description   *string `json:"description"`
	IsLinkUrl     *bool   `column:"is_link_url" json:"isLinkUrl"`
	LinkUrl       *string `column:"link_url" json:"linkUrl"`
	IsUpload      *bool   `column:"is_upload" json:"isUpload"`
	// FilePath         *string                      `column:"file_path" json:"filePath"`
	StartDate        *string                      `json:"startDate"`
	EndDate          *string                      `json:"endDate"`
	IsActive         bool                         `json:"isActive"`
	DisplayLocations []CampaignDisplayLocationDto `json:"displayLocations"`
}

type CampaignDisplayLocationDto struct {
	DisplayLocationId *int ` json:"displayLocationId"`
	IsCheck           bool ` json:"isCheck"`
}

type CampaignStatusReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
