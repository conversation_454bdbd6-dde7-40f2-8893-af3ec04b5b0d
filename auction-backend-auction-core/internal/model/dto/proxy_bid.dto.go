package dto

import (
	"auction-core-service/internal/model/entity"
	"backend-common-lib/model"
	"time"
)

type LotProxyBidSearchReqDto struct {
	BranchId    *int `json:"branchId"`
	FloorId     *int `json:"floorId"`
	AssetTypeId *int `json:"assetTypeId"`
}

type LotProxyBidSearchRespInternalServiceDto struct {
	LotProxyBidSearchRespDto
	SouLotLines     []*entity.LotSouLotLine `json:"souLotLineMap"`
	LotAssetTypeDbs []*entity.LotAssetType  `json:"lotAssetTypeDbs"`
}

type LotProxyBidSearchRespDto struct {
	Id             int                     `json:"id"`
	BranchId       *int                    `json:"branchId"`
	BranchNameTh   *string                 `json:"branchNameTh"`
	BranchNameEn   *string                 `json:"branchNameEn"`
	AuctionDate    *string                 `json:"auctionDate"`    // วันที่ประมูล format: DD/MM/YYYY
	AuctionTime    *string                 `json:"auctionTime"`    // เวลาประมูล format: HH:MM
	FloorId        *int                    `json:"floorId"`        // ลาน ID
	Floor          *string                 `json:"floor"`          // ลาน string จาก master_asset_location_floor
	Name           *string                 `json:"name"`           // ชื่อล็อต
	EventName      *string                 `json:"eventName"`      // ชื่องาน
	AssetTypes     []*LotAssetTypeCountDto `json:"assetTypes"`     // ประเภททรัพย์สินและจำนวน
	ProxyStartDate *string                 `json:"proxyStartDate"` // วันที่เริ่ม Proxy format: DD/MM/YYYY
	ProxyStartTime *string                 `json:"proxyStartTime"` // เวลาเริ่ม Proxy format: HH:MM
	IsActive       bool                    `json:"isActive"`
	FloorStatus    *int                    `json:"floorStatus"`
	Amount         *int                    `json:"amount"`
}

type LotLineProxyBidSearchReqDto struct {
	LotId       int     `json:"lotId"`
	Keyword     *string `json:"keyword"`
	Seq         *string `json:"seq"`
	VendorGroup *string `json:"vendorGroup"`
	model.PagingRequest
}

type LotLineProxyBidSearchRespDto struct {
	Id                int     `json:"id"`
	Order             *string `json:"order"`
	LicensePlateNo    string  `json:"licensePlateNo"`
	BrandDescription  string  `json:"brandDescription"`
	ModelDescription  string  `json:"modelDescription"`
	SellerName        string  `json:"sellerName"`
	AuctionDateString string  `json:"auctionDate"`
	AuctionTimeString string  `json:"auctionTime"`
	Amount            *int    `json:"amount"`
	IsCancelledNoti   bool    `json:"isCancelledNoti"`
	IsLive            bool    `json:"isLive"`
}

type LotLineProxyBidPageRespDto[T any] struct {
	model.PagingModel[T]
}

type ProxyBidListRespDto struct {
	Id              int        `json:"id"`
	BidderID        *string    `json:"bidderID"`
	BidderName      *string    `json:"bidderName"`
	CustomerGroupId *int       `json:"customerGroupId"`
	CustomerGroup   *string    `json:"customerGroup"`
	PhoneNumber     *string    `json:"phoneNumber"`
	ProxyPrice      *float64   `json:"proxyPrice"`
	ProxyStatusId   *int       `json:"proxyStatusId"`
	ProxyStatusTh   *string    `json:"proxyStatusTh"`
	ProxyStatusEn   *string    `json:"proxyStatusEn"`
	CancelType      *string    `json:"cancelType"`
	CreatedDate     *time.Time `json:"createdDate" example:"2020-01-01"`
}
