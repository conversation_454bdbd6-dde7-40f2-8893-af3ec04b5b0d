package dto

import (
	"backend-common-lib/model"
	"time"
)

type HelpRequestDto struct {
	model.BaseDto
	No                  *string    `json:"no"`
	BranchDescriptionTh *string    `json:"branchDescriptionTh"`
	BranchDescriptionEn *string    `json:"branchDescriptionEn"`
	Floor               *string    `json:"floor"`
	HelpRequestReason   *string    `json:"helpRequestReason"`
	HelpRequestReasonId *int       `json:"helpRequestReasonId"`
	StatusHelpRequestId *int       `json:"statusHelpRequestId"`
	StatusHelpRequestTh *string    `json:"statusHelpRequestTh"`
	StatusHelpRequestEn *string    `json:"statusHelpRequestEn"`
	Solution            *string    `json:"solution"`
	SolveIssueBy        *string    `json:"solveIssueBy"`
	SolveIssueDate      *time.Time `json:"solveIssueDate"`
}

type HelpRequestPageReqDto struct {
	AuctionDate         *time.Time `json:"auctionDate"`
	BranchCode          *string    `json:"branchCode"`
	StatusHelpRequestId *int       `json:"statusHelpRequestId"`
	Floor               *string    `json:"floor"`
	HelpRequestReasonId *int       `json:"helpRequestReasonId"`
	model.PagingRequest
}

type HelpRequestUpdateReqDto struct {
	model.BaseDtoActionBy
	StatusHelpRequestId *int    `json:"statusHelpRequestId"`
	Solution            *string `json:"solution"`
}
