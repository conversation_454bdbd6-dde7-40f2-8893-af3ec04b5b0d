package dto

import (
	"backend-common-lib/model"
	"time"
)

type VideoAdsDto struct {
	model.BaseDto
	IsActive           bool       `json:"isActive"`
	VideoName          *string    `json:"videoName"`
	LinkVideo          *string    `json:"linkVideo"`
	EventId            *int       `json:"eventId"`
	EventDescriptionTh *string    `json:"eventDescriptionTh"`
	EventDescriptionEn *string    `json:"eventDescriptionEn"`
	StartDate          *time.Time `json:"startDate"`
	EndDate            *time.Time `json:"endDate"`
	StartTime          *time.Time `json:"startTime"`
	EndTime            *time.Time `json:"endTime"`
}

type VideoAdsRespDto struct {
	model.BaseDto
	IsActive           bool    `json:"isActive"`
	VideoName          *string `json:"videoName"`
	LinkVideo          *string `json:"linkVideo"`
	EventId            *int    `json:"eventId"`
	EventDescriptionTh *string `json:"eventDescriptionTh"`
	EventDescriptionEn *string `json:"eventDescriptionEn"`
	StartDate          *string `json:"startDate"`
	EndDate            *string `json:"endDate"`
	StartTime          *string `json:"startTime"`
	EndTime            *string `json:"endTime"`
}

type VideoAdsPageReqDto struct {
	model.PagingRequest
}

type VideoAdsCreateReqDto struct {
	model.BaseDto
	IsActive  bool    `json:"isActive"`
	VideoName *string `json:"videoName"`
	LinkVideo *string `json:"linkVideo"`
	EventId   *int    `json:"eventId"`
	StartDate *string `json:"startDate"`
	EndDate   *string `json:"endDate"`
	StartTime *string `json:"startTime"`
	EndTime   *string `json:"endTime"`
	ActionBy  *int    `json:"actionBy"`
}
