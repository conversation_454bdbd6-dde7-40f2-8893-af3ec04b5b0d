package dto

import (
	"backend-common-lib/model"
	"time"
)

type PrintSlipDto struct {
	model.BaseDto
	LotSouLotLineId     *int       `json:"lotSouLotLineId"`
	No                  *string    `json:"no"`
	LicensePlateNo      *string    `json:"licensePlateNo"`
	AuctionTag          *string    `json:"auctionTag"`
	SaleDate            *time.Time `json:"saleDate"`
	PrintedDate         *time.Time `json:"printedDate"`
	SalesPrice          *float64   `json:"salesPrice"`
	SoldAmount          *float64   `json:"soldAmount"`
	ProductStatus       *string    `json:"productStatus"`
	Floor               *string    `json:"floor"`
	BranchDescriptionTh *string    `json:"branchDescriptionTh"`
	BranchDescriptionEn *string    `json:"branchDescriptionEn"`
	PrintNo             *int       `json:"printNo"`
	ReasonReprint       *string    `json:"reasonReprint"`
	InvoiceNo           *string    `json:"invoiceNo"`
	CreditNo            *string    `json:"creditNo"`
	Rvu                 *string    `json:"rvu"`
}

type PrintSlipPageReqDto struct {
	BranchCode     *string    `json:"branchCode"`
	Floor          *string    `json:"floor"`
	No             *string    `json:"no"`
	LicensePlateNo *string    `json:"licensePlateNo"`
	AuctionTag     *string    `json:"auctionTag"`
	SaleDate       *time.Time `json:"saleDate"`
	model.PagingRequest
}
