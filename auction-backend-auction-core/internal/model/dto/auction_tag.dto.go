package dto

import (
	"backend-common-lib/model"
)

type AuctionTagReqDto struct {
	model.BaseDtoActionBy
	AuctionId *int     `json:"auctionId" example:"0"`
	IsActive  bool     `json:"isActive" example:"true"`
	BranchIds []*int64 `json:"branchIds" example:"0"`
}

type AuctionTagUpdateReqDto struct {
	model.BaseDtoActionBy
	AuctionId      *int     `json:"auctionId"`
	IsActive       bool     `json:"isActive"`
	BranchIds      []*int64 `json:"branchIds" example:"0"`
	StartTagNumber *int     `json:"startTagNumber"`
	EndTagNumber   *int     `json:"endTagNumber"`
}

type AuctionTagRespDto struct {
	model.BaseDto
	AuctionId            *int                `json:"auctionId" example:"0"`
	AuctionName          *string             `json:"auctionName" example:"0"`
	BranchList           []BranchDto         `json:"branchList"`
	IsActive             bool                `json:"isActive" example:"true"`
	StartTagNumber       *int                `json:"startTagNumber" example:"0"`
	EndTagNumber         *int                `json:"endTagNumber" example:"0"`
	StartDate            *string             `json:"startDate" example:"01/01/2020"`
	EndDate              *string             `json:"endDate" example:"01/01/2020"`
	AuctionAssetTypeList []*AuctionAssetType `json:"assets"`
}

type BranchDto struct {
	ID           *int    `json:"id"`
	BranchCode   *string `json:"BranchCode"`
	BranchNameTh *string `json:"branchNameTh"`
	BranchNameEn *string `json:"branchNameEn"`
}

type AuctionTagPageRespDto[T any] struct {
	model.PagingModel[T]
}
