package dto

import (
	"backend-common-lib/model"
	"time"
)

type ProxyBidCancelRequestDto struct {
	model.BaseDto
	Floor               *string    `json:"floor"`
	No                  *string    `json:"no"`
	BranchDescriptionTh *string    `json:"branchDescriptionTh"`
	BranchDescriptionEn *string    `json:"branchDescriptionEn"`
	AuctionDate         *time.Time `json:"auctionDate"`
	CustomerName        *string    `json:"customerName"`
	PhoneNumber         *string    `json:"phoneNumber"`
	Product             *string    `json:"product"`
	Brand               *string    `json:"brand"`
	Model               *string    `json:"model"`
	AuctionTagNumber    *string    `json:"auctionTagNumber"`
	ProxyDate           *time.Time `json:"proxyDate"`
	ProxyStatus         *string    `json:"proxyStatus"`
	ActionStatus        *string    `json:"actionStatus"`
	ApproveBy           *string    `json:"approveBy"`
	ApproveDate         *time.Time `json:"approveDate"`
	CancelReason        *string    `json:"cancelReason"`
}

type ProxyBidCancelRequestPageReqDto struct {
	AuctionDate   *time.Time `json:"auctionDate"`
	BranchCode    *string    `json:"branchCode"`
	ProxyStatusId *int       `json:"proxyStatusId"`
	Floor         *string    `json:"floor"`
	ActionStatus  *string    `json:"actionStatus"`
	model.PagingRequest
}

type ProxyBidCancelRequestUpdateReqDto struct {
	model.BaseDto
	CancelReasonId *int `json:"cancelReasonId"`
	ActionBy       *int `json:"actionBy"`
}
