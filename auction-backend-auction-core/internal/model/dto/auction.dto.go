package dto

import (
	"auction-core-service/internal/model/entity"
	"backend-common-lib/model"
	"time"
)

type AuctionReqDto struct {
	model.BaseDtoActionBy
	AuctionTypeCode          *string             `json:"auctionTypeCode" example:"0"`
	AuctionTypeDescriptionTh *string             `json:"auctionTypeDescriptionTh" example:"0"`
	AuctionTypeDescriptionEn *string             `json:"auctionTypeDescriptionEn" example:"0"`
	AuctionName              *string             `json:"auctionName" example:"John Doe"`
	Description              *string             `json:"description" example:"JohnDoe"`
	IsActive                 bool                `json:"isActive" example:"true"`
	StartDate                *string             `json:"startDate" example:"01/01/2020"`
	EndDate                  *string             `json:"endDate" example:"01/01/2020"`
	EventId                  *int                `json:"eventId"`
	IsAutoCollateralRefund   bool                `json:"isAutoCollateralRefund"`
	AuctionAssetTypeList     []*AuctionAssetType `json:"AuctionAssetType"`
}
type AuctionRespDto struct {
	Id                       int                 `json:"id" example:"0"`
	AuctionTypeCode          *string             `json:"auctionTypeCode" example:"0"`
	AuctionTypeDescriptionTh *string             `json:"auctionTypeDescriptionTh" example:"0"`
	AuctionTypeDescriptionEn *string             `json:"auctionTypeDescriptionEn" example:"0"`
	AuctionName              *string             `json:"auctionName" example:"John Doe"`
	Description              *string             `json:"description" example:"JohnDoe"`
	IsActive                 bool                `json:"isActive" example:"true"`
	EventId                  *int                `json:"eventId"`
	IsAutoCollateralRefund   bool                `json:"isAutoCollateralRefund"`
	StartDate                *string             `json:"startDate" example:"01/01/2020"`
	EndDate                  *string             `json:"endDate" example:"01/01/2020"`
	CreatedDate              *time.Time          `json:"createdDate" example:"01/01/2020"`
	CreatedBy                *string             `json:"createdBy" example:"JohnDoe"`
	UpdatedDate              *time.Time          `json:"updatedDate" example:"01/01/2020"`
	UpdatedBy                *string             `json:"updatedBy" example:"JohnDoe"`
	AuctionAssetTypeList     []*AuctionAssetType `json:"assets"`
}

type AuctionRespInternalServiceDto struct {
	AuctionRespDto
	ActionAssetDbs                   []*entity.AuctionAsset           `json:"actionAssetDbs"`
	TempMapAuctionAmountLimitList    map[int][]*AuctionAmountLimit    `json:"tempMapAuctionAmountLimitList"`
	TempMapAuctionBidStepList        map[int][]*AuctionBidStep        `json:"tempMapAuctionBidStepList"`
	TempMapAuctionFeeList            map[int][]*AuctionFee            `json:"tempMapAuctionFeeList"`
	TempMapAuctionMinimumPaymentList map[int][]*AuctionMinimumPayment `json:"tempMapAuctionMinimumPaymentList"`
}

type AuctionPageReqDto struct {
	PageNumber               int     `json:"pageNumber" example:"0"`
	PageLimit                int     `json:"pageLimit" example:"0"`
	AuctionName              *string `json:"auctionName" example:"John Doe"`
	AuctionTypeCode          *string `json:"auctionTypeCode" example:"0"`
	AuctionTypeDescriptionTh *string `json:"auctionTypeDescriptionTh" example:"0"`
	AuctionTypeDescriptionEn *string `json:"auctionTypeDescriptionEn" example:"0"`
	StartDate                *string `json:"startDate" example:"01/01/2020"`
	EndDate                  *string `json:"endDate" example:"01/01/2020"`
}
type AuctionPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type AuctionSearchRespDto struct {
	Id                       int        `json:"id" example:"0"`
	AuctionType              *string    `json:"auctionTypeCode" example:"0"`
	AuctionTypeDescriptionTh *string    `json:"auctionTypeDescriptionTh" example:"0"`
	AuctionTypeDescriptionEn *string    `json:"auctionTypeDescriptionEn" example:"0"`
	AuctionName              *string    `json:"auctionName" example:"John Doe"`
	Description              *string    `json:"description" example:"JohnDoe"`
	StartDate                *string    `json:"startDate" example:"01/01/2020"`
	EndDate                  *string    `json:"endDate" example:"01/01/2020"`
	CreatedDate              *time.Time `json:"createdDate" example:"01/01/2020"`
	CreatedBy                *string    `json:"createdBy" example:"JohnDoe"`
	UpdatedDate              *time.Time `json:"updatedDate" example:"01/01/2020"`
	UpdatedBy                *string    `json:"updatedBy" example:"JohnDoe"`
	IsActive                 bool       `json:"isActive" example:"true"`
}

type AuctionAmountLimit struct {
	Id                      int      `json:"id"`
	AuctionId               int      `json:"auctionId"`
	AuctionAssetId          int      `json:"auctionAssetId"`
	CustomerGroupId         string   `json:"customerGroupId"`
	CustomerGroupName       *string  `json:"customerGroupName"`
	DepositAmountTypeCode   *string  `json:"depositAmountTypeCode"`
	DepositAmount           *float64 `json:"depositAmount"`
	CreditAmount            *float64 `json:"creditAmount"`
	AdditionalAmountPercent *float64 `json:"additionalAmountPercent"`
	IsNoLimit               *bool    `json:"isNoLimit"`
	ItemLimit               *int     `json:"itemLimit"`
	CollateralId            *int     `json:"collateralId"` // Per Car,One Time
}

type AuctionSubAssetType struct {
	Id                        int                      `json:"id"`
	AuctionId                 int                      `json:"auctionId"`
	AssetTypeId               int                      `json:"assetTypeId"`
	AssetGroupId              *int                     `json:"assetGroupId"`
	AssetName                 *string                  `json:"assetName" example:"Buyer"`
	AssetTypeCode             *string                  `json:"assetTypeCode" example:"Buyer"`
	AssetGroupCode            *string                  `json:"assetGroupCode" example:"Buyer"`
	IsDeposit                 bool                     `json:"isDeposit"`
	IsCredit                  bool                     `json:"isCredit"`
	IsAdditional              bool                     `json:"isAdditional"`
	IsItemLimit               bool                     `json:"isItemLimit"`
	IncrementalUnit           *string                  `json:"incrementalUnit"`    // Amount, Percent
	ReferenceIncrement        *string                  `json:"referenceIncrement"` // Opening Price, Current Price
	AuctionAmountLimitList    []*AuctionAmountLimit    `json:"amountLimits"`
	AuctionBidStepList        []*AuctionBidStep        `json:"bidSteps"`
	AuctionFeeList            []*AuctionFee            `json:"fees"`
	AuctionMinimumPaymentList []*AuctionMinimumPayment `json:"minimumPayments"`
}

type AuctionAssetType struct {
	Id                        int                      `json:"id,omitempty"`
	AuctionId                 int                      `json:"auctionId,omitempty"`
	AssetTypeId               int                      `json:"assetTypeId,omitempty"`
	AssetName                 *string                  `json:"assetName,omitempty" example:"Buyer"`
	AssetTypeCode             *string                  `json:"assetTypeCode,omitempty" example:"Buyer"`
	DescriptionTh             *string                  `json:"descriptionTh,omitempty"`
	DescriptionEn             *string                  `json:"descriptionEn,omitempty"`
	IsDeposit                 bool                     `json:"isDeposit,omitempty"`
	IsCredit                  bool                     `json:"isCredit,omitempty"`
	IsAdditional              bool                     `json:"isAdditional,omitempty"`
	IsItemLimit               bool                     `json:"isItemLimit,omitempty"`
	IncrementalUnit           *string                  `json:"incrementalUnit,omitempty"`
	ReferenceIncrement        *string                  `json:"referenceIncrement,omitempty"`
	AuctionAmountLimitList    []*AuctionAmountLimit    `json:"amountLimits,omitempty"`
	AuctionBidStepList        []*AuctionBidStep        `json:"bidSteps,omitempty"`
	AuctionFeeList            []*AuctionFee            `json:"fees,omitempty"`
	AuctionMinimumPaymentList []*AuctionMinimumPayment `json:"minimumPayments,omitempty"`
	AuctionSubAssetTypeList   []*AuctionSubAssetType   `json:"subAssets,omitempty"`
}

type AuctionAssetTypeList struct {
	AuctionAssetTypeList []*AuctionAssetType `json:"AuctionAssetTypeList"`
}

type AuctionSettingReqDto struct {
	model.BaseDtoActionBy
	AuctionTypeCode        *string               `json:"auctionTypeCode"`
	AuctionName            *string               `json:"auctionName" example:"John Doe"`
	Description            *string               `json:"description" example:"JohnDoe"`
	StartDate              *string               `json:"startDate" example:"01/01/2020"`
	EndDate                *string               `json:"endDate" example:"01/01/2020"`
	IsActive               bool                  `json:"isActive" example:"true"`
	EventId                *int                  `json:"eventId"`
	IsAutoCollateralRefund bool                  `json:"isAutoCollateralRefund"`
	Assets                 []*AuctionAssetReqDto `json:"assets"`
}

type AuctionAssetReqDto struct {
	Id                 int                      `json:"id"`
	AuctionId          int                      `json:"auctionId"`
	AssetTypeId        int                      `json:"assetTypeId"`
	AssetGroupId       *int                     `json:"assetGroupId"`
	IsDeposit          bool                     `json:"isDeposit"`
	IsCredit           bool                     `json:"isCredit"`
	IsAdditional       bool                     `json:"isAdditional"`
	IsItemLimit        bool                     `json:"isItemLimit"`
	IsCollateral       bool                     `json:"isCollateral"`       // Per Car,One Time
	IncrementalUnit    *string                  `json:"incrementalUnit"`    // Amount, Percent
	ReferenceIncrement *string                  `json:"referenceIncrement"` // Opening Price, Current Price
	SubAssets          []*AuctionAssetReqDto    `json:"subAssets"`
	AmountLimits       []*AuctionAmountLimit    `json:"amountLimits"`
	BidSteps           []*AuctionBidStep        `json:"bidSteps"`
	Fees               []*AuctionFee            `json:"fees"`
	MinimumPayments    []*AuctionMinimumPayment `json:"minimumPayments"`
}

type AuctionBidStep struct {
	Id             int      `json:"id"`
	AuctionId      int      `json:"auctionId"`
	AuctionAssetId int      `json:"auctionAssetId"`
	From           *float64 `json:"from"`
	To             *float64 `json:"to"`
	Amount         *float64 `json:"amount"`
}

type AuctionFee struct {
	Id                int      `json:"id"`
	AuctionId         int      `json:"auctionId"`
	AuctionAssetId    int      `json:"auctionAssetId"`
	VendorGroupId     *int     `json:"vendorGroupId"`
	RegisterTypeId    *int     `json:"registerTypeId"`
	SalesPrice        *float64 `json:"salesPrice"`
	SoldAmount        *float64 `json:"soldAmount"`
	CcFrom            *int     `json:"ccFrom"`
	CcTo              *int     `json:"ccTo"`
	RegisterTypeCarId *int     `json:"registerTypeCarId"`
	RegisteredYear    *int     `json:"registeredYear"`
	Fee               *float64 `json:"fee"`
	StartDate         *string  `json:"startDate" example:"01/01/2020"`
	EndDate           *string  `json:"endDate" example:"01/01/2020"`
}

type AuctionMinimumPayment struct {
	Id                 int      `json:"id"`
	AuctionId          int      `json:"auctionId"`
	AuctionAssetId     int      `json:"auctionAssetId"`
	VendorId           *int     `json:"vendorId"`
	MinimumPaymentType *string  `json:"minimumPaymentType"`
	MinimumPercent     *int     `json:"minimumPercent"`
	MinimumAmount      *float64 `json:"minimumAmount"`
}

type CustomerGroups struct {
	Id            int     `column:"id" json:"id"`
	DescriptionTh *string `column:"description_th" json:"descriptionTh"`
}
