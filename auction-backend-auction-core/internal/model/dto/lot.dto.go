package dto

import (
	"auction-core-service/internal/model/entity"
	"backend-common-lib/model"
	"bytes"
	"time"
)

type LotDto struct {
	model.BaseDtoActionBy
	BranchId         *int                  `json:"branchId"`
	AuctionDate      *string               `json:"auctionDate"`
	AuctionTime      *string               `json:"auctionTime"`
	FloorId          *int                  `json:"floorId"`
	AssetTypeList    []*int                `json:"assetTypeList"`
	AuctionTypeId    *int                  `json:"auctionTypeId"`
	AuctionId        *int                  `json:"auctionId"`
	Event            *string               `json:"event"`
	Name             *string               `json:"name"`
	Description      *string               `json:"description"`
	IsActive         bool                  `json:"isActive"`
	FloorStatus      *int                  `json:"floorStatus"`
	AuctionChannel   *int                  `json:"auctionChannel"`
	ActiveOptionList []*LotActiveOptionDto `json:"activeOptionList"`
	ShowStartDate    *string               `json:"showStartDate"`
	ShowStartTime    *string               `json:"showStartTime"`
	ShowEndDate      *string               `json:"showEndDate"`
	ShowEndTime      *string               `json:"showEndTime"`
	ProxyStartDate   *string               `json:"proxyStartDate"`
	ProxyStartTime   *string               `json:"proxyStartTime"`
	StreamingList    []*LotStreamingDto    `json:"streamingList"`
	SouList          []*LotSouDto          `json:"souList"`
	LatestSyncDate   *time.Time            `json:"latestSyncDate"`
}

type LotActiveOptionDto struct {
	Id                 int  `json:"id"`
	LotId              *int `json:"lotId"`
	ConfigFeatureLotId *int `json:"configFeatureLotId"`
}

type LotStreamingDto struct {
	Id            int     `json:"id"`
	LotId         *int    `json:"lotId"`
	Order         *int    `json:"order"`
	StreamingName *string `json:"streamingName"`
	StreamingLink *string `json:"streamingLink"`
}

type LotSouFromErpReqDto struct {
	AuctionDate *string `json:"auctionDate"`
	BranchCode  *string `json:"branchCode"`
}

type LotSouFromErpRespDto struct {
	CompanyCode     *string `json:"CompanyCode"`
	DocumentNo      *string `json:"Document_No"`
	AuctionDate     *string `json:"Auction_Date"`
	AuctionTime     *string `json:"Auction_Time"`
	DueDate         *string `json:"Due_Date"`
	NoOfNewCar      *int    `json:"No_Of_New_Car"`
	BranchCode      *string `json:"Branch_Code"`
	BranchNameTH    *string `json:"Branch_Name_TH"`
	BranchNameEN    *string `json:"Branch_Name_EN"`
	LocationCode    *string `json:"Location_Code"`
	Floor           *string `json:"Floor"`
	AssetTypeCode   *string `json:"Asset_Type_Code"`
	AssetTypeNameTH *string `json:"Asset_Type_Name_TH"`
	AssetTypeNameEN *string `json:"Asset_Type_Name_EN"`
	NoOfOldCar      *int    `json:"No_Of_Old_Car"`
	EventCode       *string `json:"Event_Code"`
	Status          *string `json:"Status"`
	AuctionStatus   *string `json:"Auction_Status"`
	RemarkTH        *string `json:"Remark_TH"`
	RemarkEN        *string `json:"Remark_EN"`
	RemarkExtra     *string `json:"Remark_Extra"`
	ActionType      *string `json:"Action_Type"`
}

type LotSouDto struct {
	Id              int     `json:"id"`
	LotId           *int    `json:"lotId"`
	CompanyCode     *string `json:"companyCode"`
	DocumentNo      *string `json:"documentNo"`
	AuctionDate     *string `json:"auctionDate"`
	AuctionTime     *string `json:"auctionTime"`
	DueDate         *string `json:"dueDate"`
	NoOfNewCar      *int    `json:"noOfNewCar"`
	BranchCode      *string `json:"branchCode"`
	BranchNameTh    *string `json:"branchNameTh"`
	BranchNameEn    *string `json:"branchNameEn"`
	LocationCode    *string `json:"locationCode"`
	Floor           *string `json:"floor"`
	AssetTypeCode   *string `json:"assetTypeCode"`
	AssetTypeNameTh *string `json:"assetTypeNameTh"`
	AssetTypeNameEn *string `json:"assetTypeNameEn"`
	NoOfOldCar      *int    `json:"noOfOldCar"`
	EventCode       *string `json:"eventCode"`
	Status          *string `json:"status"`
	AuctionStatus   *string `json:"auctionStatus"`
	RemarkTh        *string `json:"remarkTh"`
	RemarkEn        *string `json:"remarkEn"`
	RemarkExtra     *string `json:"remarkExtra"`
	ActionType      *string `json:"actionType"`
}

type LotSouLotLineFromErpRespDto struct {
	AuctionDate               *string  `json:"Auction_Date"`
	DocumentNo                *string  `json:"Document_No"`
	AssetCode                 *string  `json:"Asset_Code"`
	Floor                     *string  `json:"Floor"`
	AuctCode                  *string  `json:"Auct_code"`
	AuctionNo                 *int     `json:"Auction_No"`
	CityCode                  *string  `json:"City_Code"`
	AssetGroupCode            *string  `json:"Asset_Group_Code"`
	BrandDescription          *string  `json:"Brand_descrip"`
	ModelDescription          *string  `json:"Model_descrip"`
	Model                     *string  `json:"Model"`
	SubModel                  *string  `json:"Sub_Model"`
	SubKey                    *string  `json:"Sub_Key"`
	EngineSize                *string  `json:"Engine_Size"`
	LicensePlateNo            *string  `json:"License_Plate_No"`
	CityDescription           *string  `json:"City_Descrip"`
	RegistrationYear          *string  `json:"Regist_Year"`
	YearOfManufacture         *string  `json:"Year_of_Manufacture"`
	ColorInCopy               *string  `json:"Color_in_copy"`
	TaxDate                   *string  `json:"Tax_date"`
	VendorGroup               *string  `json:"Vendor_Group"`
	Mile                      *int     `json:"Mile"`
	SalesPrice                *float64 `json:"Sales_Price"`
	SoldAmount                *float64 `json:"Sold_Amount"`
	VatPercentage             *float64 `json:"Vat_Percentage"`
	AssetYear                 *int     `json:"Asset_Year"`
	BranchDescription         *string  `json:"Branch_Descrip"`
	ContractNo                *string  `json:"Contract_No"`
	ChassisNo                 *string  `json:"Chassis_No"`
	EngineNo                  *string  `json:"Engine_No"`
	AssetType                 *string  `json:"Asset_type"`
	BranchCode                *string  `json:"Branch_Code"`
	SellerCode                *string  `json:"Seller_Code"`
	SellerName                *string  `json:"Seller_Name"`
	FuelTap                   *string  `json:"Full_tap"`
	Gear                      *string  `json:"Gear"`
	GearName                  *string  `json:"Gear_Name"`
	Unque                     *string  `json:"Unque"`
	CarTypeCon                *string  `json:"Car_Type_con"`
	PointGrade1Final          *int     `json:"Point_Grade1_final"`
	PointGrade2Con            *int     `json:"Point_Grade2_con"`
	PointGrade3Con            *int     `json:"Point_Grade3_con"`
	FinanceZipFilename        *string  `json:"FinanceZipFileName"`
	SellerOffer               *string  `json:"Seller_Offer"`
	AuctionTime               *string  `json:"Auction_Time"`
	Remark                    *string  `json:"remark"`
	SpecialAuction            *string  `json:"Special_Auction"`
	PersonalAssessmentAuction *string  `json:"Personal_Assessment_Auction"`
	PersonalAssessment        *string  `json:"Personal_Assessment"`
	Tool                      *string  `json:"Tool_"`
	SaleRounds                *int     `json:"SaleRounds"`
}

type LotPageRespDto[T any] struct {
	model.PagingModel[T]
}

type LotPageReqDto struct {
	BranchId      *int    `json:"branchId"`
	AuctionDate   *string `json:"auctionDate"`
	AuctionTime   *string `json:"auctionTime"`
	FloorId       *int    `json:"floorId"`
	AssetTypeId   *int    `json:"assetTypeId"`
	AuctionTypeId *int    `json:"auctionTypeId"`
	Name          *string `json:"name"`
	EventName     *string `json:"eventName"`
	Description   *string `json:"description"`
	IsActive      bool    `json:"isActive"`
}

type LotSearchReqDto struct {
	StartDate   *string `json:"startDate"`
	EndDate     *string `json:"endDate"`
	BranchId    *int    `json:"branchId"`
	FloorStatus *int    `json:"floorStatus"`
	IsActive    *bool   `json:"isActive"`
}

type LotSearchRespDto struct {
	Id             int                     `json:"id"`
	BranchId       *int                    `json:"branchId"`
	BranchNameTh   *string                 `json:"branchNameTh"`
	BranchNameEn   *string                 `json:"branchNameEn"`
	AuctionDate    *string                 `json:"auctionDate"`    // วันที่ประมูล format: DD/MM/YYYY
	AuctionTime    *string                 `json:"auctionTime"`    // เวลาประมูล format: HH:MM
	FloorId        *int                    `json:"floorId"`        // ลาน ID
	Floor          *string                 `json:"floor"`          // ลาน*string จาก master_asset_location_floor
	Name           *string                 `json:"name"`           // ชื่อล็อต
	EventName      *string                 `json:"eventName"`      // ชื่องาน
	AssetTypes     []*LotAssetTypeCountDto `json:"assetTypes"`     // ประเภททรัพย์สินและจำนวน
	ProxyStartDate *string                 `json:"proxyStartDate"` // วันที่เริ่ม Proxy format: DD/MM/YYYY
	ProxyStartTime *string                 `json:"proxyStartTime"` // เวลาเริ่ม Proxy format: HH:MM
	IsActive       bool                    `json:"isActive"`
	FloorStatus    *int                    `json:"floorStatus"`
}

type LotSearchRespInternalServiceDto struct {
	LotSearchRespDto
	SouLotLines     []*entity.LotSouLotLine `json:"souLotLineMap"`
	LotAssetTypeDbs []*entity.LotAssetType  `json:"lotAssetTypeDbs"`
}

type LotAssetTypeCountDto struct {
	AssetTypeCode *string                 `json:"assetTypeCode"` // รหัสประเภททรัพย์สิน (A, B, C, ...)
	DescriptionTh *string                 `json:"descriptionTh"` // ชื่อประเภททรัพย์สินภาษาไทย (รถยนต์, ...)
	DescriptionEn *string                 `json:"descriptionEn"` // ชื่อประเภททรัพย์สินภาษาอังกฤษ
	Count         *int                    `json:"count"`         // จำนวนของประเภทนี้ในล็อต
	SubAssetType  []*LotAssetTypeCountDto `json:"subAssetType"`
}

type LotSearchPageRespDto[T any] struct {
	model.PagingModel[T]
}

type SouLotLineDto struct {
	Id                     int      `json:"id"`
	Floor                  *string  `json:"floor"`
	AuctionNo              *int     `json:"auctionNo"`
	Remark                 *string  `json:"remark"`
	SellerName             *string  `json:"sellerName"`
	BrandDescription       *string  `json:"brandDescription"`
	ModelDescription       *string  `json:"modelDescription"`
	AssetType              *string  `json:"assetType"`
	AssetTypeDescriptionTh *string  `json:"assetTypeDescriptionTh" map:"MasterAssetType.DescriptionTh"`
	AssetTypeDescriptionEn *string  `json:"assetTypeDescriptionEn" map:"MasterAssetType.DescriptionEn"`
	LicensePlateNo         *string  `json:"licensePlateNo"`
	CityDescription        *string  `json:"cityDescription"`
	AuctionStatus          *string  `json:"auctionStatus"`
	ProductStatus          *string  `json:"productStatus"`
	DupAuctionNos          []string `json:"dupAuctionNos"`
}

type SouLotLinePageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type SouLotLinePageReqDto struct {
	LotId         *int    `json:"lotId"`
	Keyword       *string `json:"keyword"`
	SellerName    *string `json:"sellerName"`
	AssetType     *string `json:"assetType"`
	AuctionStatus *string `json:"auctionStatus"`
	IsShowRemark  bool    `json:"isShowRemark"`
	model.PagingRequest
}

type SouLotLineExportDto struct {
	Filename   *string       `json:"filename"`
	ExcelBytes *bytes.Buffer `json:"excelBytes"`
}

type SouLotLineUploadDto struct {
	model.BaseDtoActionBy
	ExcelByte []byte `json:"excelByte"`
}
