package dto

import (
	"backend-common-lib/model"
)

type HelpRequestReasonDto struct {
	model.BaseDto
	Reason   *string   `json:"reason"`
	Roles    []*string `json:"roles"`
	IsActive bool      `json:"isActive"`
}

type HelpRequestReasonPageRespDto[T any] struct {
	model.PagingModel[T]
}

type HelpRequestReasonPageReqDto struct {
	model.PagingRequest
}

type HelpRequestReasonInternalReqDto struct {
	model.BaseDtoActionBy
	Reason   *string `json:"reason"`
	RoleIds  []*int  `json:"roleIds"`
	IsActive bool    `json:"isActive"`
}

type HelpRequestReasonRespDto struct {
	Id       int     `json:"id" ignore:"true"`
	Reason   *string `json:"reason"`
	RoleIds  []*int  `json:"roleIds"`
	IsActive bool    `json:"isActive"`
}

type HelpRequestReasonStatusReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
