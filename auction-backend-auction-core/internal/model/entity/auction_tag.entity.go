package entity

import (
	"backend-common-lib/model"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

type AuctionTag struct {
	*model.BaseEntity
	AuctionId       int             `gorm:"column:auction_id" json:"auctionId" example:"0"`
	BranchIds       pq.Int64Array   `gorm:"type:int[];column:branch_ids" json:"branchIds"`
	IsActive        bool            `gorm:"column:is_active" json:"isActive"`
	StartTagNumber  *int            `gorm:"column:start_tag_number;" json:"startTagNumber" example:"0"`
	EndTagNumber    *int            `gorm:"column:end_tag_number;" json:"endTagNumber" example:"0"`
	DeletedDate     *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
	AuctionName     *string         `gorm:"column:auction_name;->" json:"auctionName" example:"0"`
	BranchId        *int            `gorm:"column:branch_id;->" json:"branchId" example:"0"`
	BranchCode      *string         `gorm:"column:branch_code;->" json:"branchCode" example:"0"`
	BranchNameTh    *string         `gorm:"column:branch_name_th;->" json:"branchNameTh" example:"0"`
	BranchNameEn    *string         `gorm:"column:branch_name_en;->" json:"branchNameEn" example:"0"`
	AssetTypeId     *int            `gorm:"column:asset_type_id;->" json:"assetTypeId" example:"0"`
	AssetTypeCode   *string         `gorm:"column:asset_type_code;->" json:"assetTypeCode" example:"0"`
	AssetTypeNameTh *string         `gorm:"column:asset_type_desc_th;->" json:"assetTypeDescTh" example:"0"`
	AssetTypeNameEn *string         `gorm:"column:asset_type_desc_en;->" json:"assetTypeDescEn" example:"0"`
	CreatedUser     *string         `gorm:"column:created_user;->" json:"createdUser"`
	UpdatedUser     *string         `gorm:"column:updated_user;->" json:"updatedUser"`
}

func (AuctionTag) TableName() string {
	return "auction_tag"
}
