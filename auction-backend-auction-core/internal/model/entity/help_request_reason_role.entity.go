package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type HelpRequestReasonRole struct {
	*model.BaseEntity
	HelpRequestReasonId *int               `column:"help_request_reason_id" json:"helpRequestReasonId"`
	RoleId              *int               `column:"role_id" json:"roleId"`
	DeletedDate         *gorm.DeletedAt    `column:"deleted_date" json:"deletedDate"`
	RoleForJoin         *model.RoleForJoin `gorm:"foreignKey:RoleId;references:Id;->"`
}

func (HelpRequestReasonRole) TableName() string {
	return "help_request_reason_role"
}
