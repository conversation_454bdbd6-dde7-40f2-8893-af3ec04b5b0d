package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type CampaignAssetGroup struct {
	*model.BaseEntity
	CampaignId        *int                     `column:"campaign_id" json:"campaignId"`
	AssetGroupId      *int                     `column:"asset_group_id" json:"assetGroupId"`
	DeletedDate       *gorm.DeletedAt          `gorm:"column:deleted_date" json:"deletedDate"`
	AssetGroupForJoin *model.AssetGroupForJoin `gorm:"foreignKey:AssetGroupId;references:ID;->"`
}

func (CampaignAssetGroup) TableName() string {
	return "campaign_asset_group"
}
