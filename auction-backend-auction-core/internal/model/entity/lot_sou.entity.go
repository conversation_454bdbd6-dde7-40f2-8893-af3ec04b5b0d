package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type LotSou struct {
	*model.BaseEntity
	LotId           int             `column:"lot_id" json:"lotId"`
	CompanyCode     string          `column:"company_code" json:"companyCode"`
	DocumentNo      string          `column:"document_no" json:"documentNo"`
	AuctionDate     time.Time       `column:"auction_date" json:"auctionDate"`
	AuctionTime     time.Time       `column:"auction_time" json:"auctionTime"`
	DueDate         time.Time       `column:"due_date" json:"dueDate"`
	NoOfNewCar      int             `column:"no_of_new_car" json:"noOfNewCar"`
	BranchCode      string          `column:"branch_code" json:"branchCode"`
	BranchNameTh    string          `column:"branch_name_th" json:"branchNameTh"`
	BranchNameEn    string          `column:"branch_name_en" json:"branchNameEn"`
	LocationCode    string          `column:"location_code" json:"locationCode"`
	Floor           string          `column:"floor" json:"floor"`
	AssetTypeCode   string          `column:"asset_type_code" json:"assetTypeCode"`
	AssetTypeNameTh string          `column:"asset_type_name_th" json:"assetTypeNameTh"`
	AssetTypeNameEn string          `column:"asset_type_name_en" json:"assetTypeNameEn"`
	NoOfOldCar      int             `column:"no_of_old_car" json:"noOfOldCar"`
	EventCode       *string         `column:"event_code" json:"eventCode"`
	Status          string          `column:"status" json:"status"`
	AuctionStatus   string          `column:"auction_status" json:"auctionStatus"`
	RemarkTh        string          `column:"remark_th" json:"remarkTh"`
	RemarkEn        string          `column:"remark_en" json:"remarkEn"`
	RemarkExtra     string          `column:"remark_extra" json:"remarkExtra"`
	ActionType      *string         `column:"action_type" json:"actionType"`
	IsActive        bool            `column:"is_active" json:"isActive"`
	IsDeletedByErp  bool            `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate  *time.Time      `column:"latest_sync_date" json:"latestSyncDate"`
	DeletedDate     *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
}

func (LotSou) TableName() string {
	return "lot_sou"
}
