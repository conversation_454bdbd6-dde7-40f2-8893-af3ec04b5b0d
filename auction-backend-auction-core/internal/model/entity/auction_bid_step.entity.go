package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type AuctionBidStep struct {
	*model.BaseEntity
	AuctionId      int             `column:"auction_id" json:"auctionId"`
	AuctionAssetId int             `column:"auction_asset_id" json:"auctionAssetId"`
	From           *float64        `column:"from" json:"from"`
	To             *float64        `column:"to" json:"to"`
	Amount         *float64        `column:"amount" json:"amount"`
	DeletedDate    *gorm.DeletedAt `column:"deleted_date" json:"deletedDate"`
}

func (AuctionBidStep) TableName() string {
	return "auction_bid_step"
}
