package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type AuctionAsset struct {
	*model.BaseEntity
	AuctionId          int                      `column:"auction_id" json:"auctionId"`
	AssetTypeId        int                      `column:"asset_type_id" json:"assetTypeId"`
	AssetTypeCode      string                   `gorm:"column:asset_type_code;->" json:"assetTypeCode"`
	DescriptionTh      string                   `gorm:"column:description_th;->" json:"descriptionTh"`
	DescriptionEn      string                   `gorm:"column:description_en;->" json:"descriptionEn"`
	AssetGroupId       *int                     `column:"asset_group_id" json:"assetGroupId"`
	IsDeposit          bool                     `column:"is_deposit" json:"isDeposit"`
	IsCredit           bool                     `column:"is_credit" json:"isCredit"`
	IsAdditional       bool                     `column:"is_additional" json:"isAdditional"`
	IsItemLimit        bool                     `column:"is_item_limit" json:"isItemLimit"`
	IsCollateral       bool                     `column:"is_collateral" json:"isCollateral"`
	IncrementalUnit    *string                  `column:"incremental_unit" json:"incrementalUnit"`
	ReferenceIncrement *string                  `column:"reference_increment" json:"referenceIncrement"`
	DeletedDate        *gorm.DeletedAt          `column:"deleted_date" json:"deletedDate"`
	SubAssets          []AuctionAsset           `gorm:"-"`
	AmountLimits       []AuctionAmountLimit     `gorm:"-"`
	BidSteps           []AuctionBidStep         `gorm:"-"`
	Fees               []AuctionFee             `gorm:"-"`
	MinimumPayments    []AuctionMinimumPayment  `gorm:"-"`
	AssetTypeForJoin   *model.AssetTypeForJoin  `gorm:"foreignKey:AssetTypeId;references:ID;->"`
	AssetGroupForJoin  *model.AssetGroupForJoin `gorm:"foreignKey:AssetGroupId;references:ID;->"`
}

func (AuctionAsset) TableName() string {
	return "auction_asset"
}
