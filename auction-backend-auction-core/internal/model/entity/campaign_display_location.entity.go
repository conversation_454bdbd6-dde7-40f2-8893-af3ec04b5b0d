package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type CampaignDisplayLocation struct {
	*model.BaseEntity
	CampaignId        *int            `column:"campaign_id" json:"campaignId"`
	DisplayLocationId *int            `column:"display_location_id" json:"displayLocationId"`
	IsCheck           bool            `column:"is_check" json:"isCheck"`
	DeletedDate       *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
	DisplayLocation   *string         `gorm:"column:value_string2;->" json:"displayLocation"`
}

func (CampaignDisplayLocation) TableName() string {
	return "campaign_display_location"
}
