package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type LotSouLotLine struct {
	*model.BaseEntity
	LotId                     int                     `column:"lot_id" json:"lotId"`
	LotSouId                  *int                    `column:"lot_sou_id" json:"lotSouId"`
	AuctionDate               *time.Time              `column:"auction_date" json:"auctionDate"`
	DocumentNo                *string                 `column:"document_no" json:"documentNo"`
	AssetCode                 *string                 `column:"asset_code" json:"assetCode"`
	Floor                     *string                 `column:"floor" json:"floor"`
	AuctCode                  *string                 `column:"auct_code" json:"auctCode"`
	AuctionNo                 *int                    `column:"auction_no" json:"auctionNo"`
	CityCode                  *string                 `column:"city_code" json:"cityCode"`
	AssetGroupCode            *string                 `column:"asset_group_code" json:"assetGroupCode"`
	BrandDescription          *string                 `column:"brand_description" json:"brandDescription"`
	ModelDescription          *string                 `column:"model_description" json:"modelDescription"`
	Model                     *string                 `column:"model" json:"model"`
	SubModel                  *string                 `column:"sub_model" json:"sub_Model"`
	SubKey                    *string                 `column:"sub_key" json:"sub_Key"`
	EngineSize                *string                 `column:"engine_size" json:"engineSize"`
	LicensePlateNo            *string                 `column:"license_plate_no" json:"licensePlateNo"`
	CityDescription           *string                 `column:"city_description" json:"cityDescription"`
	RegistrationYear          *string                 `column:"registration_year" json:"registrationYear"`
	YearOfManufacture         *string                 `column:"year_of_manufacture" json:"yearOfManufacture"`
	ColorInCopy               *string                 `column:"color_in_copy" json:"colorInCopy"`
	TaxDate                   *time.Time              `column:"tax_date" json:"taxDate"`
	VendorGroup               *string                 `column:"vendor_group" json:"vendorGroup"`
	Mile                      *int                    `column:"mile" json:"mile"`
	SalesPrice                *float64                `column:"sales_price" json:"salesPrice"`
	SoldAmount                *float64                `column:"sold_amount" json:"soldAmount"`
	VatPercentage             *float64                `column:"vat_percentage" json:"vatPercentage"`
	AssetYear                 *int                    `column:"asset_year" json:"assetYear"`
	BranchDescription         *string                 `column:"branch_description" json:"branchDescription"`
	ContractNo                *string                 `column:"contract_no" json:"contractNo"`
	ChassisNo                 *string                 `column:"chassis_no" json:"chassisNo"`
	EngineNo                  *string                 `column:"engine_no" json:"engineNo"`
	AssetType                 *string                 `column:"asset_type" json:"assetType"`
	BranchCode                *string                 `column:"branch_code" json:"branchCode"`
	SellerCode                *string                 `column:"seller_code" json:"sellerCode"`
	SellerName                *string                 `column:"seller_name" json:"sellerName"`
	FuelTap                   *string                 `column:"fuel_tap" json:"fuelTap"`
	Gear                      *string                 `column:"gear" json:"gear"`
	GearName                  *string                 `column:"gear_name" json:"gear_Name"`
	Unque                     *string                 `column:"unque" json:"unque"`
	CarTypeCon                *string                 `column:"car_type_con" json:"car_Type_con"`
	PointGrade1Final          *int                    `column:"point_grade1_final" json:"pointGrade1Final"`
	PointGrade2Con            *int                    `column:"point_grade2_con" json:"pointGrade2Con"`
	PointGrade3Con            *int                    `column:"point_grade3_con" json:"pointGrade3Con"`
	FinanceZipFilename        *string                 `column:"finance_zip_filename" json:"financeZipFilename"`
	SellerOffer               *string                 `column:"seller_offer" json:"sellerOffer"`
	AuctionTime               *time.Time              `column:"auction_time" json:"auctionTime"`
	Remark                    *string                 `column:"remark" json:"remark"`
	SpecialAuction            *string                 `column:"special_auction" json:"specialAuction"`
	PersonalAssessmentAuction *string                 `column:"personal_assessment_auction" json:"personalAssessmentAuction"`
	PersonalAssessment        *string                 `column:"personal_assessment" json:"personalAssessment"`
	Tool                      *string                 `column:"tool" json:"tool"`
	SaleRounds                *int                    `column:"sale_rounds" json:"saleRounds"`
	AuctionStatus             *string                 `column:"auction_status" json:"auctionStatus"`
	ProductStatus             *string                 `column:"product_status" json:"productStatus"`
	IsActive                  bool                    `column:"is_active" json:"isActive"`
	IsDeletedByErp            bool                    `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate            *time.Time              `column:"latest_sync_date" json:"latestSyncDate"`
	DeletedDate               *gorm.DeletedAt         `gorm:"column:deleted_date" json:"deletedDate"`
	MasterAssetType           *model.AssetTypeForJoin `gorm:"foreignKey:AssetType;references:AssetTypeCode;->"`
}

func (LotSouLotLine) TableName() string {
	return "lot_sou_lot_line"
}
