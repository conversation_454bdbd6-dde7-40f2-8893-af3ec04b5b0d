package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type HelpRequestReason struct {
	*model.BaseEntity
	Reason      *string                `column:"reason" json:"reason"`
	IsActive    bool                   `column:"is_active" json:"isActive"`
	DeletedDate *gorm.DeletedAt        `column:"deleted_date" json:"deletedDate"`
	CreatedUser *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID;->"`
	UpdatedUser *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID;->"`
}

func (HelpRequestReason) TableName() string {
	return "help_request_reason"
}
