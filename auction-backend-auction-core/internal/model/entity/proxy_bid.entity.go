package entity

import (
	"backend-common-lib/model"
	"time"
)

type ProxyBid struct {
	*model.BaseEntity
	LotId           *int                   `column:"lot_id" json:"lotId"`
	LotSouLotLineId *int                   `column:"lot_sou_lot_line_id" json:"lotSouLotLineId"`
	BuyerId         *int                   `column:"buyer_id" json:"buyerId"`
	ProxyPrice      *float64               `column:"proxy_price" json:"proxyPrice"`
	ProxyStatusId   *int                   `column:"proxy_status_id" json:"proxyStatusId"`
	CancelDate      *time.Time             `column:"cancel_date" json:"cancelDate"`
	CancelBy        *int                   `column:"cancel_by" json:"cancelBy"`
	CancelReasonId  *int                   `column:"cancel_reason_id" json:"cancelReasonId"`
	CancelType      *string                `column:"cancel_type" json:"cancelType"`
	CreatedUser     *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID;->"`
	UpdatedUser     *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID;->"`
	CancelByUser    *model.EmployeeForJoin `gorm:"foreignKey:CancelBy;references:ID;->"`
	Buyer           *model.BuyerForJoin    `gorm:"foreignKey:BuyerId;references:ID;->"`
}

func (ProxyBid) TableName() string {
	return "proxy_bid"
}
