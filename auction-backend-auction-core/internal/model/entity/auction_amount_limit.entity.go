package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type AuctionAmountLimit struct {
	*model.BaseEntity
	AuctionId               int                         `column:"auction_id" json:"auctionId"`
	AuctionAssetId          int                         `column:"auction_asset_id" json:"auctionAssetId"`
	CustomerGroupId         string                      `column:"customer_group_id" json:"customerGroupId"`
	DepositAmountTypeCode   *string                     `column:"deposit_amount_type_code" json:"depositAmountTypeCode"`
	DepositAmount           *float64                    `column:"deposit_amount" json:"depositAmount"`
	CreditAmount            *float64                    `column:"credit_amount" json:"creditAmount"`
	AdditionalAmountPercent *float64                    `column:"additional_amount_percent" json:"additionalAmountPercent"`
	IsNoLimit               *bool                       `column:"is_no_limit" json:"isNoLimit"`
	ItemLimit               *int                        `column:"item_limit" json:"itemLimit"`
	CollateralId            *int                        `column:"collateral_id" json:"collateralId"`
	DeletedDate             *gorm.DeletedAt             `column:"deleted_date" json:"deletedDate"`
	CustomerGroup           *model.CustomerGroupForJoin `gorm:"foreignKey:CustomerGroupId;references:ID;->"`
}

func (AuctionAmountLimit) TableName() string {
	return "auction_amount_limit"
}
