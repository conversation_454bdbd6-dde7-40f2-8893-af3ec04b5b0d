package entity

import (
	"backend-common-lib/model"
	"time"
)

type PrintSlip struct {
	*model.BaseEntity
	LotSouLotLineId     *int       `column:"lot_sou_lot_line_id" json:"lotSouLotLineId"`
	PrintNo             *int       `column:"print_no" json:"printNo"`
	PrintedDate         *time.Time `column:"printed_date" json:"printedDate"`
	ReasonReprint       *string    `column:"reason_reprint" json:"reasonReprint"`
	No                  *string    `gorm:"column:no;->" json:"no"`
	LicensePlateNo      *string    `gorm:"column:license_plate_no;->" json:"licensePlateNo"`
	AuctionTag          *string    `gorm:"column:auction_tag;->" json:"auctionTag"`
	SaleDate            *time.Time `gorm:"column:sale_date;->" json:"saleDate"`
	SalesPrice          *float64   `gorm:"column:sales_price;->" json:"salesPrice"`
	SoldAmount          *float64   `gorm:"column:sold_amount;->" json:"soldAmount"`
	ProductStatus       *string    `gorm:"column:product_status;->" json:"productStatus"`
	Floor               *string    `gorm:"column:floor;->" json:"floor"`
	BranchDescriptionTh *string    `gorm:"column:branch_description_th;->" json:"branchDescriptionTh"`
	BranchDescriptionEn *string    `gorm:"column:branch_description_en;->" json:"branchDescriptionEn"`
	InvoiceNo           *string    `gorm:"column:invoice_no;->" json:"invoiceNo"`
	CreditNo            *string    `gorm:"column:credit_no;->" json:"creditNo"`
	Rvu                 *string    `gorm:"column:rvu;->" json:"rvu"`
	CreatedUser         *string    `gorm:"column:created_user;->" json:"createdUser"`
	UpdatedUser         *string    `gorm:"column:updated_user;->" json:"updatedUser"`
}

func (PrintSlip) TableName() string {
	return "print_slip"
}
