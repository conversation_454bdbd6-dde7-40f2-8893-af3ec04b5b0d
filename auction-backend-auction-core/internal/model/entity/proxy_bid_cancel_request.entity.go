package entity

import (
	"backend-common-lib/model"
	"time"
)

type ProxyBidCancelRequest struct {
	*model.BaseEntity
	ProxyBidId       *int                   `column:"proxy_bid_id" json:"proxyBidId"`
	BuyerId          *int                   `column:"buyer_id" json:"buyerId"`
	ActionStatus     *string                `column:"action_status" json:"actionStatus"`
	ApproveBy        *int                   `column:"approve_by" json:"approveBy"`
	ApproveDate      *time.Time             `column:"approve_date" json:"approveDate"`
	AuctionTagNumber *string                `column:"auction_tag_number" json:"auctionTagNumber"`
	CreatedUser      *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:ID;->"`
	UpdatedUser      *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:ID;->"`
	ApproveByUser    *model.EmployeeForJoin `gorm:"foreignKey:ApproveBy;references:ID;->"`
}

func (ProxyBidCancelRequest) TableName() string {
	return "proxy_bid_cancel_request"
}
