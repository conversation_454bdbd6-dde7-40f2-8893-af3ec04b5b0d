package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type LotAssetType struct {
	*model.BaseEntity
	LotId       int             `gorm:"column:lot_id" json:"lotId"`
	AssetTypeId int             `gorm:"column:asset_type_id" json:"assetTypeId"`
	DeletedDate *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
}

func (LotAssetType) TableName() string {
	return "lot_asset_type"
}
