package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type LotActiveOption struct {
	*model.BaseEntity
	LotId              int             `gorm:"column:lot_id" json:"lotId"`
	ConfigFeatureLotId int             `gorm:"column:config_feature_lot_id" json:"configFeatureLotId"`
	DeletedDate        *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
}

func (LotActiveOption) TableName() string {
	return "lot_active_option"
}
