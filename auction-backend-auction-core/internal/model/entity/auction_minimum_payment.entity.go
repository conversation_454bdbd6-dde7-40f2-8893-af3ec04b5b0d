package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type AuctionMinimumPayment struct {
	*model.BaseEntity
	AuctionId          int             `column:"auction_id" json:"auctionId"`
	AuctionAssetId     int             `column:"auction_asset_id" json:"auctionAssetId"`
	VendorId           *int            `column:"vendor_id" json:"vendorId"`
	MinimumPaymentType *string         `column:"minimum_payment_type" json:"minimumPaymentType"`
	MinimumPercent     *int            `column:"minimum_percent" json:"minimumPercent"`
	MinimumAmount      *float64        `column:"minimum_amount" json:"minimumAmount"`
	DeletedDate        *gorm.DeletedAt `column:"deleted_date" json:"deletedDate"`
}

func (AuctionMinimumPayment) TableName() string {
	return "auction_minimum_payment"
}
