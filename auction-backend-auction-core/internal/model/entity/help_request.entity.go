package entity

import (
	"backend-common-lib/model"
	"time"
)

type HelpRequest struct {
	*model.BaseEntity
	LotSouLotLineId     *int       `column:"lot_sou_lot_line_id" json:"lotSouLotLineId"`
	HelpRequestReasonId *int       `column:"help_request_reason_id" json:"helpRequestReasonId"`
	Solution            *string    `column:"solution" json:"solution"`
	StatusHelpRequestId *int       `column:"status_help_request_id" json:"statusHelpRequestId"`
	SolveIssueBy        *string    `column:"solve_issue_by" json:"solveIssueBy"`
	SolveIssueDate      *time.Time `column:"solve_issue_date" json:"solveIssueDate"`
	CreatedUser         *string    `gorm:"column:created_user;->" json:"createdUser"`
	UpdatedUser         *string    `gorm:"column:updated_user;->" json:"updatedUser"`
	No                  *string    `gorm:"column:no;->" json:"no"`
	BranchDescriptionTh *string    `gorm:"column:branch_description_th;->" json:"branchDescriptionTh"`
	BranchDescriptionEn *string    `gorm:"column:branch_description_en;->" json:"branchDescriptionEn"`
	Floor               *string    `gorm:"column:floor;->" json:"floor"`
	HelpRequestReason   *string    `gorm:"column:help_request_reason;->" json:"helpRequestReason"`
	StatusHelpRequestTh *string    `gorm:"column:status_help_request_th;->" json:"statusHelpRequestTh"`
	StatusHelpRequestEn *string    `gorm:"column:status_help_request_en;->" json:"statusHelpRequestEn"`
}

func (HelpRequest) TableName() string {
	return "help_request"
}
