package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type VideoAds struct {
	*model.BaseEntity
	IsActive    bool            `column:"is_active" json:"isActive"`
	VideoName   *string         `column:"video_name" json:"videoName"`
	LinkVideo   *string         `column:"link_video" json:"linkVideo"`
	EventId     *int            `column:"event_id" json:"eventId"`
	StartDate   *time.Time      `column:"start_date" json:"startDate"`
	EndDate     *time.Time      `column:"end_date" json:"endDate"`
	StartTime   *time.Time      `column:"start_time" json:"startTime"`
	EndTime     *time.Time      `column:"end_time" json:"endTime"`
	DeletedDate *gorm.DeletedAt `column:"deleted_date" json:"deletedDate"`
}

func (VideoAds) TableName() string {
	return "video_ads"
}
