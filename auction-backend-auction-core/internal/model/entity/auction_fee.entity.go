package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type AuctionFee struct {
	*model.BaseEntity
	AuctionId         int             `column:"auction_id" json:"auctionId"`
	AuctionAssetId    int             `column:"auction_asset_id" json:"auctionAssetId"`
	VendorGroupId     *int            `column:"vendor_group_id" json:"vendorGroupId"`
	RegisterTypeId    *int            `column:"register_type_id" json:"registerTypeId"`
	SalesPrice        *float64        `column:"sales_price" json:"salesPrice"`
	SoldAmount        *float64        `column:"sold_amount" json:"soldAmount"`
	CcFrom            *int            `column:"cc_from" json:"ccFrom"`
	CcTo              *int            `column:"cc_to" json:"ccTo"`
	RegisterTypeCarId *int            `column:"register_type_car_id" json:"registerTypeCarId"`
	RegisteredYear    *int            `column:"registered_year" json:"registeredYear"`
	Fee               *float64        `column:"fee" json:"fee"`
	StartDate         *time.Time      `column:"start_date" json:"startDate" example:"2020-01-01"`
	EndDate           *time.Time      `column:"end_date" json:"endDate" example:"2020-01-01"`
	DeletedDate       *gorm.DeletedAt `column:"deleted_date" json:"deletedDate"`
}

func (AuctionFee) TableName() string {
	return "auction_fee"
}
