package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type LotStreaming struct {
	*model.BaseEntity
	LotId         int             `gorm:"column:lot_id" json:"lotId"`
	Order         int             `gorm:"column:order" json:"order"`
	StreamingName string          `gorm:"column:streaming_name;size:100" json:"streamingName"`
	StreamingLink string          `gorm:"column:streaming_link;size:100" json:"streamingLink"`
	DeletedDate   *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
}

func (LotStreaming) TableName() string {
	return "lot_streaming"
}
