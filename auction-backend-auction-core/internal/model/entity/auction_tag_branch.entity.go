package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type AuctionTagBranch struct {
	ID           *int                       `column:"id" json:"id" example:"1"`
	AuctionTagId *int                       `column:"auction_tag_id" json:"auctionTagId" example:"1"`
	BranchId     *int                       `column:"branch_id" json:"branchId" example:"1"`
	DeletedAt    *gorm.DeletedAt            `gorm:"column:deleted_at" json:"deletedAt"`
	DeletedBy    *int                       `gorm:"column:deleted_by" json:"deletedBy"`
	Branch       *model.MasterBranchForJoin `gorm:"foreignKey:BranchId;references:ID"`
}

func (AuctionTagBranch) TableName() string {
	return "auction_tag_branch"
}
