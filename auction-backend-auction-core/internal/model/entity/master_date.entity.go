package entity

// Role
// @Description A representation of a user.
// @ID Role

type MasterData struct {
	Id            int    `gorm:"primaryKey" column:"id" json:"id"`
	MasterType    string `column:"master_type" json:"masterType"`
	DescriptionTh string `column:"description_th" json:"descriptionTh"`
	DescriptionEn string `column:"description_en" json:"descriptionEn"`
	Code          string `column:"code" json:"code"`
}

func (MasterData) TableName() string {
	return "master_data"
}
