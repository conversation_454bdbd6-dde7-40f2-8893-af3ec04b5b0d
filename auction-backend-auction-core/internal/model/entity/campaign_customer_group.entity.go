package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type CampaignCustomerGroup struct {
	*model.BaseEntity
	CampaignId           *int                        `column:"campaign_id" json:"campaignId"`
	CustomerGroupId      *int                        `column:"customer_group_id" json:"customerGroupId"`
	DeletedDate          *gorm.DeletedAt             `gorm:"column:deleted_date" json:"deletedDate"`
	CustomerGroupForJoin *model.CustomerGroupForJoin `gorm:"foreignKey:CustomerGroupId;references:ID;->"`
}

func (CampaignCustomerGroup) TableName() string {
	return "campaign_customer_group"
}
