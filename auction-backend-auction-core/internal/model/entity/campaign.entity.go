package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type Campaign struct {
	*model.BaseEntity
	Name               *string                   `column:"name" json:"name"`
	EventTypeId        *int                      `column:"event_type_id" json:"eventTypeId"`
	EventId            *int                      `column:"event_id" json:"eventId"`
	VendorGroupId      *int                      `column:"vendor_group_id" json:"vendorGroupId"`
	Description        *string                   `column:"description" json:"description"`
	IsLinkUrl          *bool                     `column:"is_link_url" json:"isLinkUrl"`
	LinkUrl            *string                   `column:"link_url" json:"linkUrl"`
	IsUpload           *bool                     `column:"is_upload" json:"isUpload"`
	FilePath           *string                   `column:"file_path" json:"filePath"`
	StartDate          *time.Time                `column:"start_date" json:"startDate"`
	EndDate            *time.Time                `column:"end_date" json:"endDate"`
	IsActive           bool                      `column:"is_active" json:"isActive"`
	DeletedDate        *gorm.DeletedAt           `gorm:"column:deleted_date" json:"deletedDate"`
	EventForJoin       *model.EventForJoin       `gorm:"foreignKey:EventId;references:ID;->"`
	VendorGroupForJoin *model.VendorGroupForJoin `gorm:"foreignKey:VendorGroupId;references:ID;->"`
}

func (Campaign) TableName() string {
	return "campaign"
}
