package router

import (
	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"

	auction "auction-core-service/internal/api/auction"
	auctionTag "auction-core-service/internal/api/auction_tag"
	campaignSurvey "auction-core-service/internal/api/campaign_survey"
	helpRequest "auction-core-service/internal/api/help_request"
	helpRequestReason "auction-core-service/internal/api/help_request_reason"
	lot "auction-core-service/internal/api/lot"
	printSlip "auction-core-service/internal/api/print_slip"
	proxyBid "auction-core-service/internal/api/proxy_bid"
	proxyBidCancelRequest "auction-core-service/internal/api/proxy_bid_cancel_request"
	videoAds "auction-core-service/internal/api/video_ads"

	"auction-core-service/internal/global"
)

func RegisterRoutes(api fiber.Router, db *gorm.DB, config *global.Config) {
	auctionGroup := api.Group("/auction-setting")
	auction.Register(auctionGroup, db, config)
	videoAds.Register(auctionGroup, db)
	helpRequestReason.Register(auctionGroup, db, config)
	campaignSurvey.Register(auctionGroup, db)

	lot.Register(api, db, config)

	monitorGroup := api.Group("/monitor")
	proxyBidCancelRequest.Register(monitorGroup, db)
	helpRequest.Register(monitorGroup, db)

	proxyBidGroup := api.Group("/proxy-bid")
	proxyBid.Register(proxyBidGroup, db)

	auctionTag.Register(auctionGroup, db, config)

	printSlipGroup := api.Group("/print-slips")
	printSlip.Register(printSlipGroup, db)
}
