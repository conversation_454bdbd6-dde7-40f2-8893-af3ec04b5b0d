package constant

const (
	ProxyBidCancelAdminEn  = "ADMIN"
	ProxyBidCancelAdminTh  = "แอดมิน"
	ProxyBidCancelBuyerEn  = "BUYER"
	ProxyBidCancelBuyerTh  = "สมาชิกกดยกเลิกเอง"
	ProxyBidCancelSystemEn = "SYSTEM"
	ProxyBidCancelSystemTh = "ยกเลิกโดยระบบ"
)

type ProxyBidCancelItem struct {
	Value   string
	LabelTh string
	LabelEn string
}

var ProxyBidCancelStatus = []ProxyBidCancelItem{
	{Value: ProxyBidCancelAdminEn, LabelTh: ProxyBidCancelAdminTh, LabelEn: ProxyBidCancelAdminEn},
	{Value: ProxyBidCancelBuyerEn, LabelTh: ProxyBidCancelBuyerTh, LabelEn: ProxyBidCancelBuyerEn},
	{Value: ProxyBidCancelSystemEn, LabelTh: ProxyBidCancelSystemTh, LabelEn: ProxyBidCancelSystemEn},
}
