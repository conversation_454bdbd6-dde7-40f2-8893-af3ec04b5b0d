package proxybidcancelrequest

import (
	auctionRepository "auction-core-service/internal/repository/auction"
	lotRepository "auction-core-service/internal/repository/lot"
	lotAssetTypeRepository "auction-core-service/internal/repository/lot_asset_type"
	lotSouRepository "auction-core-service/internal/repository/lot_sou"
	lotSouLotLineRepository "auction-core-service/internal/repository/lot_sou_lot_line"
	proxyBidRepository "auction-core-service/internal/repository/proxy_bid"
	service "auction-core-service/internal/service/proxy_bid"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB) {
	proxyBidRepo := proxyBidRepository.NewProxyBidRepository(db)
	lotRepo := lotRepository.NewLotRepository(db)
	lotAssetTypeRepo := lotAssetTypeRepository.NewLotAssetTypeRepository(db)
	lotSouRepo := lotSouRepository.NewLotSouRepository(db)
	lotSouLotLineRepo := lotSouLotLineRepository.NewLotSouLotLineRepository(db)
	auctionRepo := auctionRepository.NewAuctionRepository(db)
	service := service.NewProxyBidService(proxyBidRepo, lotRepo, lotAssetTypeRepo, lotSouRepo, lotSouLotLineRepo, auctionRepo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("/list")
	route.Post("/lot/search", h.SearchLotProxyBidFilter)
	route.Post("/lot-lines/search", h.SearchLotLineProxyBidFilter)
	route.Get("/:id", h.GetProxyBidListBySouLotLineId)
}
