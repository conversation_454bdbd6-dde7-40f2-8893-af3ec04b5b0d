package proxybidcancelrequest

import (
	"net/http"
	"strconv"

	"auction-core-service/internal/model/dto"
	service "auction-core-service/internal/service/proxy_bid"
	"backend-common-lib/errs"
	"backend-common-lib/model"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service service.ProxyBidService
}

func (h *Handler) SearchLotProxyBidFilter(c *fiber.Ctx) error {
	var req dto.LotProxyBidSearchReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchLotProxyBidFilter(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) SearchLotLineProxyBidFilter(c *fiber.Ctx) error {
	var req dto.LotLineProxyBidSearchReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchLotLineProxyBidFilter(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetProxyBidListBySouLotLineId(c *fiber.Ctx) error {

	idStr := c.Params("id")
	souLotLineId, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.GetProxyBidListBySouLotLineId(souLotLineId)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
