package auction

import (
	"auction-core-service/internal/global"
	auctionRepository "auction-core-service/internal/repository/auction"
	auctionAmountLimitRepository "auction-core-service/internal/repository/auction_amount_limit"
	auctionAssetRepository "auction-core-service/internal/repository/auction_asset"
	auctionBidStepRepository "auction-core-service/internal/repository/auction_bid_step"
	auctionFeeRepository "auction-core-service/internal/repository/auction_fee"
	auctionMinimumPaymentRepository "auction-core-service/internal/repository/auction_minimum_payment"
	commonAuctionCoreRepository "auction-core-service/internal/repository/common_auction_core"
	service "auction-core-service/internal/service/auction"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	auctionRepo := auctionRepository.NewAuctionRepository(db)
	auctionAssetRepo := auctionAssetRepository.NewAuctionAssetRepository(db)
	auctionAmountLimitRepo := auctionAmountLimitRepository.NewAuctionAmountLimitRepository(db)
	auctionBidStepRepo := auctionBidStepRepository.NewAuctionBidStepRepository(db)
	auctionFeeRepo := auctionFeeRepository.NewAuctionFeeRepository(db)
	auctionMinimumPaymentRepo := auctionMinimumPaymentRepository.NewAuctionMinimumPaymentRepository(db)
	commonAuctionCoreRepo := commonAuctionCoreRepository.NewCommonAuctionCoreRepository(db)
	service := service.NewAuctionService(auctionRepo,
		auctionAssetRepo,
		auctionAmountLimitRepo,
		auctionBidStepRepo,
		auctionFeeRepo,
		auctionMinimumPaymentRepo,
		commonAuctionCoreRepo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("auction")
	route.Get("/:id", h.GetAuctionById)
	route.Post("/search", h.SearchAuctionFilter)
	route.Post("/", h.CreateAuction)
	route.Put("/:id", h.UpdateAuction)
	route.Put("/status/:id", h.UpdateAuctionStatus)
	route.Delete("/:id", h.DeleteAuction)
}
