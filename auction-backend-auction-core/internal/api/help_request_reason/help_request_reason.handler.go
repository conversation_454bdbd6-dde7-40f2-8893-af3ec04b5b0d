package masterholiday

import (
	"auction-core-service/internal/global"
	"auction-core-service/internal/model/dto"
	service "auction-core-service/internal/service/help_request_reason"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.HelpRequestReasonService
	ErpConfig global.ErpConfig
}

func (h *Handler) HelpRequestReasonFilter(c *fiber.Ctx) error {
	var req dto.HelpRequestReasonPageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.HelpRequestReasonFilter(req)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetHelpRequestReasonById(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	res, err := h.Service.GetHelpRequestReasonById(id)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) CreateHelpRequestReason(c *fiber.Ctx) error {
	var req dto.HelpRequestReasonInternalReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.CreateHelpRequestReason(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) UpdateHelpRequestReason(c *fiber.Ctx) error {
	var req dto.HelpRequestReasonInternalReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateHelpRequestReason(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) UpdateHelpRequestReasonStatus(c *fiber.Ctx) error {
	var req dto.HelpRequestReasonStatusReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateHelpRequestReasonStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) DeleteHelpRequestReason(c *fiber.Ctx) error {

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	actionBy := util.GetActionByFromHeader(c)

	err = h.Service.DeleteHelpRequestReason(id, actionBy)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
