package masterholiday

import (
	"auction-core-service/internal/global"
	repository "auction-core-service/internal/repository/help_request_reason"
	helpRequestReasonRoleRepository "auction-core-service/internal/repository/help_request_reason_role"
	service "auction-core-service/internal/service/help_request_reason"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewHelpRequestReasonRepository(db)
	helpRequestReasonRoleRepo := helpRequestReasonRoleRepository.NewHelpRequestReasonRoleRepository(db)
	service := service.NewHelpRequestReasonService(repo, helpRequestReasonRoleRepo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}
func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("help-request-reason")
	route.Post("/search", h.HelpRequestReasonFilter)
	route.Get("/:id", h.GetHelpRequestReasonById)
	route.Post("", h.CreateHelpRequestReason)
	route.Put("/:id", h.UpdateHelpRequestReason)
	route.Put("/status/:id", h.UpdateHelpRequestReasonStatus)
	route.Delete("/:id", h.DeleteHelpRequestReason)
}
