package proxybidcancelrequest

import (
	proxyBidRepository "auction-core-service/internal/repository/proxy_bid"
	proxyBidCancelRequestRepository "auction-core-service/internal/repository/proxy_bid_cancel_request"
	service "auction-core-service/internal/service/proxy_bid_cancel_request"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB) {
	proxyBidCancelRequestRepo := proxyBidCancelRequestRepository.NewProxyBidCancelRequestRepository(db)
	proxyBidRepo := proxyBidRepository.NewProxyBidRepository(db)
	service := service.NewProxyBidCancelRequestService(proxyBidCancelRequestRepo, proxyBidRepo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("/proxy-bid-cancel-requests")
	route.Post("/search", h.SearchProxyBidCancelRequestFilter)
	route.Put("/:action/:id", h.UpdateStatusProxyBidCancelRequest)
}
