package proxybidcancelrequest

import (
	"net/http"
	"strconv"

	"auction-core-service/internal/model/dto"
	service "auction-core-service/internal/service/proxy_bid_cancel_request"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service service.ProxyBidCancelRequestService
}

func (h *Handler) SearchProxyBidCancelRequestFilter(c *fiber.Ctx) error {
	var req dto.ProxyBidCancelRequestPageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchProxyBidCancelRequestFilter(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) UpdateStatusProxyBidCancelRequest(c *fiber.Ctx) error {

	var req dto.ProxyBidCancelRequestUpdateReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	action := c.Params("action")

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateStatusProxyBidCancelRequest(req, action)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
