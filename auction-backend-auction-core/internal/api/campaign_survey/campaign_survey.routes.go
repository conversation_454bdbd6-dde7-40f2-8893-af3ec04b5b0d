package lot

import (
	campaignRepository "auction-core-service/internal/repository/campaign"
	campaignAssetGroupRepository "auction-core-service/internal/repository/campaign_asset_group"
	campaignCustomerGroupRepository "auction-core-service/internal/repository/campaign_customer_group"
	campaignDisplayLocationRepository "auction-core-service/internal/repository/campaign_display_location"
	commonAuctionCoreRepository "auction-core-service/internal/repository/common_auction_core"
	service "auction-core-service/internal/service/campaign_survey"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB) {
	campaignRepo := campaignRepository.NewCampaignRepository(db)
	campaignAssetGroupRepo := campaignAssetGroupRepository.NewCampaignAssetGroupRepository(db)
	campaignCustomerGroupRepo := campaignCustomerGroupRepository.NewCampaignCustomerGroupRepository(db)
	campaignDisplayLocationRepo := campaignDisplayLocationRepository.NewCampaignDisplayLocationRepository(db)
	commonAuctionCoreRepo := commonAuctionCoreRepository.NewCommonAuctionCoreRepository(db)
	service := service.NewCampaignSurveyService(campaignRepo, campaignAssetGroupRepo, campaignCustomerGroupRepo, campaignDisplayLocationRepo, commonAuctionCoreRepo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("campaign-survey")
	route.Post("/search", h.GetAllCampaignSurvey)
	route.Get("/campaign/:id", h.GetCampaignById)
	route.Post("/campaign/", h.CreateCampaign)
	route.Put("/campaign/:id", h.UpdateCampaign)
	route.Put("/campaign/status/:id", h.UpdateCampaignStatus)
	route.Delete("/campaign/:id", h.DeleteCampaign)
}
