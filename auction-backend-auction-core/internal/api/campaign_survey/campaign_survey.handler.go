package lot

import (
	"auction-core-service/internal/model/dto"
	service "auction-core-service/internal/service/campaign_survey"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service service.CampaignSurveyService
}

func (h *Handler) GetAllCampaignSurvey(c *fiber.Ctx) error {
	var req model.PagingRequest

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.GetAllCampaignSurvey(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetCampaignById(c *fiber.Ctx) error {
	idStr := c.<PERSON>("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	res, err := h.Service.GetCampaignById(id)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) CreateCampaign(c *fiber.Ctx) error {
	var req dto.CampaignDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.CreateCampaign(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) UpdateCampaign(c *fiber.Ctx) error {
	var req dto.CampaignDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	err = h.Service.UpdateCampaign(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) UpdateCampaignStatus(c *fiber.Ctx) error {
	var req dto.CampaignStatusReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateCampaignStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) DeleteCampaign(c *fiber.Ctx) error {

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	actionBy := util.GetActionByFromHeader(c)

	err = h.Service.DeleteCampaign(id, actionBy)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
