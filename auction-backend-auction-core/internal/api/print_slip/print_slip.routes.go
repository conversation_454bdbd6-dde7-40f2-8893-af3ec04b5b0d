package printslip

import (
	printSlipRepository "auction-core-service/internal/repository/print_slip"
	service "auction-core-service/internal/service/print_slip"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB) {
	printSlipRepo := printSlipRepository.NewPrintSlipRepository(db)
	service := service.NewPrintSlipService(printSlipRepo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	r.Post("/search", h.SearchPrintSlipFilter)
}
