package printslip

import (
	"auction-core-service/internal/model/dto"
	service "auction-core-service/internal/service/print_slip"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service service.PrintSlipService
}

func (h *Handler) SearchPrintSlipFilter(c *fiber.Ctx) error {
	var req dto.PrintSlipPageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchPrintSlipFilter(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
