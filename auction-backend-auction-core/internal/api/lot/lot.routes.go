package lot

import (
	"auction-core-service/internal/global"
	auctionRepository "auction-core-service/internal/repository/auction"
	commonAuctionCoreRepository "auction-core-service/internal/repository/common_auction_core"
	lotRepository "auction-core-service/internal/repository/lot"
	lotActiveOptionRepository "auction-core-service/internal/repository/lot_active_option"
	lotAssetTypeRepository "auction-core-service/internal/repository/lot_asset_type"
	lotSouRepository "auction-core-service/internal/repository/lot_sou"
	lotSouLotLineRepository "auction-core-service/internal/repository/lot_sou_lot_line"
	lotStreamingRepository "auction-core-service/internal/repository/lot_streaming"
	service "auction-core-service/internal/service/lot"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	lotRepo := lotRepository.NewLotRepository(db)
	lotActiveOptionRepo := lotActiveOptionRepository.NewLotActiveOptionRepository(db)
	lotAssetTypeRepo := lotAssetTypeRepository.NewLotAssetTypeRepository(db)
	lotSouRepo := lotSouRepository.NewLotSouRepository(db)
	lotSouLotLineRepo := lotSouLotLineRepository.NewLotSouLotLineRepository(db)
	lotStreamingRepo := lotStreamingRepository.NewLotStreamingRepository(db)
	auctionRepo := auctionRepository.NewAuctionRepository(db)
	commonAuctionCoreRepo := commonAuctionCoreRepository.NewCommonAuctionCoreRepository(db)
	service := service.NewLotService(lotRepo, lotActiveOptionRepo, lotAssetTypeRepo, lotSouRepo, lotSouLotLineRepo, lotStreamingRepo, auctionRepo, commonAuctionCoreRepo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("lot")
	route.Get("/:id", h.GetLotByID)
	route.Post("/", h.CreateLot)
	route.Put("/:id", h.UpdateLot)
	route.Post("/sou-erp", h.GetLotSouFromErp)
	route.Post("/sou-erp-lot-line-sync/:id", h.GetSyncLotSouLotLineFromErp)
	route.Post("/sou-erp-lot-line-sync/product/:id", h.GetSyncLotSouLotLineByProductFromErp)
	route.Delete("/:id", h.DeleteLot)
	route.Post("/search", h.SearchLots)
	route.Post("/lot-lines/search", h.SearchLotLineFilter)
	route.Post("/lot-lines/download/products", h.LotLineExportExcel)
	route.Post("/lot-lines/upload/products/:id", h.LotLineUploadExcel)
}
