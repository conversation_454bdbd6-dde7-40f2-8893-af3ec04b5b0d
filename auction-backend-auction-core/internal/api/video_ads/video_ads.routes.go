package videoads

import (
	videoAdsRepository "auction-core-service/internal/repository/video_ads"
	service "auction-core-service/internal/service/video_ads"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB) {
	videoAdsRepo := videoAdsRepository.NewVideoAdsRepository(db)
	service := service.NewVideoAdsService(videoAdsRepo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("/video-ads")
	route.Post("/search", h.SearchAllVideoAds)
	route.Post("/", h.CreateVideoAds)
	route.Get("/:id", h.GetVideoAdsById)
	route.Put("/:id", h.UpdateVideoAds)
	route.Delete("/:id", h.DeleteVideoAdsById)
}
