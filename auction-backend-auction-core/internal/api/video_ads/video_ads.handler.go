package videoads

import (
	"net/http"
	"strconv"

	"auction-core-service/internal/model/dto"
	service "auction-core-service/internal/service/video_ads"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service service.VideoAdsService
}

func (h *Handler) SearchAllVideoAds(c *fiber.Ctx) error {
	var req dto.VideoAdsPageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchAllVideoAds(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) CreateVideoAds(c *fiber.Ctx) error {
	eventIdStr := c.FormValue("eventId")
	eventId := 0
	if eventIdStr != "" {
		if parsed, err := strconv.Atoi(eventIdStr); err != nil {
			return errs.NewError(http.StatusBadRequest, err)
		} else {
			eventId = parsed
		}
	}

	actionBy := util.GetActionByFromHeader(c)
	req := dto.VideoAdsCreateReqDto{
		IsActive:  c.FormValue("isActive") == "true",
		VideoName: util.Ptr(c.FormValue("videoName")),
		LinkVideo: util.Ptr(c.FormValue("linkVideo")),
		EventId:   util.Ptr(eventId),
		StartDate: util.Ptr(c.FormValue("startDate")),
		EndDate:   util.Ptr(c.FormValue("endDate")),
		StartTime: util.Ptr(c.FormValue("startTime")),
		EndTime:   util.Ptr(c.FormValue("endTime")),
		ActionBy:  actionBy,
	}

	form, err := c.MultipartForm()
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	err = h.Service.CreateVideoAds(req, form)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) UpdateVideoAds(c *fiber.Ctx) error {
	eventIdStr := c.FormValue("eventId")
	eventId := 0
	if eventIdStr != "" {
		if parsed, err := strconv.Atoi(eventIdStr); err != nil {
			return errs.NewError(http.StatusBadRequest, err)
		} else {
			eventId = parsed
		}
	}

	actionBy := util.GetActionByFromHeader(c)
	req := dto.VideoAdsCreateReqDto{
		IsActive:  c.FormValue("isActive") == "true",
		VideoName: util.Ptr(c.FormValue("videoName")),
		LinkVideo: util.Ptr(c.FormValue("linkVideo")),
		EventId:   util.Ptr(eventId),
		StartDate: util.Ptr(c.FormValue("startDate")),
		EndDate:   util.Ptr(c.FormValue("endDate")),
		StartTime: util.Ptr(c.FormValue("startTime")),
		EndTime:   util.Ptr(c.FormValue("endTime")),
		ActionBy:  actionBy,
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	form, err := c.MultipartForm()
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	err = h.Service.UpdateVideoAds(req, form)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) GetVideoAdsById(c *fiber.Ctx) error {
	var res dto.VideoAdsRespDto

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err = h.Service.GetVideoAdsById(id)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) DeleteVideoAdsById(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	if err := h.Service.DeleteVideoAdsById(id); err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
