package auction

import (
	"auction-core-service/internal/global"
	"auction-core-service/internal/model/dto"
	service "auction-core-service/internal/service/auction_tag"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.AuctionTagService
	ErpConfig global.ErpConfig
}

func (h *Handler) GetAssetTypeByAuctionID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	res, err := h.Service.GetAssetTypeByAuctionID(id)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAuctionTags(c *fiber.Ctx) error {
	var req model.PagingRequest

	if err := c.BodyP<PERSON>er(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	res, err := h.Service.GetAuctionTagsNew(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))

}

func (h *Handler) GetAuctionTagById(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	res, err := h.Service.GetAuctionTagByIdNew(id)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) CreateAuctionTag(c *fiber.Ctx) error {
	var req dto.AuctionTagReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	actionBy := util.GetActionByFromHeader(c)
	err := h.Service.CreateAuctionTagNew(req, actionBy)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) UpdateAuctionTagStatus(c *fiber.Ctx) error {
	var req dto.AuctionTagUpdateReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy
	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	err = h.Service.UpdateAuctionTagStatusNew(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) DeleteAuctionTag(c *fiber.Ctx) error {

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	err = h.Service.DeleteAuctionTagNew(id)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
