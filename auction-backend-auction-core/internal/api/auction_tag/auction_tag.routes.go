package auction

import (
	"auction-core-service/internal/global"
	auctionRepository "auction-core-service/internal/repository/auction"
	auctionAssetRepository "auction-core-service/internal/repository/auction_asset"
	auctionTagRepository "auction-core-service/internal/repository/auction_tag"
	auctionTagBranchRepository "auction-core-service/internal/repository/auction_tag_branch"
	service "auction-core-service/internal/service/auction_tag"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	auctionTagRepo := auctionTagRepository.NewAuctionTagRepository(db)
	auctionAssetRepo := auctionAssetRepository.NewAuctionAssetRepository(db)
	auctionRepo := auctionRepository.NewAuctionRepository(db)
	auctionTagBranchRepo := auctionTagBranchRepository.NewAuctionTagBranchRepository(db)
	service := service.NewAuctionTagService(auctionTagRepo, auctionAssetRepo, auctionRepo, auctionTagBranchRepo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("auction-tags")
	route.Post("/search", h.GetAuctionTags)
	route.Get("/:id", h.GetAssetTypeByAuctionID)
	route.Get("/details/:id", h.GetAuctionTagById)
	route.Post("/", h.CreateAuctionTag)
	route.Put("/status/:id", h.UpdateAuctionTagStatus)
	route.Delete("/:id", h.DeleteAuctionTag)
}
