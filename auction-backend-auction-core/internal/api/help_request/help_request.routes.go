package helprequest

import (
	helpRequestRepository "auction-core-service/internal/repository/help_request"
	service "auction-core-service/internal/service/help_request"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB) {
	helpRequestRepo := helpRequestRepository.NewHelpRequestRepository(db)
	service := service.NewHelpRequestService(helpRequestRepo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("/help-requests")
	route.Post("/search", h.SearchHelpRequestFilter)
	route.Get("/:id", h.GetHelpRequestById)
	route.Put("/status/:id", h.UpdateStatusHelpRequest)
}
