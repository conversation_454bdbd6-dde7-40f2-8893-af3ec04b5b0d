package service

import (
	"auction-core-service/internal/model/dto"
	repoAuction "auction-core-service/internal/repository/auction"
	repoAuctionAsset "auction-core-service/internal/repository/auction_asset"
	repository "auction-core-service/internal/repository/auction_tag"
	repoAuctionTagBranch "auction-core-service/internal/repository/auction_tag_branch"
	"backend-common-lib/model"
)

type auctionTagService struct {
	RepoAuction          repoAuction.AuctionRepository
	RepoAuctionTag       repository.AuctionTagRepository
	RepoAuctionAsset     repoAuctionAsset.AuctionAssetRepository
	RepoAuctionTagBranch repoAuctionTagBranch.AuctionTagBranchRepository
}

type AuctionTagService interface {
	GetAssetTypeByAuctionID(auctionId int) ([]dto.AuctionAssetType, error)
	CreateAuctionTagNew(req dto.AuctionTagReqDto, actionBy *int) error
	GetAuctionTagsNew(req model.PagingRequest) (dto.AuctionTagPageRespDto[dto.AuctionTagRespDto], error)
	GetAuctionTagByIdNew(auctionTagId int) (dto.AuctionTagPageRespDto[dto.AuctionTagRespDto], error)
	UpdateAuctionTagStatusNew(req dto.AuctionTagUpdateReqDto) error
	DeleteAuctionTagNew(id int) error
}

func NewAuctionTagService(repoAuctionTag repository.AuctionTagRepository, repoAuctionAsset repoAuctionAsset.AuctionAssetRepository, repoAuction repoAuction.AuctionRepository, repoAuctionTagBranch repoAuctionTagBranch.AuctionTagBranchRepository) AuctionTagService {
	return &auctionTagService{RepoAuctionTag: repoAuctionTag, RepoAuctionAsset: repoAuctionAsset, RepoAuction: repoAuction, RepoAuctionTagBranch: repoAuctionTagBranch}
}
