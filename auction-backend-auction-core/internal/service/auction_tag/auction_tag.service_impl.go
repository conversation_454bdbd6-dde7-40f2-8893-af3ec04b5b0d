package service

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/log"
	"gorm.io/gorm"
)

func (s *auctionTagService) CreateAuctionTagNew(req dto.AuctionTagReqDto, actionBy *int) error {
	now := util.Now()

	if actionBy == nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "actionBy is required", "")
	}
	// Condition For Check Before Insert
	// 1. ถ้าสาขาเป็น All และ รูปแบบการประมูล 1 สามารถเพิ่ม สาขา กรุงเทพ พัทยา... กับ รูปแบบการประมูล 1 ได้อีก
	// 2. ถ้ารูปแบบการประมูล 1 + สาขา กรุงเทพ พัทยา จะไม่สามารถเพิ่ม รูปแบบการประมูล 1 + สาขา กรุงเทพ หรือ รูปแบบการประมูล 1 + สาขา พัทยาได้ เพราะถือว่ามีแล้ว

	auctionTags, err := s.RepoAuctionTag.FindAuctionTags()
	if err != nil {
		return errs.NewError(http.StatusNotFound, err)
	}

	// collected id
	existingTagIds := []int{}
	for _, v := range auctionTags {
		existingTagIds = append(existingTagIds, v.Id)
	}

	// Build a map from auction tag ID to auction ID using the tagsExceptReq slice.
	auctionIdMap := make(map[int]int)
	for _, tag := range auctionTags {
		auctionIdMap[tag.Id] = tag.AuctionId
	}

	auctionTagBranchs, err := s.RepoAuctionTagBranch.GetAuctionTagBranchesByAuctionTagIds(existingTagIds)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	checkDub := false
	if len(auctionTagBranchs) != 0 {

		// เช็ค มี สาขา All ไหม ถ้ามี แต่ len เป็น 1 สามารถ insert ป้ายประมูลได้เลย ถ้าไม่ให่ วน for เช็ค duplicate

		if len(auctionTagBranchs) > 1 {
			// วน for เพื่อเช็คว่าซ้ำไหม start
			for _, v := range auctionTagBranchs {
				// check ข้อมูลจาก request และ database
				if auctionIdMap[*v.AuctionTagId] == *req.AuctionId {
					if v.BranchId != nil {
						for _, branch := range req.BranchIds {
							if *v.BranchId == int(*branch) {
								checkDub = true
								return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "AuctionTag duplicated", "error.auctionTag.duplicateAuctionTag")
							}
						}
					} else {
						if req.BranchIds == nil {
							checkDub = true
							return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "AuctionTag duplicated", "error.auctionTag.duplicateAuctionTag")
						}
						for _, branch := range req.BranchIds {
							if branch == nil || *branch == 0 {
								checkDub = true
								return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "AuctionTag duplicated", "error.auctionTag.duplicateAuctionTag")
							}
						}
					}
				}
			}
			// วน for เพื่อเช็คว่าซ้ำไหม end
		} else if len(auctionTagBranchs) == 1 && auctionTagBranchs[len(auctionTagBranchs)-1].BranchId == nil {
			// create ได้เลย เพราะ สาขา เป็น All และ ยังไม่มี สาขาอื่นเลย
			// insert to auction tag

			entityAuctionTag := &entity.AuctionTag{
				AuctionId:  util.Val(req.AuctionId),
				IsActive:   req.IsActive,
				BaseEntity: &model.BaseEntity{},
			}

			// NOTE - Create AuctionTag entity from req
			entityAuctionTag.BaseEntity = &model.BaseEntity{
				CreatedBy:   actionBy,
				CreatedDate: now,
				UpdatedBy:   actionBy,
				UpdatedDate: &now,
			}

			errTx := util.WithTx(s.RepoAuctionTag.GetDB(), func(tx *gorm.DB) error {

				err := s.RepoAuctionTag.InsertAuctionTag(tx, entityAuctionTag)
				if err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

				branches := make([]entity.AuctionTagBranch, 0, len(req.BranchIds))

				for _, branch := range req.BranchIds {
					branchInt := int(*branch)
					branch := entity.AuctionTagBranch{
						AuctionTagId: &entityAuctionTag.Id,
						BranchId:     &branchInt,
					}
					branches = append(branches, branch)
				}
				if len(branches) > 0 {
					if err := s.RepoAuctionTagBranch.InsertAuctionTagBranches(tx, branches); err != nil {
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}

				return nil

			})
			if errTx != nil {
				return errTx
			}
		}
	}

	if !checkDub || len(auctionTagBranchs) == 0 {
		// ไม่มีซ้ำ insert ได้
		// insert to auction tag

		entityAuctionTag := &entity.AuctionTag{
			AuctionId: util.Val(req.AuctionId),
			IsActive:  req.IsActive,
		}

		// NOTE - Create AuctionTag entity from req
		entityAuctionTag.BaseEntity = &model.BaseEntity{
			CreatedBy:   actionBy,
			CreatedDate: now,
			UpdatedBy:   actionBy,
			UpdatedDate: &now,
		}

		errTx := util.WithTx(s.RepoAuctionTag.GetDB(), func(tx *gorm.DB) error {

			err := s.RepoAuctionTag.InsertAuctionTag(tx, entityAuctionTag)
			if err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}

			branches := make([]entity.AuctionTagBranch, 0, len(req.BranchIds))

			for _, branch := range req.BranchIds {
				branchInt := int(*branch)
				branch := entity.AuctionTagBranch{
					AuctionTagId: &entityAuctionTag.Id,
					BranchId:     &branchInt,
				}
				branches = append(branches, branch)
			}
			if len(branches) > 0 {
				if err := s.RepoAuctionTagBranch.InsertAuctionTagBranches(tx, branches); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			} else {
				branch := entity.AuctionTagBranch{
					AuctionTagId: &entityAuctionTag.Id,
					BranchId:     nil,
				}
				if err := s.RepoAuctionTagBranch.InsertAuctionTagBranches(tx, []entity.AuctionTagBranch{branch}); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}

			return nil

		})
		if errTx != nil {
			return errTx
		}

	}

	return nil

}

func (s *auctionTagService) GetAssetTypeByAuctionID(auctionId int) ([]dto.AuctionAssetType, error) {

	results, err := s.RepoAuctionAsset.FindAssetTypesByAuctionId(auctionId)
	if err != nil {
		if errs.IsGormNotFound(err) {
			return []dto.AuctionAssetType{}, errs.NewError(http.StatusNotFound, err)
		}
		return []dto.AuctionAssetType{}, errs.NewError(http.StatusInternalServerError, err)
	}

	resp := []dto.AuctionAssetType{}
	for _, v := range results {
		resp = append(resp, dto.AuctionAssetType{
			AssetTypeId:   v.AssetTypeId,
			AssetTypeCode: &v.AssetTypeCode,
			DescriptionTh: &v.DescriptionTh,
			DescriptionEn: &v.DescriptionEn,
		})
	}

	return resp, nil
}

func (s *auctionTagService) GetAuctionTagsNew(req model.PagingRequest) (dto.AuctionTagPageRespDto[dto.AuctionTagRespDto], error) {
	resp := dto.AuctionTagPageRespDto[dto.AuctionTagRespDto]{}

	// Step 1 - Get All Auction Tags
	results, err := s.RepoAuctionTag.FindAuctionTagsPageSimple(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.RepoAuctionTag.CountAuctionTagsWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	// หลังจากได้ results จาก query หลักแล้ว
	auctionTagIds := make([]int, 0, len(results))
	for _, at := range results {
		auctionTagIds = append(auctionTagIds, at.Id)
	}

	// ดึงข้อมูล asset_type ทั้งหมด
	assetTypesMap, err := s.RepoAuctionTag.FindAssetTypesByAuctionTagIds(auctionTagIds)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Get All Auction Tag IDs
	var ids []int
	for _, n := range results {
		ids = append(ids, n.BaseEntity.Id)
	}

	branchMap, err := s.RepoAuctionTagBranch.GetBranchByAuctionTagIds(ids)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}
	// log.Info(branchMap)

	//NOTE - Map to DTO
	dtoList := make([]dto.AuctionTagRespDto, 0, len(results))
	for _, entity := range results {
		dtoResult := dto.AuctionTagRespDto{}

		dtoResult = util.MapToWithCreatedByAndUpdatedBy[dto.AuctionTagRespDto](entity)
		dtoResult.AuctionId = &entity.AuctionId
		dtoResult.AuctionName = entity.AuctionName
		dtoResult.IsActive = entity.IsActive
		dtoResult.StartTagNumber = entity.StartTagNumber
		dtoResult.EndTagNumber = entity.EndTagNumber
		dtoResult.CreatedDate = entity.CreatedDate
		dtoResult.UpdatedDate = entity.UpdatedDate
		for _, branch := range branchMap[entity.BaseEntity.Id] {

			if branch.Branch != nil {
				dtoResult.BranchList = append(dtoResult.BranchList, dto.BranchDto{
					ID:           &branch.Branch.ID,
					BranchCode:   util.Val(&branch.Branch.BranchCode),
					BranchNameTh: util.Val(&branch.Branch.DescriptionTh),
					BranchNameEn: util.Val(&branch.Branch.DescriptionEn),
				})
			} else {
				// only case nil branch
				if branch.BranchId == nil {
					defaultBranchId := 0
					defaultBranchCode := "ALL"
					defaultBranchNameTh := "ทุกสาขา"
					defaultBranchNameEn := "All Branches"
					dtoResult.BranchList = append(dtoResult.BranchList, dto.BranchDto{
						ID:           &defaultBranchId,
						BranchCode:   &defaultBranchCode,
						BranchNameTh: &defaultBranchNameTh,
						BranchNameEn: &defaultBranchNameEn,
					})
				}

			}
		}

		for _, at := range assetTypesMap[entity.Id] {
			dtoResult.AuctionAssetTypeList = append(dtoResult.AuctionAssetTypeList, &dto.AuctionAssetType{
				AssetTypeId:   at.AssetTypeId,
				AssetTypeCode: at.AssetTypeCode,
				DescriptionTh: at.DescriptionTh,
				DescriptionEn: at.DescriptionEn,
			})
		}

		dtoList = append(dtoList, dtoResult)
	}

	resp = dto.AuctionTagPageRespDto[dto.AuctionTagRespDto]{
		PagingModel: *util.MapPaginationResult(dtoList, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
	}

	return resp, nil

}

func (s *auctionTagService) GetAuctionTagByIdNew(auctionTagId int) (dto.AuctionTagPageRespDto[dto.AuctionTagRespDto], error) {
	resp := dto.AuctionTagPageRespDto[dto.AuctionTagRespDto]{}

	// NOTE - ActionTag By Id
	result, err := s.RepoAuctionTag.FindAuctionTagsPageSimpleById(auctionTagId)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}
	// หลังจากได้ results จาก query หลักแล้ว
	if result.AuctionId == 0 {
		return resp, errs.NewError(http.StatusNotFound, err)
	}
	auctionTagIds := make([]int, 0, 1)
	auctionTagIds = append(auctionTagIds, result.Id)

	// ดึงข้อมูล asset_type ทั้งหมด
	assetTypesMap, err := s.RepoAuctionTag.FindAssetTypesByAuctionTagIds(auctionTagIds)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Get All Auction Tag IDs
	var ids []int
	ids = append(ids, result.BaseEntity.Id)

	branchMap, err := s.RepoAuctionTagBranch.GetBranchByAuctionTagIds(ids)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	dtoList := make([]dto.AuctionTagRespDto, 0, 1)

	dtoResult := dto.AuctionTagRespDto{}

	dtoResult = util.MapToWithCreatedByAndUpdatedBy[dto.AuctionTagRespDto](result) // nd
	dtoResult.AuctionId = &result.AuctionId
	dtoResult.AuctionName = result.AuctionName
	dtoResult.IsActive = result.IsActive
	dtoResult.StartTagNumber = result.StartTagNumber
	dtoResult.EndTagNumber = result.EndTagNumber
	dtoResult.CreatedDate = result.CreatedDate
	dtoResult.UpdatedDate = result.UpdatedDate
	for _, branch := range branchMap[result.BaseEntity.Id] {

		if branch.Branch != nil {
			dtoResult.BranchList = append(dtoResult.BranchList, dto.BranchDto{
				ID:           &branch.Branch.ID,
				BranchCode:   util.Val(&branch.Branch.BranchCode),
				BranchNameTh: util.Val(&branch.Branch.DescriptionTh),
				BranchNameEn: util.Val(&branch.Branch.DescriptionEn),
			})
		} else {
			// only case nil branch
			if branch.BranchId == nil {
				defaultBranchId := 0
				defaultBranchCode := "ALL"
				defaultBranchNameTh := "ทุกสาขา"
				defaultBranchNameEn := "All Branches"
				dtoResult.BranchList = append(dtoResult.BranchList, dto.BranchDto{
					ID:           &defaultBranchId,
					BranchCode:   &defaultBranchCode,
					BranchNameTh: &defaultBranchNameTh,
					BranchNameEn: &defaultBranchNameEn,
				})
			}

		}
	}

	for _, at := range assetTypesMap[result.Id] {
		dtoResult.AuctionAssetTypeList = append(dtoResult.AuctionAssetTypeList, &dto.AuctionAssetType{
			AssetTypeId:   at.AssetTypeId,
			AssetTypeCode: at.AssetTypeCode,
			DescriptionTh: at.DescriptionTh,
			DescriptionEn: at.DescriptionEn,
		})
	}

	dtoList = append(dtoList, dtoResult)

	resp = dto.AuctionTagPageRespDto[dto.AuctionTagRespDto]{
		PagingModel: *util.MapPaginationResult(dtoList, int(1), 0, 1),
	}

	return resp, nil
}

func (s *auctionTagService) UpdateAuctionTagStatusNew(req dto.AuctionTagUpdateReqDto) error {

	if req.ActionBy == nil {
		return errs.NewBusinessError(fiber.StatusBadRequest, constant.BadRequest, "actionBy is required", "")
	}

	// Step 1: Check if auction_tag exists
	existingTag, err := s.RepoAuctionTag.FindAuctionTagsPageSimpleById(req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if existingTag == nil {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("auction tag with id %d not found", req.Id), "")
	}

	// Get Auction Tag Except Req
	tagsExceptReq, err := s.RepoAuctionTag.FindByIdsExcept(req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	// collected id
	existingTagIds := []int{}
	for _, v := range tagsExceptReq {
		existingTagIds = append(existingTagIds, v.Id)
	}

	// Build a map from auction tag ID to auction ID using the tagsExceptReq slice.
	auctionIdMap := make(map[int]int)
	for _, tag := range tagsExceptReq {
		auctionIdMap[tag.Id] = tag.AuctionId
	}

	existingBranches, err := s.RepoAuctionTagBranch.GetAuctionTagBranchesByAuctionTagIds(existingTagIds)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	// Step 2:  Check Duplicate
	// Updated duplicate check logic: Only compare branch IDs when the auction_id matches
	for _, v := range existingBranches {
		if req.AuctionId != nil && auctionIdMap[*v.AuctionTagId] == *req.AuctionId {
			if v.BranchId != nil {
				for _, branch := range req.BranchIds {
					if *v.BranchId == int(*branch) {
						return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "AuctionTag duplicated", "error.auctionTag.duplicateAuctionTag")
					}
				}
			} else {
				if req.BranchIds == nil {
					return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "AuctionTag duplicated", "error.auctionTag.duplicateAuctionTag")
				}
				for _, branch := range req.BranchIds {
					if branch == nil || *branch == 0 {
						return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "AuctionTag duplicated", "error.auctionTag.duplicateAuctionTag")
					}
				}
			}
		}

	}

	// Step 3: Build new tag branches to insert
	newBranches := make([]entity.AuctionTagBranch, 0, len(req.BranchIds))
	for _, branchId := range req.BranchIds {
		id := int(*branchId)
		newBranches = append(newBranches, entity.AuctionTagBranch{
			AuctionTagId: &req.Id,
			BranchId:     &id,
		})
	}

	// Step 4: Update auction_tag and branch data in transaction

	// Case IsActive Only
	if req.BranchIds == nil && req.AuctionId == nil && req.StartTagNumber == nil && req.EndTagNumber == nil {

		errTx := util.WithTx(s.RepoAuctionTag.GetDB(), func(tx *gorm.DB) error {

			// Update main auction_tag record
			fieldsToUpdate := map[string]interface{}{
				"is_active":    req.IsActive,
				"updated_by":   req.ActionBy,
				"updated_date": util.Now(),
			}
			if req.AuctionId != nil {
				fieldsToUpdate["auction_id"] = req.AuctionId
			}
			if req.StartTagNumber != nil {
				fieldsToUpdate["start_tag_number"] = req.StartTagNumber
			}
			if req.EndTagNumber != nil {
				fieldsToUpdate["end_tag_number"] = req.EndTagNumber
			}
			if _, err := s.RepoAuctionTag.UpdatesAuctionTagFieldsWhere(tx, fieldsToUpdate, "id = ?", req.Id); err != nil {
				return err
			}

			// Insert new branch relations
			if len(newBranches) > 0 {
				if err := s.RepoAuctionTagBranch.InsertAuctionTagBranches(tx, newBranches); err != nil {
					return err
				}
			}
			return nil
		})

		if errTx != nil {
			return errs.NewError(http.StatusInternalServerError, errTx)
		}

	} else {

		errTx := util.WithTx(s.RepoAuctionTag.GetDB(), func(tx *gorm.DB) error {

			// Delete old branches
			if err := s.RepoAuctionTagBranch.Delete(tx, req.Id); err != nil {
				return err
			}

			// Update main auction_tag record
			fieldsToUpdate := map[string]interface{}{
				"is_active":    req.IsActive,
				"updated_by":   req.ActionBy,
				"updated_date": util.Now(),
			}
			if req.AuctionId != nil {
				fieldsToUpdate["auction_id"] = req.AuctionId
			}
			if req.StartTagNumber != nil {
				fieldsToUpdate["start_tag_number"] = req.StartTagNumber
			}
			if req.EndTagNumber != nil {
				fieldsToUpdate["end_tag_number"] = req.EndTagNumber
			}
			if _, err := s.RepoAuctionTag.UpdatesAuctionTagFieldsWhere(tx, fieldsToUpdate, "id = ?", req.Id); err != nil {
				return err
			}

			// Insert new branch relations
			if len(newBranches) > 0 {
				if err := s.RepoAuctionTagBranch.InsertAuctionTagBranches(tx, newBranches); err != nil {
					return err
				}
			} else {
				return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "BranchIds is required", "error.auctionTag.branchIdsIsRequired")
			}

			return nil
		})

		if errTx != nil {
			return errs.NewError(http.StatusInternalServerError, errTx)
		}
	}

	return nil
}

func (s *auctionTagService) DeleteAuctionTagNew(id int) error {

	errTx := util.WithTx(s.RepoAuctionTag.GetDB(), func(tx *gorm.DB) error {
		// Step 1: Delete auction_tag
		err := s.RepoAuctionTag.Delete(tx, id)
		if err != nil {
			if err.Error() == fmt.Sprintf("auction tag with id %d not found", id) {
				return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", id), "")
			}
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		// Step 2: Delete auction_tag_branch by auction_tag_id
		err = s.RepoAuctionTagBranch.Delete(tx, id)
		if err != nil {
			if err.Error() == fmt.Sprintf("auction tag branch with id %d not found", id) {
				// Don't return error if branch not found, just log it
				log.Warnf("No auction_tag_branch found for auction_tag_id %d", id)
			} else {
				log.Error(err)
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		return nil
	})
	if errTx != nil {
		return errTx
	}

	return nil
}
