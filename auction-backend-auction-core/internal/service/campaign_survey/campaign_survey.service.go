package service

import (
	"auction-core-service/internal/model/dto"
	campaignRepository "auction-core-service/internal/repository/campaign"
	campaignAssetGroupRepository "auction-core-service/internal/repository/campaign_asset_group"
	campaignCustomerGroupRepository "auction-core-service/internal/repository/campaign_customer_group"
	campaignDisplayLocationRepository "auction-core-service/internal/repository/campaign_display_location"
	commonAuctionCoreRepository "auction-core-service/internal/repository/common_auction_core"
	"backend-common-lib/model"
)

type campaignSurveyService struct {
	CampaignRepo                campaignRepository.CampaignRepository
	CampaignAssetGroupRepo      campaignAssetGroupRepository.CampaignAssetGroupRepository
	CampaignCustomerGroupRepo   campaignCustomerGroupRepository.CampaignCustomerGroupRepository
	CampaignDisplayLocationRepo campaignDisplayLocationRepository.CampaignDisplayLocationRepository
	CommonAuctionCoreRepo       commonAuctionCoreRepository.CommonAuctionCoreRepository
}

type CampaignSurveyService interface {
	GetAllCampaignSurvey(req model.PagingRequest) (dto.CampaignSurveyPageRespDto[dto.CampaignSurveyFilterRespDto], error)
	GetCampaignById(id int) (*dto.CampaignDto, error)
	CreateCampaign(req dto.CampaignDto) error
	UpdateCampaign(req dto.CampaignDto) error
	UpdateCampaignStatus(req dto.CampaignStatusReqDto) error
	DeleteCampaign(id int, actionBy *int) error
}

func NewCampaignSurveyService(campaignRepo campaignRepository.CampaignRepository,
	campaignAssetGroupRepo campaignAssetGroupRepository.CampaignAssetGroupRepository,
	campaignCustomerGroupRepo campaignCustomerGroupRepository.CampaignCustomerGroupRepository,
	campaignDisplayLocationRepo campaignDisplayLocationRepository.CampaignDisplayLocationRepository,
	commonAuctionCoreRepo commonAuctionCoreRepository.CommonAuctionCoreRepository,
) CampaignSurveyService {
	return &campaignSurveyService{CampaignRepo: campaignRepo,
		CampaignAssetGroupRepo:      campaignAssetGroupRepo,
		CampaignCustomerGroupRepo:   campaignCustomerGroupRepo,
		CampaignDisplayLocationRepo: campaignDisplayLocationRepo,
		CommonAuctionCoreRepo:       commonAuctionCoreRepo}

}
