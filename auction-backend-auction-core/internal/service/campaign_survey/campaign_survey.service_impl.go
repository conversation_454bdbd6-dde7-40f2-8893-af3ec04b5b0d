package service

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	campaignRepository "auction-core-service/internal/repository/campaign"
	campaignAssetGroupRepository "auction-core-service/internal/repository/campaign_asset_group"
	campaignCustomerGroupRepository "auction-core-service/internal/repository/campaign_customer_group"
	campaignDisplayLocationRepository "auction-core-service/internal/repository/campaign_display_location"
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2/log"
	"gorm.io/gorm"
)

func (s *campaignSurveyService) GetAllCampaignSurvey(req model.PagingRequest) (dto.CampaignSurveyPageRespDto[dto.CampaignSurveyFilterRespDto], error) {
	results, err := s.CampaignRepo.FindCampaignWithFilter(req)
	if err != nil {
		log.Error(err)
		return dto.CampaignSurveyPageRespDto[dto.CampaignSurveyFilterRespDto]{}, errs.NewError(http.StatusInternalServerError, err)
	}

	count, err := s.CampaignRepo.CountCampaignWithFilter(req)
	if err != nil {
		return dto.CampaignSurveyPageRespDto[dto.CampaignSurveyFilterRespDto]{}, errs.NewError(http.StatusInternalServerError, err)
	}

	var campaignIds []int
	for _, campaign := range results {
		campaignIds = append(campaignIds, campaign.Id)
	}

	campaignAssetGroupDbs, err := s.CampaignAssetGroupRepo.GetAllByCampaignIds(campaignIds)
	if err != nil {
		log.Error(err)
		return dto.CampaignSurveyPageRespDto[dto.CampaignSurveyFilterRespDto]{}, errs.NewError(http.StatusInternalServerError, err)
	}

	campaignCustomerGroupDbs, err := s.CampaignCustomerGroupRepo.GetAllByCampaignIds(campaignIds)
	if err != nil {
		log.Error(err)
		return dto.CampaignSurveyPageRespDto[dto.CampaignSurveyFilterRespDto]{}, errs.NewError(http.StatusInternalServerError, err)
	}

	campaignDisplayLocationDbs, err := s.CampaignDisplayLocationRepo.GetAllByCampaignIds(campaignIds)
	if err != nil {
		log.Error(err)
		return dto.CampaignSurveyPageRespDto[dto.CampaignSurveyFilterRespDto]{}, errs.NewError(http.StatusInternalServerError, err)
	}

	resultAssetTypes, err := s.CommonAuctionCoreRepo.GetAllAssetType()
	if err != nil {
		log.Error(err)
		return dto.CampaignSurveyPageRespDto[dto.CampaignSurveyFilterRespDto]{}, errs.NewError(http.StatusInternalServerError, err)
	}

	campaignAssetGroupMap := make(map[int][]*string)
	campaignAssetGroupEntityMap := make(map[int][]*model.AssetGroupForJoin)
	campaignCustomerGroupDbMap := make(map[int][]*string)
	campaignDisplayLocationDbMap := make(map[int][]*string)
	assetTypeMap := make(map[string]string)

	for _, campaignAssetGroupDb := range campaignAssetGroupDbs {
		campaignAssetGroupMap[util.Val(campaignAssetGroupDb.CampaignId)] = append(campaignAssetGroupMap[util.Val(campaignAssetGroupDb.CampaignId)], &campaignAssetGroupDb.AssetGroupForJoin.DescriptionTh)
		campaignAssetGroupEntityMap[util.Val(campaignAssetGroupDb.CampaignId)] = append(campaignAssetGroupEntityMap[util.Val(campaignAssetGroupDb.CampaignId)], campaignAssetGroupDb.AssetGroupForJoin)
	}

	for _, campaignCustomerGroupDb := range campaignCustomerGroupDbs {
		campaignCustomerGroupDbMap[util.Val(campaignCustomerGroupDb.CampaignId)] = append(campaignCustomerGroupDbMap[util.Val(campaignCustomerGroupDb.CampaignId)], &campaignCustomerGroupDb.CustomerGroupForJoin.DescriptionTh)
	}

	for _, campaignDisplayLocationDb := range campaignDisplayLocationDbs {
		campaignDisplayLocationDbMap[util.Val(campaignDisplayLocationDb.CampaignId)] = append(campaignDisplayLocationDbMap[util.Val(campaignDisplayLocationDb.CampaignId)], campaignDisplayLocationDb.DisplayLocation)
	}

	for _, assetType := range resultAssetTypes {
		assetTypeMap[assetType.AssetTypeCode] = assetType.DescriptionTh
	}

	campaign := "แคมเปญ"

	campaignSurveyFilterRespDto := []dto.CampaignSurveyFilterRespDto{}
	for _, v := range results {

		var assetTypes []*string

		if campaignAssetGroupEntityMap[v.Id] != nil {
			for _, assetGroup := range campaignAssetGroupEntityMap[v.Id] {
				descriptionTh := assetTypeMap[assetGroup.AssetTypeCode]
				assetTypes = append(assetTypes, &descriptionTh)
			}
		}

		startDate := v.StartDate.Format(time.DateOnly)

		var endDate string
		if v.EndDate != nil {
			endDate = v.EndDate.Format(time.DateOnly)
		}

		campaignSurveyFilterRespDto = append(campaignSurveyFilterRespDto, dto.CampaignSurveyFilterRespDto{
			Id:               v.Id,
			Type:             &campaign,
			Name:             v.Name,
			Event:            v.EventForJoin.DescriptionTh,
			Finance:          v.VendorGroupForJoin.VendorGroupCode,
			AssetTypes:       assetTypes,
			AssetGroups:      campaignAssetGroupMap[v.Id],
			UserGroups:       campaignCustomerGroupDbMap[v.Id],
			StartDate:        &startDate,
			EndDate:          &endDate,
			DisplayLocations: campaignDisplayLocationDbMap[v.Id],
			IsActive:         v.IsActive,
		})
	}

	resp := dto.CampaignSurveyPageRespDto[dto.CampaignSurveyFilterRespDto]{
		PagingModel: *util.MapPaginationResult(campaignSurveyFilterRespDto, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
	}

	return resp, nil
}

func (s *campaignSurveyService) GetCampaignById(id int) (*dto.CampaignDto, error) {
	campaign, err := s.CampaignRepo.GetById(id)
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	campaignAssetGroupDbs, err := s.CampaignAssetGroupRepo.GetAllByCampaignId(campaign.Id)
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	campaignCustomerGroupDbs, err := s.CampaignCustomerGroupRepo.GetAllByCampaignId(campaign.Id)
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	campaignDisplayLocationDbs, err := s.CampaignDisplayLocationRepo.GetAllByCampaignId(campaign.Id)
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	var campaignAssetGroupIds []*int
	var campaignCustomerGroupIds []*int
	var campaignDisplayLocationIds []dto.CampaignDisplayLocationDto

	for _, campaignAssetGroupDb := range campaignAssetGroupDbs {
		campaignAssetGroupIds = append(campaignAssetGroupIds, campaignAssetGroupDb.AssetGroupId)
	}

	for _, campaignCustomerGroupDb := range campaignCustomerGroupDbs {
		campaignCustomerGroupIds = append(campaignCustomerGroupIds, campaignCustomerGroupDb.CustomerGroupId)
	}

	for _, campaignDisplayLocationDb := range campaignDisplayLocationDbs {
		campaignDisplayLocationIds = append(campaignDisplayLocationIds, dto.CampaignDisplayLocationDto{
			DisplayLocationId: campaignDisplayLocationDb.DisplayLocationId,
			IsCheck:           campaignDisplayLocationDb.IsCheck,
		})
	}

	startDate := campaign.StartDate.Format(constant.DateFormatDMY)
	endDate := campaign.EndDate.Format(constant.DateFormatDMY)

	resp := dto.CampaignDto{
		BaseDtoActionBy: model.BaseDtoActionBy{Id: campaign.Id},
		Name:            campaign.Name,
		EventTypeId:     campaign.EventTypeId,
		EventId:         campaign.EventId,
		FinanceId:       campaign.VendorGroupId,
		AssetGroupIds:   campaignAssetGroupIds,
		UserGroupIds:    campaignCustomerGroupIds,
		Description:     campaign.Description,
		IsLinkUrl:       campaign.IsLinkUrl,
		LinkUrl:         campaign.LinkUrl,
		IsUpload:        campaign.IsUpload,
		// FilePath:         campaign.FilePath,
		StartDate:        &startDate,
		EndDate:          &endDate,
		IsActive:         campaign.IsActive,
		DisplayLocations: campaignDisplayLocationIds,
	}

	return &resp, nil
}

func (s *campaignSurveyService) CreateCampaign(req dto.CampaignDto) error {
	err := validateData(req)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	campaign := prepareCreateCampaignToDb(req)
	campaignAssetGroups := prepareCreateCampaignAssetGroupToDb(req.AssetGroupIds, req.ActionBy)
	campaignCustomerGroups := prepareCreateCampaignCustomerGroupToDb(req.UserGroupIds, req.ActionBy)
	campaignDisplayLocations := prepareCreateCampaignDisplayLocationToDb(req.DisplayLocations, req.ActionBy)

	err = s.createCampaignToDb(campaign, campaignAssetGroups, campaignCustomerGroups, campaignDisplayLocations)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return nil
}

func (s *campaignSurveyService) UpdateCampaign(req dto.CampaignDto) error {
	err := validateData(req)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	campaign, err := s.prepareUpdateCampaignToDb(req)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	campaignAssetGroups := prepareCreateCampaignAssetGroupToDb(req.AssetGroupIds, req.ActionBy)
	campaignCustomerGroups := prepareCreateCampaignCustomerGroupToDb(req.UserGroupIds, req.ActionBy)
	campaignDisplayLocations := prepareCreateCampaignDisplayLocationToDb(req.DisplayLocations, req.ActionBy)

	err = s.updateCampaignToDb(campaign, campaignAssetGroups, campaignCustomerGroups, campaignDisplayLocations)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return nil
}

func (s *campaignSurveyService) UpdateCampaignStatus(req dto.CampaignStatusReqDto) error {
	_, err := s.CampaignRepo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	err = s.CampaignRepo.UpdateStatus(req.Id, fieldsToUpdate)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return nil
}

func (s *campaignSurveyService) DeleteCampaign(id int, actionBy *int) error {
	_, err := s.CampaignRepo.GetById(id)
	if err != nil {
		log.Error(err)
		return err
	}

	return util.WithTx(s.CampaignRepo.GetDB(), func(tx *gorm.DB) error {
		campaignRepo := campaignRepository.NewCampaignRepository(tx)
		campaignAssetGroupRepo := campaignAssetGroupRepository.NewCampaignAssetGroupRepository(tx)
		campaignCustomerGroupRepo := campaignCustomerGroupRepository.NewCampaignCustomerGroupRepository(tx)
		campaignCampaignDisplayRepo := campaignDisplayLocationRepository.NewCampaignDisplayLocationRepository(tx)

		err := campaignRepo.Delete(id)
		if err != nil {
			log.Error(err)
			return err
		}

		err = campaignAssetGroupRepo.DeleteByCampaignId(id)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}
		err = campaignCustomerGroupRepo.DeleteByCampaignId(id)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}
		err = campaignCampaignDisplayRepo.DeleteByCampaignId(id)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		return nil
	})
}

func validateData(req dto.CampaignDto) error {
	if util.Val(req.Name) == "" {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "name is required", "")
	}
	if util.Val(req.EventTypeId) == 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "eventTypeId is required", "")
	}
	if util.Val(req.EventId) == 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "eventId is required", "")
	}
	if util.Val(req.FinanceId) == 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "financeId is required", "")
	}
	if len(req.AssetGroupIds) == 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "assetGroupIds is required", "")
	}
	if len(req.UserGroupIds) == 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "userGroupIds is required", "")
	}
	if !util.Val(req.IsLinkUrl) && !util.Val(req.IsUpload) {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "isLinkUrl or isUpload is required", "")
	}
	//TO DO: check file
	// if util.Val(req.LinkUrl) == "" {
	// 	return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "linkUrl is required", "")
	// }
	if util.Val(req.StartDate) == "" {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "startDate is required", "")
	}
	if len(req.DisplayLocations) == 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "displayLocations is required", "")
	}

	return nil
}

func prepareCreateCampaignToDb(req dto.CampaignDto) entity.Campaign {

	layoutDMY := constant.DateFormatDMY

	startDate, _ := time.Parse(layoutDMY, util.Val(req.StartDate))

	campaign := entity.Campaign{
		Name:          req.Name,
		EventTypeId:   req.EventTypeId,
		EventId:       req.EventId,
		VendorGroupId: req.FinanceId,
		Description:   req.Description,
		IsLinkUrl:     req.IsLinkUrl,
		LinkUrl:       req.LinkUrl,
		IsUpload:      req.IsUpload,
		// FilePath:      req.FilePath,
		StartDate: &startDate,
		IsActive:  req.IsActive,
		BaseEntity: &model.BaseEntity{
			CreatedDate: util.Now(),
			CreatedBy:   req.ActionBy,
			UpdatedDate: util.NowPtr(),
			UpdatedBy:   req.ActionBy,
		},
	}

	if util.Val(req.EndDate) != "" {
		endDate, _ := time.Parse(layoutDMY, util.Val(req.EndDate))
		campaign.EndDate = &endDate
	}

	return campaign

}

func prepareCreateCampaignAssetGroupToDb(campaignAssetGroupIds []*int, actionBy *int) []entity.CampaignAssetGroup {
	campaignAssetGroups := []entity.CampaignAssetGroup{}
	for _, campaignAssetGroupId := range campaignAssetGroupIds {
		campaignAssetGroup := entity.CampaignAssetGroup{
			AssetGroupId: campaignAssetGroupId,
			BaseEntity: &model.BaseEntity{
				CreatedDate: util.Now(),
				CreatedBy:   actionBy,
				UpdatedDate: util.NowPtr(),
				UpdatedBy:   actionBy,
			},
		}
		campaignAssetGroups = append(campaignAssetGroups, campaignAssetGroup)
	}

	return campaignAssetGroups
}

func prepareCreateCampaignCustomerGroupToDb(campaignCustomerGroupIds []*int, actionBy *int) []entity.CampaignCustomerGroup {
	campaignCustomerGroups := []entity.CampaignCustomerGroup{}
	for _, campaignCustomerGroupId := range campaignCustomerGroupIds {
		campaignCustomerGroup := entity.CampaignCustomerGroup{
			CustomerGroupId: campaignCustomerGroupId,
			BaseEntity: &model.BaseEntity{
				CreatedDate: util.Now(),
				CreatedBy:   actionBy,
				UpdatedDate: util.NowPtr(),
				UpdatedBy:   actionBy,
			},
		}
		campaignCustomerGroups = append(campaignCustomerGroups, campaignCustomerGroup)
	}

	return campaignCustomerGroups
}

func prepareCreateCampaignDisplayLocationToDb(displayLocations []dto.CampaignDisplayLocationDto, actionBy *int) []entity.CampaignDisplayLocation {
	campaignDisplayLocations := []entity.CampaignDisplayLocation{}
	for _, displayLocation := range displayLocations {
		campaignDisplayLocation := entity.CampaignDisplayLocation{
			DisplayLocationId: displayLocation.DisplayLocationId,
			IsCheck:           displayLocation.IsCheck,
			BaseEntity: &model.BaseEntity{
				CreatedDate: util.Now(),
				CreatedBy:   actionBy,
				UpdatedDate: util.NowPtr(),
				UpdatedBy:   actionBy,
			},
		}
		campaignDisplayLocations = append(campaignDisplayLocations, campaignDisplayLocation)
	}

	return campaignDisplayLocations
}

func (s *campaignSurveyService) createCampaignToDb(campaign entity.Campaign,
	campaignAssetGroups []entity.CampaignAssetGroup,
	campaignCustomerGroups []entity.CampaignCustomerGroup,
	campaignDisplayLocations []entity.CampaignDisplayLocation) error {

	return util.WithTx(s.CampaignRepo.GetDB(), func(tx *gorm.DB) error {
		campaignRepo := campaignRepository.NewCampaignRepository(tx)
		campaignAssetGroupRepo := campaignAssetGroupRepository.NewCampaignAssetGroupRepository(tx)
		campaignCustomerGroupRepo := campaignCustomerGroupRepository.NewCampaignCustomerGroupRepository(tx)
		campaignCampaignDisplayRepo := campaignDisplayLocationRepository.NewCampaignDisplayLocationRepository(tx)

		err := campaignRepo.Insert(campaign)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		for i := range campaignAssetGroups {
			campaignAssetGroups[i].CampaignId = &campaign.Id
		}

		err = campaignAssetGroupRepo.InsertList(campaignAssetGroups)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		for i := range campaignCustomerGroups {
			campaignCustomerGroups[i].CampaignId = &campaign.Id
		}

		err = campaignCustomerGroupRepo.InsertList(campaignCustomerGroups)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		for i := range campaignDisplayLocations {
			campaignDisplayLocations[i].CampaignId = &campaign.Id
		}

		err = campaignCampaignDisplayRepo.InsertList(campaignDisplayLocations)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		return nil
	})
}

func (s *campaignSurveyService) prepareUpdateCampaignToDb(req dto.CampaignDto) (entity.Campaign, error) {
	campaign, err := s.CampaignRepo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return campaign, errs.NewError(http.StatusInternalServerError, err)
	}

	layoutDMY := constant.DateFormatDMY

	startDate, _ := time.Parse(layoutDMY, util.Val(req.StartDate))

	campaign.Name = req.Name
	campaign.EventTypeId = req.EventTypeId
	campaign.EventId = req.EventId
	campaign.VendorGroupId = req.FinanceId
	campaign.Description = req.Description
	campaign.IsLinkUrl = req.IsLinkUrl
	campaign.LinkUrl = req.LinkUrl
	campaign.IsUpload = req.IsUpload
	campaign.StartDate = &startDate
	campaign.IsActive = req.IsActive
	campaign.UpdatedDate = util.NowPtr()
	campaign.UpdatedBy = req.ActionBy

	if util.Val(req.EndDate) != "" {
		endDate, _ := time.Parse(layoutDMY, util.Val(req.EndDate))
		campaign.EndDate = &endDate
	}

	return campaign, nil

}

func (s *campaignSurveyService) updateCampaignToDb(campaign entity.Campaign,
	campaignAssetGroups []entity.CampaignAssetGroup,
	campaignCustomerGroups []entity.CampaignCustomerGroup,
	campaignDisplayLocations []entity.CampaignDisplayLocation) error {

	return util.WithTx(s.CampaignRepo.GetDB(), func(tx *gorm.DB) error {
		campaignRepo := campaignRepository.NewCampaignRepository(tx)
		campaignAssetGroupRepo := campaignAssetGroupRepository.NewCampaignAssetGroupRepository(tx)
		campaignCustomerGroupRepo := campaignCustomerGroupRepository.NewCampaignCustomerGroupRepository(tx)
		campaignCampaignDisplayRepo := campaignDisplayLocationRepository.NewCampaignDisplayLocationRepository(tx)

		err := campaignRepo.UpdateAllFields(campaign)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		err = campaignAssetGroupRepo.DeleteByCampaignId(campaign.Id)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}
		err = campaignCustomerGroupRepo.DeleteByCampaignId(campaign.Id)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}
		err = campaignCampaignDisplayRepo.DeleteByCampaignId(campaign.Id)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		for i := range campaignAssetGroups {
			campaignAssetGroups[i].CampaignId = &campaign.Id
		}
		for i := range campaignCustomerGroups {
			campaignCustomerGroups[i].CampaignId = &campaign.Id
		}
		for i := range campaignDisplayLocations {
			campaignDisplayLocations[i].CampaignId = &campaign.Id
		}

		err = campaignAssetGroupRepo.InsertList(campaignAssetGroups)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		err = campaignCustomerGroupRepo.InsertList(campaignCustomerGroups)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		err = campaignCampaignDisplayRepo.InsertList(campaignDisplayLocations)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		return nil
	})
}
