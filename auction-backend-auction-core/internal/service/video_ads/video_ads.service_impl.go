package service

import (
	"errors"
	"fmt"
	"mime/multipart"
	"net/http"

	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"gorm.io/gorm"
)

func (s *videoAdsService) SearchAllVideoAds(req dto.VideoAdsPageReqDto) ([]dto.VideoAdsRespDto, error) {
	result, err := s.Repo.FindAllVideoAds(req)
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.VideoAdsRespDto, len(result))
	for i, v := range result {
		mapResult[i].Id = v.Id
		mapResult[i].CreatedBy = v.CreatedBy
		mapResult[i].CreatedDate = v.CreatedDate
		mapResult[i].UpdatedBy = v.UpdatedBy
		mapResult[i].UpdatedDate = v.UpdatedDate
		mapResult[i].IsActive = v.IsActive
		mapResult[i].VideoName = v.VideoName
		mapResult[i].LinkVideo = v.LinkVideo
		mapResult[i].EventId = v.EventId
		mapResult[i].EventDescriptionTh = v.EventDescriptionTh
		mapResult[i].EventDescriptionEn = v.EventDescriptionEn
		if v.StartDate != nil {
			mapResult[i].StartDate = util.Ptr(v.StartDate.Format(constant.DateFormatDMY))
		}
		if v.EndDate != nil {
			mapResult[i].EndDate = util.Ptr(v.EndDate.Format(constant.DateFormatDMY))
		}
		if v.StartTime != nil {
			mapResult[i].StartTime = util.Ptr(v.StartTime.Format(constant.TimeFormat))
		}
		if v.EndTime != nil {
			mapResult[i].EndTime = util.Ptr(v.EndTime.Format(constant.TimeFormat))
		}
	}

	return mapResult, nil
}

func (s *videoAdsService) CreateVideoAds(req dto.VideoAdsCreateReqDto, form *multipart.Form) error {
	var videoFile *multipart.FileHeader
	if files := form.File["videoFile"]; len(files) > 0 {
		videoFile = files[0]
	}

	//NOTE - Check Required fields
	if err := validateVideoAds(&req, videoFile); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	//NOTE - Map to Entity
	entityVideoAds := util.MapToPtr[entity.VideoAds](req)
	if entityVideoAds == nil {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("Invalid input data"))
	}

	//NOTE - Parse Date
	if err := parseDateFields(req, entityVideoAds); err != nil {
		return err
	}

	//NOTE - check duplicate
	exist, err := s.Repo.FindByEventIdAndVideoName(*entityVideoAds.EventId, *entityVideoAds.VideoName)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "event and video name is duplicated", "error.videoAds.duplicateEventAndVideoName")
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	//TODO - upload video file

	//NOTE - Save to DB
	now := util.Now()
	entityVideoAds.BaseEntity = &model.BaseEntity{
		CreatedBy:   req.ActionBy,
		CreatedDate: now,
		UpdatedBy:   req.ActionBy,
		UpdatedDate: &now,
	}
	if err := s.Repo.InsertVideoAds(*entityVideoAds); err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return nil
}

func validateVideoAds(videoAds *dto.VideoAdsCreateReqDto, videoFile *multipart.FileHeader) *errs.ErrContext {
	if videoAds.EventId == nil || *videoAds.EventId == 0 {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("event is required"))
	} else if videoAds.VideoName == nil || *videoAds.VideoName == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("video name is required"))
	} else if (videoAds.LinkVideo == nil || *videoAds.LinkVideo == "") && (videoFile == nil) {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("video is required"))
	} else if videoAds.StartDate == nil || *videoAds.StartDate == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("start date is required"))
	} else if videoAds.StartTime == nil || *videoAds.StartTime == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("start time is required"))
	}
	return nil
}

func parseDateFields(req dto.VideoAdsCreateReqDto, entity *entity.VideoAds) error {
	var err error

	if entity.StartDate, err = util.ParseDatePtr(req.StartDate, constant.DateFormatDMY); err != nil {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("invalid start date"))
	}
	if entity.EndDate, err = util.ParseDatePtr(req.EndDate, constant.DateFormatDMY); err != nil {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("invalid end date"))
	}
	if entity.StartTime, err = util.ParseDatePtr(req.StartTime, constant.TimeFormat); err != nil {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("invalid start time"))
	}
	if entity.EndTime, err = util.ParseDatePtr(req.EndTime, constant.TimeFormat); err != nil {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("invalid end time"))
	}
	return nil
}

func (s *videoAdsService) UpdateVideoAds(req dto.VideoAdsCreateReqDto, form *multipart.Form) error {
	var videoFile *multipart.FileHeader
	if files := form.File["videoFile"]; len(files) > 0 {
		videoFile = files[0]
	}

	//NOTE - Check Required fields
	if err := validateVideoAds(&req, videoFile); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	// //NOTE - Map to Entity
	entityVideoAds := util.MapToPtr[entity.VideoAds](req)
	if entityVideoAds == nil {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("Invalid input data"))
	}

	//NOTE - Parse Date
	if err := parseDateFields(req, entityVideoAds); err != nil {
		return err
	}

	//NOTE - check duplicate
	exist, err := s.Repo.FindDuplicateByEventIdAndVideoNameExcludingId(req.Id, *req.EventId, *req.VideoName)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "event and video name is duplicated", "error.videoAds.duplicateEventAndVideoName")
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	//TODO - upload video file

	//NOTE - Save to DB
	now := util.Now()
	fieldsToUpdate := map[string]interface{}{
		"is_active":    entityVideoAds.IsActive,
		"video_name":   entityVideoAds.VideoName,
		"link_video":   entityVideoAds.LinkVideo,
		"event_id":     entityVideoAds.EventId,
		"start_date":   entityVideoAds.StartDate,
		"end_date":     entityVideoAds.EndDate,
		"start_time":   entityVideoAds.StartTime,
		"end_time":     entityVideoAds.EndTime,
		"updated_by":   req.ActionBy,
		"updated_date": &now,
	}

	affectedRows, err := s.Repo.UpdatesVideoAdsFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}
	return nil
}

func (s *videoAdsService) GetVideoAdsById(id int) (dto.VideoAdsRespDto, error) {
	resp := dto.VideoAdsRespDto{}
	result, err := s.Repo.FindVideoAdsById(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return resp, errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", id), "")
		}
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	resp.Id = result.Id
	resp.CreatedBy = result.CreatedBy
	resp.CreatedDate = result.CreatedDate
	resp.UpdatedBy = result.UpdatedBy
	resp.UpdatedDate = result.UpdatedDate
	resp.IsActive = result.IsActive
	resp.VideoName = result.VideoName
	resp.LinkVideo = result.LinkVideo
	resp.EventId = result.EventId
	resp.EventDescriptionTh = result.EventDescriptionTh
	resp.EventDescriptionEn = result.EventDescriptionEn
	if result.StartDate != nil {
		resp.StartDate = util.Ptr(result.StartDate.Format(constant.DateFormatDMY))
	}
	if result.EndDate != nil {
		resp.EndDate = util.Ptr(result.EndDate.Format(constant.DateFormatDMY))
	}
	if result.StartTime != nil {
		resp.StartTime = util.Ptr(result.StartTime.Format(constant.TimeFormat))
	}
	if result.EndTime != nil {
		resp.EndTime = util.Ptr(result.EndTime.Format(constant.TimeFormat))
	}

	return resp, nil
}

func (s *videoAdsService) DeleteVideoAdsById(id int) error {
	affectedRows, err := s.Repo.DeleteVideoAdsById(id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", id), "")
	}
	return nil
}
