package service

import (
	"auction-core-service/internal/model/dto"
	videoAdsRepository "auction-core-service/internal/repository/video_ads"
	"mime/multipart"
)

type videoAdsService struct {
	Repo videoAdsRepository.VideoAdsRepository
}

type VideoAdsService interface {
	SearchAllVideoAds(req dto.VideoAdsPageReqDto) ([]dto.VideoAdsRespDto, error)
	CreateVideoAds(req dto.VideoAdsCreateReqDto, form *multipart.Form) error
	UpdateVideoAds(req dto.VideoAdsCreateReqDto, form *multipart.Form) error
	GetVideoAdsById(id int) (dto.VideoAdsRespDto, error)
	DeleteVideoAdsById(id int) error
}

func NewVideoAdsService(repo videoAdsRepository.VideoAdsRepository,
) VideoAdsService {
	return &videoAdsService{Repo: repo}

}
