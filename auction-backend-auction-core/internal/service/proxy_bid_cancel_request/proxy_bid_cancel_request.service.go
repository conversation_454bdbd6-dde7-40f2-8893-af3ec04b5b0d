package service

import (
	"auction-core-service/internal/model/dto"
	proxyBidRepository "auction-core-service/internal/repository/proxy_bid"
	proxyBidCancelRequestRepository "auction-core-service/internal/repository/proxy_bid_cancel_request"
	"backend-common-lib/model"
)

type proxyBidCancelRequestService struct {
	Repo         proxyBidCancelRequestRepository.ProxyBidCancelRequestRepository
	ProxyBidRepo proxyBidRepository.ProxyBidRepository
}

type ProxyBidCancelRequestService interface {
	SearchProxyBidCancelRequestFilter(req dto.ProxyBidCancelRequestPageReqDto) (model.PagingModel[dto.ProxyBidCancelRequestDto], error)
	UpdateStatusProxyBidCancelRequest(req dto.ProxyBidCancelRequestUpdateReqDto, action string) error
}

func NewProxyBidCancelRequestService(repo proxyBidCancelRequestRepository.ProxyBidCancelRequestRepository, proxyBidRepo proxyBidRepository.ProxyBidRepository,
) ProxyBidCancelRequestService {
	return &proxyBidCancelRequestService{Repo: repo, ProxyBidRepo: proxyBidRepo}

}
