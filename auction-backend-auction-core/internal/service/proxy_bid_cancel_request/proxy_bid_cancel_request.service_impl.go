package service

import (
	"auction-core-service/internal/model/dto"
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"fmt"
	"net/http"

	"gorm.io/gorm"
)

func (s *proxyBidCancelRequestService) SearchProxyBidCancelRequestFilter(req dto.ProxyBidCancelRequestPageReqDto) (model.PagingModel[dto.ProxyBidCancelRequestDto], error) {
	resp := model.PagingModel[dto.ProxyBidCancelRequestDto]{}

	result, err := s.Repo.FindProxyBidCancelRequestWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountProxyBidCancelRequestWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Set paging model
	resp = *util.MapPaginationResult(result, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit)

	return resp, nil
}

func validateApproveRequireField(req dto.ProxyBidCancelRequestUpdateReqDto) error {
	if req.CancelReasonId == nil || util.Val(req.CancelReasonId) == 0 {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("cancel reason is required"))
	}
	return nil
}

func (s *proxyBidCancelRequestService) UpdateStatusProxyBidCancelRequest(req dto.ProxyBidCancelRequestUpdateReqDto, action string) error {
	var actionStatus string
	var proxyStatusId *int

	switch action {
	case constant.ActionApprove:
		actionStatus = constant.ActionStatusApprovedEn
		proxyStatusId = util.Ptr(constant.ProxyStatusCancelId)
	case constant.ActionReject:
		actionStatus = constant.ActionStatusRejectedEn
		proxyStatusId = util.Ptr(constant.ProxyStatusActiveId)
	default:
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("invalid action"))
	}
	//NOTE - Check Required fields if approve
	if actionStatus == constant.ActionStatusApprovedEn {
		if err := validateApproveRequireField(req); err != nil {
			return errs.NewError(http.StatusBadRequest, err)
		}
	}
	//NOTE - Get Proxy bid id use for update proxy bid cancel reason
	proxyBidCancelRequest, err := s.Repo.FindById(req.Id)
	if err != nil {
		if errs.IsGormNotFound(err) {
			return errs.NewError(http.StatusNotFound, err)
		}
		return errs.NewError(http.StatusInternalServerError, err)
	}

	now := util.Now()
	fieldsToUpdate := map[string]interface{}{
		"action_status": actionStatus,
		"updated_by":    req.ActionBy,
		"updated_date":  &now,
		"approve_by":    req.ActionBy,
		"approve_date":  &now,
	}

	fieldsToUpdateProxyBid := map[string]interface{}{
		"proxy_status_id": proxyStatusId,
		"updated_by":      req.ActionBy,
		"updated_date":    &now,
	}

	if actionStatus == constant.ActionStatusApprovedEn {
		fieldsToUpdateProxyBid["cancel_type"] = constant.ProxyBidCancelTypeAdmin
		fieldsToUpdateProxyBid["cancel_reason_id"] = req.CancelReasonId
		fieldsToUpdateProxyBid["cancel_by"] = req.ActionBy
		fieldsToUpdateProxyBid["cancel_date"] = &now
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {

		affectedRows, err := s.Repo.UpdateProxyBidCancelRequest(tx, req.Id, fieldsToUpdate)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if affectedRows == 0 {
			return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
		}

		affectedRowsProxyBid, err := s.ProxyBidRepo.UpdateProxyBid(tx, util.Val(proxyBidCancelRequest.ProxyBidId), fieldsToUpdateProxyBid)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if affectedRowsProxyBid == 0 {
			return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("proxy bid id %d not found", util.Val(proxyBidCancelRequest.ProxyBidId)), "")
		}

		return nil

	})
	if errTx != nil {
		return errTx
	}

	return nil

}
