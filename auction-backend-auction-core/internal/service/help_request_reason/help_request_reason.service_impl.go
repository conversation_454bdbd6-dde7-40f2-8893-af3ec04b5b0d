package service

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	helpRequestReasonRepository "auction-core-service/internal/repository/help_request_reason"
	helpRequestReasonRoleRepository "auction-core-service/internal/repository/help_request_reason_role"
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"net/http"

	"github.com/gofiber/fiber/v2/log"
	"gorm.io/gorm"
)

func (s *helpRequestReasonService) HelpRequestReasonFilter(req dto.HelpRequestReasonPageReqDto) (dto.HelpRequestReasonPageRespDto[dto.HelpRequestReasonDto], error) {

	resp := dto.HelpRequestReasonPageRespDto[dto.HelpRequestReasonDto]{}

	result, err := s.Repo.HelpRequestReasonFilter(req)
	if err != nil {
		log.Error(err)
		return resp, err
	}

	count, err := s.Repo.CountHelpRequestReasonFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	var ids []int
	for _, n := range result {
		ids = append(ids, n.BaseEntity.Id)
	}

	roleDbs, err := s.HelpRequestReasonRoleRepo.GetAllByInHelpRequestReasonId(ids)
	if err != nil {
		log.Error(err)
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	roleDbsMap := make(map[int][]entity.HelpRequestReasonRole)
	for _, b := range roleDbs {
		roleDbsMap[util.Val(b.HelpRequestReasonId)] = append(roleDbsMap[util.Val(b.HelpRequestReasonId)], b)
	}

	mapResult := make([]dto.HelpRequestReasonDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.HelpRequestReasonDto](v)

		var roles []*string

		for _, role := range roleDbsMap[v.Id] {
			if role.RoleForJoin != nil {
				roles = append(roles, &role.RoleForJoin.RoleName)
			}
		}

		if len(roles) != 0 {
			mapResult[i].Roles = roles
		}
	}

	resp = dto.HelpRequestReasonPageRespDto[dto.HelpRequestReasonDto]{
		PagingModel: *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
	}

	return resp, nil
}

func (s *helpRequestReasonService) GetHelpRequestReasonById(id int) (*dto.HelpRequestReasonRespDto, error) {
	var resp dto.HelpRequestReasonRespDto

	helpRequestReasonDb, err := s.Repo.GetById(id)
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	roleDbs, err := s.HelpRequestReasonRoleRepo.GetAllByHelpRequestReasonId(id)
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	var roleIds []*int

	for _, role := range roleDbs {
		if role.RoleForJoin != nil {
			roleIds = append(roleIds, &role.RoleForJoin.Id)
		}
	}

	resp.Id = helpRequestReasonDb.Id
	resp.Reason = helpRequestReasonDb.Reason
	resp.IsActive = helpRequestReasonDb.IsActive

	if len(roleIds) != 0 {
		resp.RoleIds = roleIds
	}

	return &resp, nil

}

func (s *helpRequestReasonService) CreateHelpRequestReason(req dto.HelpRequestReasonInternalReqDto) error {
	err := validateTab(req)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	now := util.Now()

	helpRequestReason := entity.HelpRequestReason{
		BaseEntity: &model.BaseEntity{
			CreatedDate: now,
			CreatedBy:   req.ActionBy,
			UpdatedDate: &now,
			UpdatedBy:   req.ActionBy,
		},
		Reason:   req.Reason,
		IsActive: req.IsActive,
	}

	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		helpRequestReasonRepo := helpRequestReasonRepository.NewHelpRequestReasonRepository(tx)
		helpRequestReasonRoleRepo := helpRequestReasonRoleRepository.NewHelpRequestReasonRoleRepository(tx)

		err := helpRequestReasonRepo.Insert(helpRequestReason)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		helpRequestReasonRoleList := []entity.HelpRequestReasonRole{}

		for _, roleId := range req.RoleIds {
			helpRequestReasonRole := entity.HelpRequestReasonRole{
				HelpRequestReasonId: &helpRequestReason.Id,
				RoleId:              roleId,
				BaseEntity: &model.BaseEntity{
					CreatedDate: now,
					CreatedBy:   req.ActionBy,
					UpdatedDate: &now,
					UpdatedBy:   req.ActionBy,
				},
			}

			helpRequestReasonRoleList = append(helpRequestReasonRoleList, helpRequestReasonRole)
		}

		if len(helpRequestReasonRoleList) != 0 {
			err = helpRequestReasonRoleRepo.InsertList(helpRequestReasonRoleList)
			if err != nil {
				log.Error(err)
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		return nil
	})

}

func (s *helpRequestReasonService) UpdateHelpRequestReason(req dto.HelpRequestReasonInternalReqDto) error {

	helpRequestReason, err := s.Repo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	err = validateTab(req)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	now := util.Now()

	helpRequestReason.UpdatedDate = &now
	helpRequestReason.UpdatedBy = req.ActionBy
	helpRequestReason.Reason = req.Reason
	helpRequestReason.IsActive = req.IsActive

	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		helpRequestReasonRepo := helpRequestReasonRepository.NewHelpRequestReasonRepository(tx)
		helpRequestReasonRoleRepo := helpRequestReasonRoleRepository.NewHelpRequestReasonRoleRepository(tx)

		err := helpRequestReasonRepo.UpdateAllFields(helpRequestReason)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		err = helpRequestReasonRoleRepo.DeleteByHelpRequestReasonId(helpRequestReason.Id)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		helpRequestReasonRoleList := []entity.HelpRequestReasonRole{}

		for _, roleId := range req.RoleIds {
			helpRequestReasonRole := entity.HelpRequestReasonRole{
				HelpRequestReasonId: &helpRequestReason.Id,
				RoleId:              roleId,
				BaseEntity: &model.BaseEntity{
					CreatedDate: now,
					CreatedBy:   req.ActionBy,
					UpdatedDate: &now,
					UpdatedBy:   req.ActionBy,
				},
			}

			helpRequestReasonRoleList = append(helpRequestReasonRoleList, helpRequestReasonRole)
		}

		if len(helpRequestReasonRoleList) != 0 {
			err = helpRequestReasonRoleRepo.InsertList(helpRequestReasonRoleList)
			if err != nil {
				log.Error(err)
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		return nil
	})

}

func (s *helpRequestReasonService) UpdateHelpRequestReasonStatus(req dto.HelpRequestReasonStatusReqDto) error {
	_, err := s.Repo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	err = s.Repo.UpdateStatus(req.Id, fieldsToUpdate)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return nil
}

func (s *helpRequestReasonService) DeleteHelpRequestReason(id int, actionBy *int) error {
	_, err := s.Repo.GetById(id)
	if err != nil {
		log.Error(err)
		return err
	}

	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		helpRequestReasonRepo := helpRequestReasonRepository.NewHelpRequestReasonRepository(tx)
		helpRequestReasonRoleRepo := helpRequestReasonRoleRepository.NewHelpRequestReasonRoleRepository(tx)

		err := helpRequestReasonRepo.Delete(id)
		if err != nil {
			log.Error(err)
			return err
		}

		err = helpRequestReasonRoleRepo.DeleteByHelpRequestReasonId(id)
		if err != nil {
			log.Error(err)
			return err
		}

		return nil
	})
}

func validateTab(req dto.HelpRequestReasonInternalReqDto) error {
	if util.Val(req.Reason) == "" {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "reason is required", "")
	}
	if len(req.RoleIds) == 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "roleIds is required", "")
	} else {
		for _, roleId := range req.RoleIds {
			if util.Val(roleId) == 0 {
				return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "roleId is required", "")
			}
		}
	}

	return nil
}
