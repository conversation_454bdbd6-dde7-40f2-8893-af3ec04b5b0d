package service

import (
	"auction-core-service/internal/model/dto"
	repository "auction-core-service/internal/repository/help_request_reason"
	helpRequsetRolerepository "auction-core-service/internal/repository/help_request_reason_role"
)

type helpRequestReasonService struct {
	Repo                      repository.HelpRequestReasonRepository
	HelpRequestReasonRoleRepo helpRequsetRolerepository.HelpRequestReasonRoleRepository
}

type HelpRequestReasonService interface {
	HelpRequestReasonFilter(req dto.HelpRequestReasonPageReqDto) (dto.HelpRequestReasonPageRespDto[dto.HelpRequestReasonDto], error)
	GetHelpRequestReasonById(id int) (*dto.HelpRequestReasonRespDto, error)
	CreateHelpRequestReason(req dto.HelpRequestReasonInternalReqDto) error
	UpdateHelpRequestReason(req dto.HelpRequestReasonInternalReqDto) error
	UpdateHelpRequestReasonStatus(req dto.HelpRequestReasonStatusReqDto) error
	DeleteHelpRequestReason(id int, actionBy *int) error
}

func NewHelpRequestReasonService(repo repository.HelpRequestReasonRepository,
	helpRequestReasonRoleRepo helpRequsetRolerepository.HelpRequestReasonRoleRepository) HelpRequestReasonService {
	return &helpRequestReasonService{Repo: repo,
		HelpRequestReasonRoleRepo: helpRequestReasonRoleRepo}
}
