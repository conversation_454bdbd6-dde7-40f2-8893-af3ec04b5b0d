package service

import (
	"auction-core-service/internal/model/dto"
	helpRequestRepository "auction-core-service/internal/repository/help_request"
	"backend-common-lib/model"
)

type helpRequestService struct {
	Repo helpRequestRepository.HelpRequestRepository
}

type HelpRequestService interface {
	SearchHelpRequestFilter(req dto.HelpRequestPageReqDto) (model.PagingModel[dto.HelpRequestDto], error)
	GetHelpRequestById(id int) (dto.HelpRequestDto, error)
	UpdateHelpRequestStatus(req dto.HelpRequestUpdateReqDto) error
}

func NewHelpRequestService(repo helpRequestRepository.HelpRequestRepository) HelpRequestService {
	return &helpRequestService{Repo: repo}

}
