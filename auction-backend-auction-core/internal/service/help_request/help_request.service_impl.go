package service

import (
	constant_auction "auction-core-service/constant"
	"auction-core-service/internal/model/dto"
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"errors"
	"fmt"
	"net/http"

	"gorm.io/gorm"
)

func (s *helpRequestService) SearchHelpRequestFilter(req dto.HelpRequestPageReqDto) (model.PagingModel[dto.HelpRequestDto], error) {
	resp := model.PagingModel[dto.HelpRequestDto]{}

	result, err := s.Repo.FindHelpRequestWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountHelpRequestWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.HelpRequestDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.HelpRequestDto](v)
	}

	//NOTE - Set paging model
	resp = *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit)

	return resp, nil
}

func (s *helpRequestService) GetHelpRequestById(id int) (dto.HelpRequestDto, error) {
	resp := dto.HelpRequestDto{}
	result, err := s.Repo.FindHelpRequestById(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return resp, errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", id), "")
		}
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	resp = util.MapToWithCreatedByAndUpdatedBy[dto.HelpRequestDto](result)
	return resp, nil
}

func validateRequireField(req dto.HelpRequestUpdateReqDto) error {
	if (util.Val(req.StatusHelpRequestId) == constant_auction.HelpRequestStatusClosedId) && util.Val(req.Solution) == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("solution is required"))
	}
	return nil
}

func (s *helpRequestService) UpdateHelpRequestStatus(req dto.HelpRequestUpdateReqDto) error {
	//NOTE - Check Required fields if close
	if err := validateRequireField(req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	_, err := s.Repo.FindHelpRequestById(req.Id)
	if err != nil {
		if errs.IsGormNotFound(err) {
			return errs.NewError(http.StatusNotFound, err)
		}
		return errs.NewError(http.StatusInternalServerError, err)
	}

	now := util.Now()
	fieldsToUpdate := map[string]interface{}{
		"status_help_request_id": req.StatusHelpRequestId,
		"updated_by":             req.ActionBy,
		"updated_date":           &now,
	}

	if util.Val(req.StatusHelpRequestId) == constant_auction.HelpRequestStatusClosedId {
		fieldsToUpdate["solution"] = req.Solution
		fieldsToUpdate["solve_issue_date"] = &now
		fieldsToUpdate["solve_issue_by"] = req.ActionBy
	}

	affected, err := s.Repo.UpdatesHelpRequestFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affected == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}
