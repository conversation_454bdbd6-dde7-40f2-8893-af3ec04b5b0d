package service

import (
	"auction-core-service/internal/model/dto"
	auctionRepository "auction-core-service/internal/repository/auction"
	lotRepository "auction-core-service/internal/repository/lot"
	lotAssetTypeRepository "auction-core-service/internal/repository/lot_asset_type"
	lotSouRepository "auction-core-service/internal/repository/lot_sou"
	lotSouLotLineRepository "auction-core-service/internal/repository/lot_sou_lot_line"
	proxyBidRepository "auction-core-service/internal/repository/proxy_bid"
)

type proxyBidService struct {
	Repo              proxyBidRepository.ProxyBidRepository
	LotRepo           lotRepository.LotRepository
	LotAssetTypeRepo  lotAssetTypeRepository.LotAssetTypeRepository
	LotSouRepo        lotSouRepository.LotSouRepository
	LotSouLotLineRepo lotSouLotLineRepository.LotSouLotLineRepository
	AuctionRepo       auctionRepository.AuctionRepository
}

type ProxyBidService interface {
	SearchLotProxyBidFilter(req dto.LotProxyBidSearchReqDto) ([]dto.LotProxyBidSearchRespInternalServiceDto, error)
	SearchLotLineProxyBidFilter(req dto.LotLineProxyBidSearchReqDto) (dto.LotLineProxyBidPageRespDto[dto.LotLineProxyBidSearchRespDto], error)
	GetProxyBidListBySouLotLineId(souLotLineId int) ([]dto.ProxyBidListRespDto, error)
}

func NewProxyBidService(repo proxyBidRepository.ProxyBidRepository,
	lotRepo lotRepository.LotRepository,
	lotAssetTypeRepo lotAssetTypeRepository.LotAssetTypeRepository,
	lotSouRepo lotSouRepository.LotSouRepository,
	lotSouLotLineRepo lotSouLotLineRepository.LotSouLotLineRepository,
	auctionRepo auctionRepository.AuctionRepository,
) ProxyBidService {
	return &proxyBidService{Repo: repo,
		LotRepo:           lotRepo,
		LotAssetTypeRepo:  lotAssetTypeRepo,
		LotSouRepo:        lotSouRepo,
		LotSouLotLineRepo: lotSouLotLineRepo,
		AuctionRepo:       auctionRepo}
}
