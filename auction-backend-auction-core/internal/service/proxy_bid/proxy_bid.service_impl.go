package service

import (
	"auction-core-service/internal/constant"
	"auction-core-service/internal/model/dto"
	constantCommon "backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/util"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2/log"
)

func (s *proxyBidService) SearchLotProxyBidFilter(req dto.LotProxyBidSearchReqDto) ([]dto.LotProxyBidSearchRespInternalServiceDto, error) {
	layoutDMY := constantCommon.DateFormatDMY
	layoutTime := constantCommon.TimeFormatShort

	lotDbs, err := s.LotRepo.FindLotProxyBidWithFilter(req)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	var responseDtos []dto.LotProxyBidSearchRespInternalServiceDto
	if lotDbs != nil && len(lotDbs) > 0 {
		for _, lot := range lotDbs {
			proxyBidAmount := 0
			souLotLineDbs, err := s.LotSouLotLineRepo.GetAllByLotId(lot.Id)
			if err != nil {
				log.Error(err)
				return nil, err
			}

			proxyBidDbs, err := s.Repo.GetAllByLotId(lot.Id)
			if err != nil {
				log.Error(err)
				return nil, err
			}

			proxyBidAmount += len(proxyBidDbs)

			lotAssetTypeDbs, err := s.LotAssetTypeRepo.GetAllByLotId(lot.Id)
			if err != nil {
				log.Error(err)
				return nil, err
			}

			isAssetTypeIdFilter := false
			if req.AssetTypeId != nil {
				for _, lotAssetTypeDb := range lotAssetTypeDbs {
					if lotAssetTypeDb.AssetTypeId == *req.AssetTypeId {
						isAssetTypeIdFilter = true
					}
				}
			} else {
				isAssetTypeIdFilter = true
			}

			if isAssetTypeIdFilter {
				var proxyStartDate *string
				var proxyStartTime *string

				if lot.ProxyStartDate != nil {
					temp := lot.ProxyStartDate.Format(layoutDMY)
					proxyStartDate = &temp
				}

				if lot.ProxyStartTime != nil {
					temp := lot.ProxyStartTime.Format(layoutTime)
					proxyStartTime = &temp
				}

				auction, err := s.AuctionRepo.GetById(lot.AuctionId)
				if err != nil {
					log.Error(err)
					return nil, err
				}

				auctionDate := lot.AuctionDate.Format(layoutDMY)
				auctionTime := lot.AuctionTime.Format(layoutTime)
				floor := fmt.Sprintf("ลาน %s", *lot.Floor.Floor)

				responseDtos = append(responseDtos, dto.LotProxyBidSearchRespInternalServiceDto{
					LotProxyBidSearchRespDto: dto.LotProxyBidSearchRespDto{Id: lot.Id,
						BranchId:       &lot.BranchId,
						BranchNameTh:   lot.Branch.DescriptionTh,
						BranchNameEn:   lot.Branch.DescriptionEn,
						AuctionDate:    &auctionDate,
						AuctionTime:    &auctionTime,
						FloorId:        &lot.FloorId,
						Floor:          &floor,
						Name:           &lot.Name,
						EventName:      auction.Event.DescriptionTh,
						ProxyStartDate: proxyStartDate,
						ProxyStartTime: proxyStartTime,
						IsActive:       *lot.IsActive,
						FloorStatus:    calFloorStatus(lot.FloorStatus, lot.ShowStartDate, lot.ShowStartTime, lot.ShowEndDate, lot.ShowEndTime),
						Amount:         &proxyBidAmount,
					},
					SouLotLines:     souLotLineDbs,
					LotAssetTypeDbs: lotAssetTypeDbs,
				})
			}
		}
	}

	return responseDtos, nil
}

func (s *proxyBidService) SearchLotLineProxyBidFilter(req dto.LotLineProxyBidSearchReqDto) (dto.LotLineProxyBidPageRespDto[dto.LotLineProxyBidSearchRespDto], error) {
	layoutDMY := constantCommon.DateFormatDMY
	layoutTime := constantCommon.TimeFormatShort

	lotLineDbs, err := s.LotSouLotLineRepo.FindLotLineProxyBidWithFilter(req)
	if err != nil {
		log.Error(err)
		return dto.LotLineProxyBidPageRespDto[dto.LotLineProxyBidSearchRespDto]{}, err
	}

	count, err := s.LotSouLotLineRepo.CountLotLineProxyBidWithFilter(req)
	if err != nil {
		return dto.LotLineProxyBidPageRespDto[dto.LotLineProxyBidSearchRespDto]{}, errs.NewError(http.StatusInternalServerError, err)
	}

	lotDb, err := s.LotRepo.GetById(req.LotId)
	if err != nil {
		log.Error(err)
		return dto.LotLineProxyBidPageRespDto[dto.LotLineProxyBidSearchRespDto]{}, err
	}

	mapResult := make([]dto.LotLineProxyBidSearchRespDto, len(lotLineDbs))
	for i, v := range lotLineDbs {
		mapResult[i] = *util.MapToPtr[dto.LotLineProxyBidSearchRespDto](v)
		if util.Val(v.AuctCode) != "" && util.Val(v.AuctionNo) != 0 {
			order := util.Val(v.AuctCode) + strconv.Itoa(util.Val(v.AuctionNo))
			mapResult[i].Order = &order
		}
		proxyBidDbs, err := s.Repo.GetAllBySouLotLineId(v.Id)
		if err != nil {
			log.Error(err)
			return dto.LotLineProxyBidPageRespDto[dto.LotLineProxyBidSearchRespDto]{}, err
		}
		for _, proxyBidDb := range proxyBidDbs {
			if proxyBidDb.CancelType != nil && *proxyBidDb.CancelType != constant.ProxyBidCancelAdminEn {
				mapResult[i].IsCancelledNoti = true
			}
		}
		auctionDate := v.AuctionDate.Format(layoutDMY)
		auctionTime := v.AuctionTime.Format(layoutTime)
		mapResult[i].AuctionDateString = auctionDate
		mapResult[i].AuctionTimeString = auctionTime
		amount := len(proxyBidDbs)
		mapResult[i].Amount = &amount
		if lotDb.FloorStatus == 2 {
			mapResult[i].IsLive = true
		} else {
			mapResult[i].IsLive = false
		}
	}

	responseDtos := dto.LotLineProxyBidPageRespDto[dto.LotLineProxyBidSearchRespDto]{
		PagingModel: *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
	}

	return responseDtos, nil
}

func (s *proxyBidService) GetProxyBidListBySouLotLineId(souLotLineId int) ([]dto.ProxyBidListRespDto, error) {

	proxyBidDbs, err := s.Repo.GetAllBySouLotLineId(souLotLineId)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	mapResult := make([]dto.ProxyBidListRespDto, len(proxyBidDbs))
	for i, v := range proxyBidDbs {

		var parts []string

		if v.Buyer.FirstName != nil && *v.Buyer.FirstName != "" {
			parts = append(parts, *v.Buyer.FirstName)
		}
		if v.Buyer.MiddleName != nil && *v.Buyer.MiddleName != "" {
			parts = append(parts, *v.Buyer.MiddleName)
		}
		if v.Buyer.LastName != nil && *v.Buyer.LastName != "" {
			parts = append(parts, *v.Buyer.LastName)
		}

		fullname := strings.Join(parts, " ")

		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.ProxyBidListRespDto](v)
		mapResult[i].CustomerGroupId = v.Buyer.CustomerGroupId
		mapResult[i].BidderID = &v.Buyer.BidderId
		mapResult[i].BidderName = &fullname
		mapResult[i].PhoneNumber = &v.Buyer.PhoneNumber
	}

	return mapResult, nil
}

func calFloorStatus(floorStatus int, showStartDate time.Time, showStartTime *time.Time, showEndDate time.Time, showEndTime *time.Time) *int {
	now := time.Now()

	showStartDateTime := time.Date(showStartDate.Year(), showStartDate.Month(), showStartDate.Day(), showStartTime.Hour(), showStartTime.Minute(), showStartTime.Second(), 0, showStartDate.Location())
	showEndDateTime := time.Date(showEndDate.Year(), showEndDate.Month(), showEndDate.Day(), showEndTime.Hour(), showEndTime.Minute(), showEndTime.Second(), 0, showEndDate.Location())

	if now.After(showStartDateTime) && floorStatus != 4 && floorStatus != 5 {
		floorStatus = 2
	}
	if now.After(showEndDateTime) {
		floorStatus = 3
	}

	return &floorStatus
}
