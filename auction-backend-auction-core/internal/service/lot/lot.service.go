package service

import (
	"auction-core-service/internal/global"
	"auction-core-service/internal/model/dto"
	auctionRepository "auction-core-service/internal/repository/auction"
	commonAuctionCoreRepository "auction-core-service/internal/repository/common_auction_core"
	lotRepository "auction-core-service/internal/repository/lot"
	lotActiveOptionRepository "auction-core-service/internal/repository/lot_active_option"
	lotAssetTypeRepository "auction-core-service/internal/repository/lot_asset_type"
	lotSouRepository "auction-core-service/internal/repository/lot_sou"
	lotSouLotLineRepository "auction-core-service/internal/repository/lot_sou_lot_line"
	lotStreamingRepository "auction-core-service/internal/repository/lot_streaming"
	"backend-common-lib/model"
	"mime/multipart"
)

type lotService struct {
	Repo                  lotRepository.LotRepository
	LotActiveOptionRepo   lotActiveOptionRepository.LotActiveOptionRepository
	LotAssetTypeRepo      lotAssetTypeRepository.LotAssetTypeRepository
	LotSouRepo            lotSouRepository.LotSouRepository
	LotSouLotLineRepo     lotSouLotLineRepository.LotSouLotLineRepository
	LotStreamingRepo      lotStreamingRepository.LotStreamingRepository
	AuctionRepo           auctionRepository.AuctionRepository
	CommonAuctionCoreRepo commonAuctionCoreRepository.CommonAuctionCoreRepository
}

type LotService interface {
	GetLotByID(id int) (*dto.LotDto, error)
	CreateLot(req dto.LotDto) error
	UpdateLot(req dto.LotDto) error
	GetLotSouFromErp(req dto.LotSouFromErpReqDto, erpConfig global.ErpConfig) ([]dto.LotSouDto, error)
	GetSyncLotSouLotLineFromErp(lotId int, user *int, erpConfig global.ErpConfig) error
	GetSyncLotSouLotLineByProductFromErp(lotLineId int, user *int, erpConfig global.ErpConfig) error
	DeleteLot(id int, actionBy *int) error
	SearchLots(req dto.LotSearchReqDto) ([]dto.LotSearchRespInternalServiceDto, error)
	SearchLotLineFilter(req dto.SouLotLinePageReqDto) (dto.SouLotLinePageRespDto[dto.SouLotLineDto], error)
	LotLineExportExcel(req dto.SouLotLinePageReqDto) (*dto.SouLotLineExportDto, error)
	LotLineUploadExcel(file multipart.File, req model.BaseDtoActionBy) error
}

func NewLotService(repo lotRepository.LotRepository,
	lotActiveOptionRepo lotActiveOptionRepository.LotActiveOptionRepository,
	lotAssetTypeRepo lotAssetTypeRepository.LotAssetTypeRepository,
	lotSouRepo lotSouRepository.LotSouRepository,
	lotSouLotLineRepo lotSouLotLineRepository.LotSouLotLineRepository,
	lotStreamingRepo lotStreamingRepository.LotStreamingRepository,
	auctionRepo auctionRepository.AuctionRepository,
	commonAuctionCoreRepository commonAuctionCoreRepository.CommonAuctionCoreRepository) LotService {
	return &lotService{Repo: repo,
		LotActiveOptionRepo:   lotActiveOptionRepo,
		LotAssetTypeRepo:      lotAssetTypeRepo,
		LotSouRepo:            lotSouRepo,
		LotSouLotLineRepo:     lotSouLotLineRepo,
		LotStreamingRepo:      lotStreamingRepo,
		AuctionRepo:           auctionRepo,
		CommonAuctionCoreRepo: commonAuctionCoreRepository}
}
