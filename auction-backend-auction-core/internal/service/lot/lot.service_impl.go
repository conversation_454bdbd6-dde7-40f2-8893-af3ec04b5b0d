package service

import (
	"auction-core-service/internal/global"
	"auction-core-service/internal/integration/erp"
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	lotRepository "auction-core-service/internal/repository/lot"
	lotActiveOptionRepository "auction-core-service/internal/repository/lot_active_option"
	lotAssetTypeRepository "auction-core-service/internal/repository/lot_asset_type"
	lotSouRepository "auction-core-service/internal/repository/lot_sou"
	lotSouLotLineRepository "auction-core-service/internal/repository/lot_sou_lot_line"
	lotStreamingRepository "auction-core-service/internal/repository/lot_streaming"
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"fmt"
	"mime/multipart"
	"net/http"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/log"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

func (s *lotService) GetLotByID(id int) (*dto.LotDto, error) {
	lotDb, err := s.Repo.GetById(id)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	lotAssetTypeDbs, err := s.LotAssetTypeRepo.GetAllByLotId(id)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	lotActiveOptionDbs, err := s.LotActiveOptionRepo.GetAllByLotId(id)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	lotStreamingDbs, err := s.LotStreamingRepo.GetAllByLotId(id)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	lotSouDbs, err := s.LotSouRepo.GetAllByLotId(id)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	auctionDb, err := s.AuctionRepo.GetById(lotDb.AuctionId)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	//cannot use reflect assign manually need to fix
	layout := constant.DateFormatDMY
	layoutTime := constant.TimeFormatShort

	tempLotAssetTypeIdList := []*int{}
	tempLotActiveOptionDtoList := []*dto.LotActiveOptionDto{}
	tempLotStreamingDtoList := []*dto.LotStreamingDto{}
	tempLotSouDtoList := []*dto.LotSouDto{}

	if lotAssetTypeDbs != nil {
		for _, m := range lotAssetTypeDbs {
			tempLotAssetTypeIdList = append(tempLotAssetTypeIdList, &m.AssetTypeId)
		}
	}

	if lotActiveOptionDbs != nil {
		for _, m := range lotActiveOptionDbs {
			tempLotActiveOptionDtoList = append(tempLotActiveOptionDtoList, &dto.LotActiveOptionDto{
				Id:                 m.Id,
				LotId:              &m.LotId,
				ConfigFeatureLotId: &m.ConfigFeatureLotId,
			})
		}
	}

	if lotStreamingDbs != nil {
		for _, m := range lotStreamingDbs {
			tempLotStreamingDtoList = append(tempLotStreamingDtoList, &dto.LotStreamingDto{
				Id:            m.Id,
				LotId:         &m.LotId,
				Order:         &m.Order,
				StreamingName: &m.StreamingName,
				StreamingLink: &m.StreamingLink,
			})
		}
	}

	var latestSyncDate *time.Time
	if lotSouDbs != nil {
		for i, m := range lotSouDbs {

			auctionDate := m.AuctionDate.Format(layout)
			auctionTime := m.AuctionTime.Format(layoutTime)
			dueDate := m.DueDate.Format(layout)

			tempLotSouDtoList = append(tempLotSouDtoList, &dto.LotSouDto{
				Id:              m.Id,
				LotId:           &m.LotId,
				CompanyCode:     &m.CompanyCode,
				DocumentNo:      &m.DocumentNo,
				AuctionDate:     &auctionDate,
				AuctionTime:     &auctionTime,
				DueDate:         &dueDate,
				NoOfNewCar:      &m.NoOfNewCar,
				BranchCode:      &m.BranchCode,
				BranchNameTh:    &m.BranchNameTh,
				BranchNameEn:    &m.BranchNameEn,
				LocationCode:    &m.LocationCode,
				Floor:           &m.Floor,
				AssetTypeCode:   &m.AssetTypeCode,
				AssetTypeNameTh: &m.AssetTypeNameTh,
				AssetTypeNameEn: &m.AssetTypeNameEn,
				NoOfOldCar:      &m.NoOfOldCar,
				EventCode:       m.EventCode,
				Status:          &m.Status,
				AuctionStatus:   &m.AuctionStatus,
				RemarkTh:        &m.RemarkTh,
				RemarkEn:        &m.RemarkEn,
				RemarkExtra:     &m.RemarkExtra,
				ActionType:      m.ActionType,
			})

			if i == 0 {
				latestSyncDate = m.LatestSyncDate
			}
		}

	}

	auctionTime := lotDb.AuctionTime.Format(layoutTime)
	showStartTime := lotDb.ShowStartTime.Format(layoutTime)
	showEndTime := lotDb.ShowEndTime.Format(layoutTime)
	auctionDate := lotDb.AuctionDate.Format(layout)
	showStartDate := lotDb.ShowStartDate.Format(layout)
	showEndDate := lotDb.ShowEndDate.Format(layout)

	auctionTypeId, _ := strconv.Atoi(util.Val(auctionDb.AuctionTypeId))

	lotDto := dto.LotDto{
		BaseDtoActionBy:  model.BaseDtoActionBy{Id: lotDb.Id},
		BranchId:         &lotDb.BranchId,
		AuctionDate:      &auctionDate,
		AuctionTime:      &auctionTime,
		FloorId:          &lotDb.FloorId,
		AssetTypeList:    tempLotAssetTypeIdList,
		AuctionTypeId:    &auctionTypeId,
		AuctionId:        &lotDb.AuctionId,
		Name:             &lotDb.Name,
		Description:      &lotDb.Description,
		IsActive:         *lotDb.IsActive,
		FloorStatus:      &lotDb.FloorStatus,
		AuctionChannel:   &lotDb.AuctionChannel,
		ActiveOptionList: tempLotActiveOptionDtoList,
		ShowStartDate:    &showStartDate,
		ShowStartTime:    &showStartTime,
		ShowEndDate:      &showEndDate,
		ShowEndTime:      &showEndTime,
		StreamingList:    tempLotStreamingDtoList,
		SouList:          tempLotSouDtoList,
		LatestSyncDate:   latestSyncDate,
	}

	if lotDb.ProxyStartDate != nil {
		proxyStartDate := lotDb.ProxyStartDate.Format(layout)
		lotDto.ProxyStartDate = &proxyStartDate
	}
	if lotDb.ProxyStartTime != nil {
		proxyStartTime := lotDb.ProxyStartTime.Format(layoutTime)
		lotDto.ProxyStartTime = &proxyStartTime
	}

	return &lotDto, nil
}

func (s *lotService) CreateLot(req dto.LotDto) error {
	now := util.Now()
	req, err := validateData(req)
	if err != nil {
		log.Error(err)
		return err
	}

	lot := prepareCreateLotToDb(req, req.ActionBy, now)

	err = s.createLotToDb(lot)
	if err != nil {
		log.Error(err)
		return err
	}

	return nil
}

func (s *lotService) UpdateLot(req dto.LotDto) error {
	now := util.Now()
	req, err := validateData(req)
	if err != nil {
		log.Error(err)
		return err
	}

	lot, err := s.prepareUpdateLotToDb(req, req.ActionBy, now)
	if err != nil {
		log.Error(err)
		return err
	}

	err = s.updateLotToDb(lot)
	if err != nil {
		log.Error(err)
		return err
	}

	return nil
}

func (s *lotService) GetLotSouFromErp(req dto.LotSouFromErpReqDto, erpConfig global.ErpConfig) ([]dto.LotSouDto, error) {

	if util.Val(req.AuctionDate) == "" {
		return nil, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "auctionDate is required", "")
	}
	if util.Val(req.BranchCode) == "" {
		return nil, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "branchCode is required", "")
	}

	layoutDMY := constant.DateFormatDMY
	layoutYMD := constant.DateFormatYMD
	auctionDate, _ := time.Parse(layoutDMY, util.Val(req.AuctionDate))

	erpList, err := erp.FetchListFromErp[dto.LotSouFromErpRespDto](
		erpConfig.LotSouUrl,
		erpConfig.Token,
		map[string]interface{}{
			"Company_Name": "AUCT",
			"Data": map[string]interface{}{
				"Auction_Date": auctionDate.Format(layoutYMD),
				"Branch_Code":  req.BranchCode,
			}},
		"data",
	)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	lotSous := []dto.LotSouDto{}
	for _, e := range erpList {
		if e.Status != nil && *e.Status == "Released" {

			souDb, _ := s.LotSouRepo.GetByDocumentNo(*e.DocumentNo)

			if souDb.BaseEntity == nil {
				auctionDateTime, _ := time.Parse(layoutYMD, *e.AuctionDate)
				dueDateTime, _ := time.Parse(layoutYMD, *e.DueDate)
				auctionDate := auctionDateTime.Format(layoutDMY)
				dueDate := dueDateTime.Format(layoutDMY)

				lotSous = append(lotSous, dto.LotSouDto{
					CompanyCode:     e.CompanyCode,
					DocumentNo:      e.DocumentNo,
					AuctionDate:     &auctionDate,
					AuctionTime:     e.AuctionTime,
					DueDate:         &dueDate,
					NoOfNewCar:      e.NoOfNewCar,
					BranchCode:      e.BranchCode,
					BranchNameTh:    e.BranchNameTH,
					BranchNameEn:    e.BranchNameEN,
					LocationCode:    e.LocationCode,
					Floor:           e.Floor,
					AssetTypeCode:   e.AssetTypeCode,
					AssetTypeNameTh: e.AssetTypeNameTH,
					AssetTypeNameEn: e.AssetTypeNameEN,
					NoOfOldCar:      e.NoOfOldCar,
					EventCode:       e.EventCode,
					Status:          e.Status,
					AuctionStatus:   e.AuctionStatus,
					RemarkTh:        e.RemarkTH,
					RemarkEn:        e.RemarkEN,
					RemarkExtra:     e.RemarkExtra,
					ActionType:      e.ActionType,
				})
			}
		}
	}
	return lotSous, nil
}

func (s *lotService) GetSyncLotSouLotLineFromErp(lotId int, user *int, erpConfig global.ErpConfig) error {

	souDbs, err := s.LotSouRepo.GetAllByLotId(lotId)
	if err != nil {
		log.Error(err)
		return err
	}

	lotSouLotLineListToInsert := []entity.LotSouLotLine{}
	lotSouLotLineListToUpdate := []entity.LotSouLotLine{}
	compositeKeyDupMap := make(map[string]bool)

	waiting := "Waiting"

	if souDbs != nil {
		for _, sou := range souDbs {
			//section 1 - call external api from erp
			erpList, err := erp.FetchListFromErp[dto.LotSouLotLineFromErpRespDto](
				erpConfig.LotSouLotLineUrl,
				erpConfig.Token,
				map[string]interface{}{
					"Company_Name": "AUCT",
					"Data": map[string]interface{}{
						"Document_No": sou.DocumentNo,
					}},
				"data",
			)
			if err != nil {
				log.Error(err)
				return err
			}

			//section 2 - save to database
			erpDataMap := make(map[string]dto.LotSouLotLineFromErpRespDto)
			lotSouLotLineFromErpMap := make(map[string]entity.LotSouLotLine)
			allKeys := make(map[string]struct{})
			if erpList != nil {
				for _, e := range erpList {

					auctionDate, _ := time.Parse(time.RFC3339, *e.AuctionDate)
					taxDate, _ := time.Parse(time.RFC3339, *e.TaxDate)
					auctionTime, _ := time.Parse(time.RFC3339, *e.AuctionTime)

					compositeKey := util.Val(e.AssetCode)
					erpDataMap[compositeKey] = e
					lotSouLotLineFromErpMap[compositeKey] = entity.LotSouLotLine{
						LotId:                     sou.LotId,
						LotSouId:                  &sou.Id,
						AuctionDate:               &auctionDate,
						DocumentNo:                e.DocumentNo,
						AssetCode:                 e.AssetCode,
						Floor:                     e.Floor,
						AuctCode:                  e.AuctCode,
						AuctionNo:                 e.AuctionNo,
						CityCode:                  e.CityCode,
						AssetGroupCode:            e.AssetGroupCode,
						BrandDescription:          e.BrandDescription,
						ModelDescription:          e.ModelDescription,
						Model:                     e.Model,
						SubModel:                  e.SubModel,
						SubKey:                    e.SubKey,
						EngineSize:                e.EngineSize,
						LicensePlateNo:            e.LicensePlateNo,
						CityDescription:           e.CityDescription,
						RegistrationYear:          e.RegistrationYear,
						YearOfManufacture:         e.YearOfManufacture,
						ColorInCopy:               e.ColorInCopy,
						TaxDate:                   &taxDate,
						VendorGroup:               e.VendorGroup,
						Mile:                      e.Mile,
						SalesPrice:                e.SalesPrice,
						SoldAmount:                e.SoldAmount,
						VatPercentage:             e.VatPercentage,
						AssetYear:                 e.AssetYear,
						BranchDescription:         e.BranchDescription,
						ContractNo:                e.ContractNo,
						ChassisNo:                 e.ChassisNo,
						EngineNo:                  e.EngineNo,
						AssetType:                 e.AssetType,
						BranchCode:                e.BranchCode,
						SellerCode:                e.SellerCode,
						SellerName:                e.SellerName,
						FuelTap:                   e.FuelTap,
						Gear:                      e.Gear,
						GearName:                  e.GearName,
						Unque:                     e.Unque,
						CarTypeCon:                e.CarTypeCon,
						PointGrade1Final:          e.PointGrade1Final,
						PointGrade2Con:            e.PointGrade2Con,
						PointGrade3Con:            e.PointGrade3Con,
						FinanceZipFilename:        e.FinanceZipFilename,
						SellerOffer:               e.SellerOffer,
						AuctionTime:               &auctionTime,
						Remark:                    e.Remark,
						SpecialAuction:            e.SpecialAuction,
						PersonalAssessmentAuction: e.PersonalAssessmentAuction,
						PersonalAssessment:        e.PersonalAssessment,
						Tool:                      e.Tool,
						SaleRounds:                e.SaleRounds,
					}
					allKeys[compositeKey] = struct{}{}

				}
			}

			//section 3 - get event master from db
			lotSouLotLineDbs, err := s.LotSouLotLineRepo.GetAllBySouId(sou.Id)
			if err != nil {
				return err
			}

			lotSouLotLineFromDbMap := make(map[string]entity.LotSouLotLine)
			if lotSouLotLineDbs != nil {
				for _, e := range lotSouLotLineDbs {
					// Use DocumentNo+AssetCode as composite key
					compositeKey := util.Val(e.AssetCode)
					lotSouLotLineFromDbMap[compositeKey] = e
					allKeys[compositeKey] = struct{}{}
				}
			}

			//section 4 - compare
			currentDateTime := util.Now()

			for compositeKey := range allKeys {
				erp, erpExists := lotSouLotLineFromErpMap[compositeKey]
				db, dbExists := lotSouLotLineFromDbMap[compositeKey]

				if compositeKeyDupMap[compositeKey] {
					return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "Duplicate AssetCode", "")
				}

				if erpExists && dbExists {
					// มีทั้ง ERP และ DB
					temp := db

					temp.AuctionDate = erp.AuctionDate
					temp.DocumentNo = erp.DocumentNo
					temp.AssetCode = erp.AssetCode
					temp.Floor = erp.Floor
					temp.AuctCode = erp.AuctCode
					temp.AuctionNo = erp.AuctionNo
					temp.CityCode = erp.CityCode
					temp.AssetGroupCode = erp.AssetGroupCode
					temp.BrandDescription = erp.BrandDescription
					temp.ModelDescription = erp.ModelDescription
					temp.Model = erp.Model
					temp.SubModel = erp.SubModel
					temp.SubKey = erp.SubKey
					temp.EngineSize = erp.EngineSize
					temp.LicensePlateNo = erp.LicensePlateNo
					temp.CityDescription = erp.CityDescription
					temp.RegistrationYear = erp.RegistrationYear
					temp.YearOfManufacture = erp.YearOfManufacture
					temp.ColorInCopy = erp.ColorInCopy
					temp.TaxDate = erp.TaxDate
					temp.VendorGroup = erp.VendorGroup
					temp.Mile = erp.Mile
					temp.SalesPrice = erp.SalesPrice
					temp.SoldAmount = erp.SoldAmount
					temp.VatPercentage = erp.VatPercentage
					temp.AssetYear = erp.AssetYear
					temp.BranchDescription = erp.BranchDescription
					temp.ContractNo = erp.ContractNo
					temp.ChassisNo = erp.ChassisNo
					temp.EngineNo = erp.EngineNo
					temp.AssetType = erp.AssetType
					temp.BranchCode = erp.BranchCode
					temp.SellerCode = erp.SellerCode
					temp.SellerName = erp.SellerName
					temp.FuelTap = erp.FuelTap
					temp.Gear = erp.Gear
					temp.GearName = erp.GearName
					temp.Unque = erp.Unque
					temp.CarTypeCon = erp.CarTypeCon
					temp.PointGrade1Final = erp.PointGrade1Final
					temp.PointGrade2Con = erp.PointGrade2Con
					temp.PointGrade3Con = erp.PointGrade3Con
					temp.FinanceZipFilename = erp.FinanceZipFilename
					temp.SellerOffer = erp.SellerOffer
					temp.AuctionTime = erp.AuctionTime
					temp.Remark = erp.Remark
					temp.SpecialAuction = erp.SpecialAuction
					temp.PersonalAssessmentAuction = erp.PersonalAssessmentAuction
					temp.PersonalAssessment = erp.PersonalAssessment
					temp.Tool = erp.Tool
					temp.SaleRounds = erp.SaleRounds

					temp.UpdatedBy = user
					temp.UpdatedDate = &currentDateTime
					temp.IsActive = true
					temp.IsDeletedByErp = false
					temp.LatestSyncDate = &currentDateTime

					compositeKeyDupMap[compositeKey] = true

					lotSouLotLineListToUpdate = append(lotSouLotLineListToUpdate, temp)

				} else if erpExists && !dbExists {
					// มีใน ERP แต่ไม่มีใน DB → insert

					temp := entity.LotSouLotLine{
						BaseEntity: &model.BaseEntity{
							CreatedDate: currentDateTime,
							CreatedBy:   user,
							UpdatedDate: &currentDateTime,
							UpdatedBy:   user,
						},
						LotId:                     sou.LotId,
						LotSouId:                  &sou.Id,
						AuctionDate:               erp.AuctionDate,
						DocumentNo:                erp.DocumentNo,
						AssetCode:                 erp.AssetCode,
						Floor:                     erp.Floor,
						AuctCode:                  erp.AuctCode,
						AuctionNo:                 erp.AuctionNo,
						CityCode:                  erp.CityCode,
						AssetGroupCode:            erp.AssetGroupCode,
						BrandDescription:          erp.BrandDescription,
						ModelDescription:          erp.ModelDescription,
						Model:                     erp.Model,
						SubModel:                  erp.SubModel,
						SubKey:                    erp.SubKey,
						EngineSize:                erp.EngineSize,
						LicensePlateNo:            erp.LicensePlateNo,
						CityDescription:           erp.CityDescription,
						RegistrationYear:          erp.RegistrationYear,
						YearOfManufacture:         erp.YearOfManufacture,
						ColorInCopy:               erp.ColorInCopy,
						TaxDate:                   erp.TaxDate,
						VendorGroup:               erp.VendorGroup,
						Mile:                      erp.Mile,
						SalesPrice:                erp.SalesPrice,
						SoldAmount:                erp.SoldAmount,
						VatPercentage:             erp.VatPercentage,
						AssetYear:                 erp.AssetYear,
						BranchDescription:         erp.BranchDescription,
						ContractNo:                erp.ContractNo,
						ChassisNo:                 erp.ChassisNo,
						EngineNo:                  erp.EngineNo,
						AssetType:                 erp.AssetType,
						BranchCode:                erp.BranchCode,
						SellerCode:                erp.SellerCode,
						SellerName:                erp.SellerName,
						FuelTap:                   erp.FuelTap,
						Gear:                      erp.Gear,
						GearName:                  erp.GearName,
						Unque:                     erp.Unque,
						CarTypeCon:                erp.CarTypeCon,
						PointGrade1Final:          erp.PointGrade1Final,
						PointGrade2Con:            erp.PointGrade2Con,
						PointGrade3Con:            erp.PointGrade3Con,
						FinanceZipFilename:        erp.FinanceZipFilename,
						SellerOffer:               erp.SellerOffer,
						AuctionTime:               erp.AuctionTime,
						Remark:                    erp.Remark,
						SpecialAuction:            erp.SpecialAuction,
						PersonalAssessmentAuction: erp.PersonalAssessmentAuction,
						PersonalAssessment:        erp.PersonalAssessment,
						Tool:                      erp.Tool,
						SaleRounds:                erp.SaleRounds,
						AuctionStatus:             &waiting,
						// ProductStatus:            ,
						IsActive:       true,
						IsDeletedByErp: false,
						LatestSyncDate: &currentDateTime,
					}

					compositeKeyDupMap[compositeKey] = true

					lotSouLotLineListToInsert = append(lotSouLotLineListToInsert, temp)
				} else if !erpExists && dbExists {
					// มีใน DB แต่ไม่มีใน ERP → mark delete
					temp := db
					temp.UpdatedDate = &currentDateTime
					temp.UpdatedBy = user
					temp.IsActive = false
					temp.IsDeletedByErp = true
					temp.LatestSyncDate = &currentDateTime

					lotSouLotLineListToUpdate = append(lotSouLotLineListToUpdate, temp)
				}
			}

		}
	}

	//insert and update to db
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		lotSouLotLineRepo := lotSouLotLineRepository.NewLotSouLotLineRepository(tx)

		if len(lotSouLotLineListToInsert) > 0 {
			if err := lotSouLotLineRepo.InsertList(lotSouLotLineListToInsert); err != nil {
				return err
			}
		}

		if len(lotSouLotLineListToUpdate) > 0 {
			for _, lotSouLotLine := range lotSouLotLineListToUpdate {
				err := lotSouLotLineRepo.Update(lotSouLotLine)
				if err != nil {
					return err
				}
			}
		}

		return nil
	})

	// return nil
}

func (s *lotService) GetSyncLotSouLotLineByProductFromErp(lotLineId int, user *int, erpConfig global.ErpConfig) error {

	souLotLine, err := s.LotSouLotLineRepo.GetById(lotLineId)
	if err != nil {
		log.Error(err)
		return err
	}

	//section 1 - call external api from erp
	erpList, err := erp.FetchListFromErp[dto.LotSouLotLineFromErpRespDto](
		erpConfig.LotSouLotLineUrl,
		erpConfig.Token,
		map[string]interface{}{
			"Company_Name": "AUCT",
			"Data": map[string]interface{}{
				"Document_No": souLotLine.DocumentNo,
				"Asset_Code":  souLotLine.AssetCode,
			}},
		"data",
	)
	if err != nil {
		log.Error(err)
		return err
	}

	//section 2 - save to database
	erpDataMap := make(map[string]dto.LotSouLotLineFromErpRespDto)
	lotSouLotLineFromErpMap := make(map[string]entity.LotSouLotLine)
	allKeys := make(map[string]struct{})
	if erpList != nil {
		for _, e := range erpList {

			auctionDate, _ := time.Parse(time.RFC3339, *e.AuctionDate)
			taxDate, _ := time.Parse(time.RFC3339, *e.TaxDate)
			auctionTime, _ := time.Parse(time.RFC3339, *e.AuctionTime)

			compositeKey := util.Val(e.AssetCode)
			erpDataMap[compositeKey] = e
			lotSouLotLineFromErpMap[compositeKey] = entity.LotSouLotLine{
				LotId:                     souLotLine.LotId,
				LotSouId:                  &souLotLine.Id,
				AuctionDate:               &auctionDate,
				DocumentNo:                e.DocumentNo,
				AssetCode:                 e.AssetCode,
				Floor:                     e.Floor,
				AuctCode:                  e.AuctCode,
				AuctionNo:                 e.AuctionNo,
				CityCode:                  e.CityCode,
				AssetGroupCode:            e.AssetGroupCode,
				BrandDescription:          e.BrandDescription,
				ModelDescription:          e.ModelDescription,
				Model:                     e.Model,
				SubModel:                  e.SubModel,
				SubKey:                    e.SubKey,
				EngineSize:                e.EngineSize,
				LicensePlateNo:            e.LicensePlateNo,
				CityDescription:           e.CityDescription,
				RegistrationYear:          e.RegistrationYear,
				YearOfManufacture:         e.YearOfManufacture,
				ColorInCopy:               e.ColorInCopy,
				TaxDate:                   &taxDate,
				VendorGroup:               e.VendorGroup,
				Mile:                      e.Mile,
				SalesPrice:                e.SalesPrice,
				SoldAmount:                e.SoldAmount,
				VatPercentage:             e.VatPercentage,
				AssetYear:                 e.AssetYear,
				BranchDescription:         e.BranchDescription,
				ContractNo:                e.ContractNo,
				ChassisNo:                 e.ChassisNo,
				EngineNo:                  e.EngineNo,
				AssetType:                 e.AssetType,
				BranchCode:                e.BranchCode,
				SellerCode:                e.SellerCode,
				SellerName:                e.SellerName,
				FuelTap:                   e.FuelTap,
				Gear:                      e.Gear,
				GearName:                  e.GearName,
				Unque:                     e.Unque,
				CarTypeCon:                e.CarTypeCon,
				PointGrade1Final:          e.PointGrade1Final,
				PointGrade2Con:            e.PointGrade2Con,
				PointGrade3Con:            e.PointGrade3Con,
				FinanceZipFilename:        e.FinanceZipFilename,
				SellerOffer:               e.SellerOffer,
				AuctionTime:               &auctionTime,
				Remark:                    e.Remark,
				SpecialAuction:            e.SpecialAuction,
				PersonalAssessmentAuction: e.PersonalAssessmentAuction,
				PersonalAssessment:        e.PersonalAssessment,
				Tool:                      e.Tool,
				SaleRounds:                e.SaleRounds,
			}
			allKeys[compositeKey] = struct{}{}

		}
	}

	lotSouLotLineFromDbMap := make(map[string]entity.LotSouLotLine)
	// Use DocumentNo+AssetCode as composite key
	compositeKey := util.Val(souLotLine.AssetCode)
	lotSouLotLineFromDbMap[compositeKey] = souLotLine
	allKeys[compositeKey] = struct{}{}

	//section 4 - compare and update
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {

		lotSouLotLineRepo := lotSouLotLineRepository.NewLotSouLotLineRepository(tx)

		currentDateTime := util.Now()

		for compositeKey := range allKeys {
			erp, erpExists := lotSouLotLineFromErpMap[compositeKey]
			db, dbExists := lotSouLotLineFromDbMap[compositeKey]

			if erpExists && dbExists {
				// มีทั้ง ERP และ DB
				temp := db

				temp.AuctionDate = erp.AuctionDate
				temp.DocumentNo = erp.DocumentNo
				temp.AssetCode = erp.AssetCode
				temp.Floor = erp.Floor
				temp.AuctCode = erp.AuctCode
				temp.AuctionNo = erp.AuctionNo
				temp.CityCode = erp.CityCode
				temp.AssetGroupCode = erp.AssetGroupCode
				temp.BrandDescription = erp.BrandDescription
				temp.ModelDescription = erp.ModelDescription
				temp.Model = erp.Model
				temp.SubModel = erp.SubModel
				temp.SubKey = erp.SubKey
				temp.EngineSize = erp.EngineSize
				temp.LicensePlateNo = erp.LicensePlateNo
				temp.CityDescription = erp.CityDescription
				temp.RegistrationYear = erp.RegistrationYear
				temp.YearOfManufacture = erp.YearOfManufacture
				temp.ColorInCopy = erp.ColorInCopy
				temp.TaxDate = erp.TaxDate
				temp.VendorGroup = erp.VendorGroup
				temp.Mile = erp.Mile
				temp.SalesPrice = erp.SalesPrice
				temp.SoldAmount = erp.SoldAmount
				temp.VatPercentage = erp.VatPercentage
				temp.AssetYear = erp.AssetYear
				temp.BranchDescription = erp.BranchDescription
				temp.ContractNo = erp.ContractNo
				temp.ChassisNo = erp.ChassisNo
				temp.EngineNo = erp.EngineNo
				temp.AssetType = erp.AssetType
				temp.BranchCode = erp.BranchCode
				temp.SellerCode = erp.SellerCode
				temp.SellerName = erp.SellerName
				temp.FuelTap = erp.FuelTap
				temp.Gear = erp.Gear
				temp.GearName = erp.GearName
				temp.Unque = erp.Unque
				temp.CarTypeCon = erp.CarTypeCon
				temp.PointGrade1Final = erp.PointGrade1Final
				temp.PointGrade2Con = erp.PointGrade2Con
				temp.PointGrade3Con = erp.PointGrade3Con
				temp.FinanceZipFilename = erp.FinanceZipFilename
				temp.SellerOffer = erp.SellerOffer
				temp.AuctionTime = erp.AuctionTime
				temp.Remark = erp.Remark
				temp.SpecialAuction = erp.SpecialAuction
				temp.PersonalAssessmentAuction = erp.PersonalAssessmentAuction
				temp.PersonalAssessment = erp.PersonalAssessment
				temp.Tool = erp.Tool
				temp.SaleRounds = erp.SaleRounds

				temp.UpdatedBy = user
				temp.UpdatedDate = &currentDateTime
				temp.IsActive = true
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = &currentDateTime

				err := lotSouLotLineRepo.Update(temp)
				if err != nil {
					log.Error(err)
					return err
				}

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = user
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = &currentDateTime

				err := lotSouLotLineRepo.Update(temp)
				if err != nil {
					log.Error(err)
					return err
				}
			}
		}

		return nil
	})

}

func (s *lotService) DeleteLot(id int, actionBy *int) error {
	_, err := s.Repo.GetById(id)
	if err != nil {
		log.Error(err)
		return err
	}

	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		lotRepo := lotRepository.NewLotRepository(tx)
		lotAssetTypeRepo := lotAssetTypeRepository.NewLotAssetTypeRepository(tx)
		lotActiveOptionRepo := lotActiveOptionRepository.NewLotActiveOptionRepository(tx)
		lotStreamingRepo := lotStreamingRepository.NewLotStreamingRepository(tx)
		lotSouRepo := lotSouRepository.NewLotSouRepository(tx)
		lotSouLotLineRepo := lotSouLotLineRepository.NewLotSouLotLineRepository(tx)

		err := lotRepo.Delete(id)
		if err != nil {
			log.Error(err)
			return err
		}

		err = lotAssetTypeRepo.DeleteByLotId(id)
		if err != nil {
			log.Error(err)
			return err
		}

		err = lotActiveOptionRepo.DeleteByLotId(id)
		if err != nil {
			log.Error(err)
			return err
		}

		err = lotStreamingRepo.DeleteByLotId(id)
		if err != nil {
			log.Error(err)
			return err
		}

		err = lotSouRepo.DeleteByLotId(id)
		if err != nil {
			log.Error(err)
			return err
		}

		err = lotSouLotLineRepo.DeleteByLotId(id)
		if err != nil {
			log.Error(err)
			return err
		}

		return nil
	})
}

func (s *lotService) SearchLots(req dto.LotSearchReqDto) ([]dto.LotSearchRespInternalServiceDto, error) {
	layoutDMY := constant.DateFormatDMY
	layoutTime := constant.TimeFormatShort

	lotDbs, err := s.Repo.FindLotWithFilter(req)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	var lotIds []int

	for _, lotDb := range lotDbs {
		lotIds = append(lotIds, lotDb.Id)
	}

	souLotLineDbs, err := s.LotSouLotLineRepo.GetAllByLotIds(lotIds)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	mapSouLotLineDbs := make(map[int][]*entity.LotSouLotLine)
	if souLotLineDbs != nil {
		for _, souLotLineDb := range souLotLineDbs {
			if souLotLineDb.LotId != 0 {
				mapSouLotLineDbs[souLotLineDb.LotId] = append(mapSouLotLineDbs[souLotLineDb.LotId], souLotLineDb)
			}
		}
	}

	resultAssetType, err := s.CommonAuctionCoreRepo.GetAllAssetType()
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	assetTypeMap := make(map[string]*model.AssetTypeForJoin)
	if resultAssetType != nil {
		for _, assetType := range resultAssetType {
			if assetType.AssetTypeCode != "" {
				assetTypeMap[assetType.AssetTypeCode] = &assetType
			}
		}
	}

	var responseDtos []dto.LotSearchRespInternalServiceDto
	if lotDbs != nil && len(lotDbs) > 0 {
		for _, lot := range lotDbs {
			assetTypes := []*dto.LotAssetTypeCountDto{}
			subAssetTypeOthers := []*dto.LotAssetTypeCountDto{}
			subAssetTypeOtherMaps := make(map[string]int)

			if mapSouLotLineDbs[lot.Id] != nil {
				assetTypeC := 0
				assetTypeG := 0
				assetTypeOther := 0
				for _, souLotLine := range mapSouLotLineDbs[lot.Id] {
					if util.Val(souLotLine.AssetType) == "C" {
						assetTypeC++
					} else if util.Val(souLotLine.AssetType) == "G" {
						assetTypeG++
					} else {
						subAssetTypeOtherMaps[util.Val(souLotLine.AssetType)]++
						assetTypeOther++
					}
				}

				if assetTypeC != 0 {
					assetTypes = append(assetTypes, &dto.LotAssetTypeCountDto{
						AssetTypeCode: &assetTypeMap["C"].AssetTypeCode,
						DescriptionTh: &assetTypeMap["C"].DescriptionTh,
						DescriptionEn: &assetTypeMap["C"].DescriptionEn,
						Count:         &assetTypeC,
					})
				}

				if assetTypeG != 0 {
					assetTypes = append(assetTypes, &dto.LotAssetTypeCountDto{
						AssetTypeCode: &assetTypeMap["G"].AssetTypeCode,
						DescriptionTh: &assetTypeMap["G"].DescriptionTh,
						DescriptionEn: &assetTypeMap["G"].DescriptionEn,
						Count:         &assetTypeG,
					})
				}

				for _, assetType := range resultAssetType {
					if subAssetTypeOtherMaps[assetType.AssetTypeCode] != 0 {
						count := subAssetTypeOtherMaps[assetType.AssetTypeCode]
						subAssetTypeOthers = append(subAssetTypeOthers, &dto.LotAssetTypeCountDto{
							AssetTypeCode: &assetType.AssetTypeCode,
							DescriptionTh: &assetType.DescriptionTh,
							DescriptionEn: &assetType.DescriptionEn,
							Count:         &count,
						})
					}
				}

				if assetTypeOther != 0 {
					descriptionTh := "สินค้าอื่นๆ"
					descriptionEn := "Others"
					assetTypes = append(assetTypes, &dto.LotAssetTypeCountDto{
						AssetTypeCode: &descriptionEn,
						DescriptionTh: &descriptionTh,
						DescriptionEn: &descriptionEn,
						Count:         &assetTypeOther,
						SubAssetType:  subAssetTypeOthers,
					})
				}
			}

			var proxyStartDate *string
			var proxyStartTime *string

			if lot.ProxyStartDate != nil {
				temp := lot.ProxyStartDate.Format(layoutDMY)
				proxyStartDate = &temp
			}

			if lot.ProxyStartTime != nil {
				temp := lot.ProxyStartTime.Format(layoutTime)
				proxyStartTime = &temp
			}

			auction, err := s.AuctionRepo.GetById(lot.AuctionId)
			if err != nil {
				log.Error(err)
				return nil, err
			}

			auctionDate := lot.AuctionDate.Format(layoutDMY)
			auctionTime := lot.AuctionTime.Format(layoutTime)
			floor := fmt.Sprintf("ลาน %s", *lot.Floor.Floor)

			responseDtos = append(responseDtos, dto.LotSearchRespInternalServiceDto{
				LotSearchRespDto: dto.LotSearchRespDto{Id: lot.Id,
					BranchId:       &lot.BranchId,
					BranchNameTh:   lot.Branch.DescriptionTh,
					BranchNameEn:   lot.Branch.DescriptionEn,
					AuctionDate:    &auctionDate,
					AuctionTime:    &auctionTime,
					FloorId:        &lot.FloorId,
					Floor:          &floor,
					Name:           &lot.Name,
					EventName:      auction.Event.DescriptionTh,
					ProxyStartDate: proxyStartDate,
					ProxyStartTime: proxyStartTime,
					IsActive:       *lot.IsActive,
					FloorStatus:    calFloorStatus(lot.FloorStatus, lot.ShowStartDate, lot.ShowStartTime, lot.ShowEndDate, lot.ShowEndTime),
					AssetTypes:     assetTypes,
				},
			})

		}
	}

	return responseDtos, nil
}

func (s *lotService) SearchLotLineFilter(req dto.SouLotLinePageReqDto) (dto.SouLotLinePageRespDto[dto.SouLotLineDto], error) {

	lotLineDbs, err := s.LotSouLotLineRepo.FindSouLotLineWithFilter(req)
	if err != nil {
		log.Error(err)
		return dto.SouLotLinePageRespDto[dto.SouLotLineDto]{}, err
	}

	count, err := s.LotSouLotLineRepo.CountSouLotLineWithFilter(req)
	if err != nil {
		return dto.SouLotLinePageRespDto[dto.SouLotLineDto]{}, err
	}

	compositeKeyDupMap := make(map[string]bool)
	dupAuctionNo := []string{}

	erpDelete := constant.LotLineErpDeleted

	mapResult := make([]dto.SouLotLineDto, len(lotLineDbs))
	for i, v := range lotLineDbs {
		mapResult[i] = *util.MapToPtr[dto.SouLotLineDto](v)
		mapResult[i].AssetTypeDescriptionTh = &v.MasterAssetType.DescriptionTh
		mapResult[i].AssetTypeDescriptionEn = &v.MasterAssetType.DescriptionEn

		if compositeKeyDupMap[util.Val(v.Floor)+"-"+strconv.Itoa(util.Val(v.AuctionNo))] {
			dupAuctionNo = append(dupAuctionNo, util.Val(v.AuctCode)+strconv.Itoa(util.Val(v.AuctionNo)))
		}

		compositeKeyDupMap[util.Val(v.Floor)+"-"+strconv.Itoa(util.Val(v.AuctionNo))] = true

		mapResult[i].DupAuctionNos = dupAuctionNo

		if v.IsDeletedByErp {
			mapResult[i].ProductStatus = &erpDelete
		}
	}

	var latestSyncDate *time.Time
	if lotLineDbs != nil && len(lotLineDbs) > 0 {
		latestSyncDate = lotLineDbs[0].LatestSyncDate
	}

	resp := dto.SouLotLinePageRespDto[dto.SouLotLineDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func validateData(lot dto.LotDto) (dto.LotDto, error) {

	if util.Val(lot.BranchId) == 0 {
		return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "auctionTypeCode is required", "")
	}
	if util.Val(lot.AuctionDate) == "" {
		return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "auctionDate is required", "")
	}
	if util.Val(lot.AuctionTime) == "" {
		return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "auctionTime is required", "")
	}
	if util.Val(lot.FloorId) == 0 {
		return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "floorId is required", "")
	}
	if len(lot.AssetTypeList) == 0 {
		return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "assetTypeList is required", "")
	}
	if util.Val(lot.AuctionId) == 0 {
		return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "auctionId is required", "")
	}
	if util.Val(lot.ShowStartDate) == "" {
		return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "showStartDate is required", "")
	}
	if util.Val(lot.ShowStartTime) == "" {
		return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "showStartTime is required", "")
	}
	if util.Val(lot.ShowEndDate) == "" {
		return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "showEndDate is required", "")
	}
	if util.Val(lot.ShowEndTime) == "" {
		return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "showEndTime is required", "")
	}

	layoutDMY := constant.DateFormatDMY
	layoutDateTime := constant.DateTimeFormatDMY
	defaultTime := "00:00"

	auctionDate, _ := time.Parse(layoutDMY, util.Val(lot.AuctionDate))

	yesterday := time.Now().AddDate(0, 0, -1)
	//cannot insert start yesterday
	if yesterday.After(auctionDate) {
		return lot, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "auctionDate cant after yesterday", "")
	}

	if lot.ShowStartTime == nil {
		lot.ShowStartTime = &defaultTime
	}
	if lot.ShowEndTime == nil {
		lot.ShowEndTime = &defaultTime
	}

	auctionDateTimeStr := fmt.Sprintf("%s %s", util.Val(lot.AuctionDate), util.Val(lot.AuctionTime))
	auctionDateTime, _ := time.Parse(layoutDateTime, auctionDateTimeStr)

	showStartDateTimeStr := fmt.Sprintf("%s %s", util.Val(lot.ShowStartDate), util.Val(lot.ShowStartTime))
	showStartDateTime, _ := time.Parse(layoutDateTime, showStartDateTimeStr)

	showEndDateTimeStr := fmt.Sprintf("%s %s", util.Val(lot.ShowEndDate), util.Val(lot.ShowEndTime))
	showEndDateTime, _ := time.Parse(layoutDateTime, showEndDateTimeStr)

	showEndDateStr := fmt.Sprintf("%s %s", util.Val(lot.ShowEndDate), defaultTime)
	showEndDate, _ := time.Parse(layoutDateTime, showEndDateStr)

	if showStartDateTime.After(auctionDateTime) {
		return lot, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "showStartDateTime cant after auctionDateTime", "")
	}

	if showEndDate.Before(auctionDateTime) {
		return lot, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "showEndDate cant after auctionDateTime", "")
	}

	if showStartDateTime.After(showEndDateTime) {
		return lot, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "showStartDateTime cant after showEndDateTime", "")
	}

	if lot.ProxyStartDate != nil {
		if lot.ProxyStartTime == nil {
			lot.ProxyStartTime = &defaultTime
		}

		proxyStartDateStr := *lot.ProxyStartDate + " " + *lot.ProxyStartTime
		proxyStartDate, _ := time.Parse(layoutDateTime, proxyStartDateStr)

		if proxyStartDate.After(auctionDateTime) {
			return lot, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "proxyStartDate cant after auctionDateTime", "")
		}
	}

	streamingNameMap := make(map[string]bool)
	streamingLinkMap := make(map[string]bool)
	for _, streaming := range lot.StreamingList {

		if util.Val(streaming.StreamingName) == "" {
			return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "streamingName is required", "")
		}
		if util.Val(streaming.StreamingLink) == "" {
			return lot, errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "streamingLink is required", "")
		}

		if streamingNameMap[util.Val(streaming.StreamingName)] {
			return lot, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "Duplicate streamingName", "")
		}
		streamingNameMap[util.Val(streaming.StreamingName)] = true

		if streamingLinkMap[util.Val(streaming.StreamingLink)] {
			return lot, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "Duplicate streamingLink", "")
		}
		streamingLinkMap[util.Val(streaming.StreamingLink)] = true
	}

	return lot, nil
}

func prepareCreateLotToDb(req dto.LotDto, actionBy *int, now time.Time) entity.Lot {
	layoutDMY := constant.DateFormatDMY
	layoutTime := constant.TimeFormatShort
	layoutTimeV2 := constant.TimeFormat

	auctionDate, _ := time.Parse(layoutDMY, util.Val(req.AuctionDate))
	auctionTime, _ := time.Parse(layoutTime, util.Val(req.AuctionTime))
	showStartDate, _ := time.Parse(layoutDMY, util.Val(req.ShowStartDate))
	showStartTime, _ := time.Parse(layoutTime, util.Val(req.ShowStartTime))
	showEndDate, _ := time.Parse(layoutDMY, util.Val(req.ShowEndDate))
	showEndTime, _ := time.Parse(layoutTime, util.Val(req.ShowEndTime))

	//Insert Lot
	entityLot := entity.Lot{
		BranchId:       util.Val(req.BranchId),
		AuctionDate:    auctionDate,
		AuctionTime:    auctionTime,
		FloorId:        util.Val(req.FloorId),
		AuctionId:      util.Val(req.AuctionId),
		Name:           util.Val(req.Name),
		Description:    util.Val(req.Description),
		IsActive:       &req.IsActive,
		FloorStatus:    util.Val(req.FloorStatus),
		AuctionChannel: util.Val(req.AuctionChannel),
		ShowStartDate:  showStartDate,
		ShowStartTime:  &showStartTime,
		ShowEndDate:    showEndDate,
		ShowEndTime:    &showEndTime,
		BaseEntity: &model.BaseEntity{
			CreatedDate: now,
			CreatedBy:   actionBy,
			UpdatedDate: &now,
			UpdatedBy:   actionBy,
		},
	}

	if req.ProxyStartDate != nil {
		proxyStartDate, _ := time.Parse(layoutDMY, util.Val(req.ProxyStartDate))
		proxyStartTime, _ := time.Parse(layoutTime, util.Val(req.ProxyStartTime))

		entityLot.ProxyStartDate = &proxyStartDate
		entityLot.ProxyStartTime = &proxyStartTime
	}

	for _, activeOption := range req.ActiveOptionList {

		entityLotActiveOption := entity.LotActiveOption{
			ConfigFeatureLotId: *activeOption.ConfigFeatureLotId,
			BaseEntity: &model.BaseEntity{
				CreatedDate: now,
				CreatedBy:   actionBy,
				UpdatedDate: &now,
				UpdatedBy:   actionBy,
			},
		}
		entityLot.LotActiveOptions = append(entityLot.LotActiveOptions, entityLotActiveOption)
	}

	for _, assetTypeId := range req.AssetTypeList {

		entityLotAssetType := entity.LotAssetType{
			AssetTypeId: *assetTypeId,
			BaseEntity: &model.BaseEntity{
				CreatedDate: now,
				CreatedBy:   actionBy,
				UpdatedDate: &now,
				UpdatedBy:   actionBy,
			},
		}
		entityLot.LotAssetTypes = append(entityLot.LotAssetTypes, entityLotAssetType)
	}

	for _, streaming := range req.StreamingList {

		entityLotStreaming := entity.LotStreaming{
			Order:         *streaming.Order,
			StreamingName: *streaming.StreamingName,
			StreamingLink: *streaming.StreamingLink,
			BaseEntity: &model.BaseEntity{
				CreatedDate: now,
				CreatedBy:   actionBy,
				UpdatedDate: &now,
				UpdatedBy:   actionBy,
			},
		}
		entityLot.LotStreamings = append(entityLot.LotStreamings, entityLotStreaming)
	}

	for _, sou := range req.SouList {
		auctionDate, _ := time.Parse(layoutDMY, *sou.AuctionDate)
		auctionTime, _ := time.Parse(layoutTimeV2, *sou.AuctionTime)
		dueDate, _ := time.Parse(layoutDMY, *sou.DueDate)
		entityLotSou := entity.LotSou{
			CompanyCode:     *sou.CompanyCode,
			DocumentNo:      *sou.DocumentNo,
			AuctionDate:     auctionDate,
			AuctionTime:     auctionTime,
			DueDate:         dueDate,
			NoOfNewCar:      *sou.NoOfNewCar,
			BranchCode:      *sou.BranchCode,
			BranchNameTh:    *sou.BranchNameTh,
			BranchNameEn:    *sou.BranchNameEn,
			LocationCode:    *sou.LocationCode,
			Floor:           *sou.Floor,
			AssetTypeCode:   *sou.AssetTypeCode,
			AssetTypeNameTh: *sou.AssetTypeNameTh,
			AssetTypeNameEn: *sou.AssetTypeNameEn,
			NoOfOldCar:      *sou.NoOfOldCar,
			EventCode:       sou.EventCode,
			Status:          *sou.Status,
			AuctionStatus:   *sou.AuctionStatus,
			RemarkTh:        *sou.RemarkTh,
			RemarkEn:        *sou.RemarkEn,
			RemarkExtra:     *sou.RemarkExtra,
			ActionType:      sou.ActionType,
			BaseEntity: &model.BaseEntity{
				CreatedDate: now,
				CreatedBy:   actionBy,
				UpdatedDate: &now,
				UpdatedBy:   actionBy,
			},
			LatestSyncDate: &now,
		}
		entityLot.LotSous = append(entityLot.LotSous, entityLotSou)
	}

	return entityLot

}

func (s *lotService) createLotToDb(lot entity.Lot) error {

	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		lotRepo := lotRepository.NewLotRepository(tx)
		lotAssetTypeRepo := lotAssetTypeRepository.NewLotAssetTypeRepository(tx)
		lotActiveOptionRepo := lotActiveOptionRepository.NewLotActiveOptionRepository(tx)
		lotStreamingRepo := lotStreamingRepository.NewLotStreamingRepository(tx)
		lotSouRepo := lotSouRepository.NewLotSouRepository(tx)

		err := lotRepo.Insert(lot)
		if err != nil {
			log.Error(err)
			return err
		}

		for _, assetTypeId := range lot.LotAssetTypes {
			assetTypeId.LotId = lot.Id
			err := lotAssetTypeRepo.Insert(assetTypeId)
			if err != nil {
				log.Error(err)
				return err
			}
		}

		for _, activeOption := range lot.LotActiveOptions {
			activeOption.LotId = lot.Id
			err := lotActiveOptionRepo.Insert(activeOption)
			if err != nil {
				log.Error(err)
				return err
			}
		}

		for _, streaming := range lot.LotStreamings {
			streaming.LotId = lot.Id
			err := lotStreamingRepo.Insert(streaming)
			if err != nil {
				log.Error(err)
				return err
			}
		}

		for _, sou := range lot.LotSous {
			sou.LotId = lot.Id
			err := lotSouRepo.Insert(sou)
			if err != nil {
				log.Error(err)
				return err
			}
		}

		return nil
	})
}

func (s *lotService) prepareUpdateLotToDb(req dto.LotDto, actionBy *int, now time.Time) (entity.Lot, error) {
	layoutDMY := constant.DateFormatDMY
	layoutTime := constant.TimeFormatShort
	layoutTimeV2 := constant.TimeFormat

	auctionDate, _ := time.Parse(layoutDMY, util.Val(req.AuctionDate))
	auctionTime, _ := time.Parse(layoutTime, util.Val(req.AuctionTime))
	showStartDate, _ := time.Parse(layoutDMY, util.Val(req.ShowStartDate))
	showStartTime, _ := time.Parse(layoutTime, util.Val(req.ShowStartTime))
	showEndDate, _ := time.Parse(layoutDMY, util.Val(req.ShowEndDate))
	showEndTime, _ := time.Parse(layoutTime, util.Val(req.ShowEndTime))

	entityLot, err := s.Repo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return entityLot, err
	}

	entityLot.UpdatedDate = &now
	entityLot.UpdatedBy = actionBy
	entityLot.BranchId = util.Val(req.BranchId)
	entityLot.AuctionDate = auctionDate
	entityLot.AuctionTime = auctionTime
	entityLot.FloorId = util.Val(req.FloorId)
	entityLot.AuctionId = util.Val(req.AuctionId)
	entityLot.Name = util.Val(req.Name)
	entityLot.Description = util.Val(req.Description)
	entityLot.IsActive = &req.IsActive
	entityLot.FloorStatus = util.Val(req.FloorStatus)
	entityLot.AuctionChannel = util.Val(req.AuctionChannel)
	entityLot.ShowStartDate = showStartDate
	entityLot.ShowStartTime = &showStartTime
	entityLot.ShowEndDate = showEndDate
	entityLot.ShowEndTime = &showEndTime

	if req.ProxyStartDate != nil {
		proxyStartDate, _ := time.Parse(layoutDMY, util.Val(req.ProxyStartDate))
		proxyStartTime, _ := time.Parse(layoutTime, util.Val(req.ProxyStartTime))

		entityLot.ProxyStartDate = &proxyStartDate
		entityLot.ProxyStartTime = &proxyStartTime
	} else {
		entityLot.ProxyStartDate = nil
		entityLot.ProxyStartTime = nil
	}

	for _, activeOption := range req.ActiveOptionList {
		entityLotActiveOption := entity.LotActiveOption{
			ConfigFeatureLotId: *activeOption.ConfigFeatureLotId,
			BaseEntity: &model.BaseEntity{
				CreatedDate: now,
				CreatedBy:   actionBy,
				UpdatedDate: &now,
				UpdatedBy:   actionBy,
			},
		}
		entityLot.LotActiveOptions = append(entityLot.LotActiveOptions, entityLotActiveOption)
	}

	for _, assetTypeId := range req.AssetTypeList {
		entityLotAssetType := entity.LotAssetType{
			AssetTypeId: *assetTypeId,
			BaseEntity: &model.BaseEntity{
				CreatedDate: now,
				CreatedBy:   actionBy,
				UpdatedDate: &now,
				UpdatedBy:   actionBy,
			},
		}
		entityLot.LotAssetTypes = append(entityLot.LotAssetTypes, entityLotAssetType)
	}

	for _, streaming := range req.StreamingList {

		entityLotStreaming := entity.LotStreaming{}

		if streaming.Id != 0 {
			entityLotStreaming, err = s.LotStreamingRepo.GetById(streaming.Id)
			if err != nil {
				log.Error(err)
				return entityLot, err
			}
		} else {
			entityLotStreaming.CreatedBy = actionBy
			entityLotStreaming.CreatedDate = now
		}

		entityLotStreaming.UpdatedDate = &now
		entityLotStreaming.UpdatedBy = actionBy
		entityLotStreaming.Order = util.Val(streaming.Order)
		entityLotStreaming.StreamingName = util.Val(streaming.StreamingName)
		entityLotStreaming.StreamingLink = util.Val(streaming.StreamingLink)

		entityLot.LotStreamings = append(entityLot.LotStreamings, entityLotStreaming)
	}

	for _, sou := range req.SouList {
		auctionDate, _ := time.Parse(layoutDMY, *sou.AuctionDate)
		auctionTime, _ := time.Parse(layoutTimeV2, *sou.AuctionTime)
		dueDate, _ := time.Parse(layoutDMY, *sou.DueDate)

		entityLotSou := entity.LotSou{}

		if sou.Id != 0 {
			entityLotSou, err = s.LotSouRepo.GetById(sou.Id)
			if err != nil {
				log.Error(err)
				return entityLot, err
			}
		} else {
			entityLotSou.CreatedBy = actionBy
			entityLotSou.CreatedDate = now
		}

		entityLotSou.UpdatedDate = &now
		entityLotSou.UpdatedBy = actionBy
		entityLotSou.CompanyCode = *sou.CompanyCode
		entityLotSou.DocumentNo = *sou.DocumentNo
		entityLotSou.AuctionDate = auctionDate
		entityLotSou.AuctionTime = auctionTime
		entityLotSou.DueDate = dueDate
		entityLotSou.NoOfNewCar = *sou.NoOfNewCar
		entityLotSou.BranchCode = *sou.BranchCode
		entityLotSou.BranchNameTh = *sou.BranchNameTh
		entityLotSou.BranchNameEn = *sou.BranchNameEn
		entityLotSou.LocationCode = *sou.LocationCode
		entityLotSou.Floor = *sou.Floor
		entityLotSou.AssetTypeCode = *sou.AssetTypeCode
		entityLotSou.AssetTypeNameTh = *sou.AssetTypeNameTh
		entityLotSou.AssetTypeNameEn = *sou.AssetTypeNameEn
		entityLotSou.NoOfOldCar = *sou.NoOfOldCar
		entityLotSou.EventCode = sou.EventCode
		entityLotSou.Status = *sou.Status
		entityLotSou.AuctionStatus = *sou.AuctionStatus
		entityLotSou.RemarkTh = *sou.RemarkTh
		entityLotSou.RemarkEn = *sou.RemarkEn
		entityLotSou.RemarkExtra = *sou.RemarkExtra
		entityLotSou.ActionType = sou.ActionType

		entityLot.LotSous = append(entityLot.LotSous, entityLotSou)
	}

	return entityLot, nil

}

func (s *lotService) updateLotToDb(lot entity.Lot) error {

	lotStreamingDbs, err := s.LotStreamingRepo.GetAllByLotId(lot.Id)
	if err != nil {
		log.Error(err)
		return err
	}

	lotSouDbs, err := s.LotSouRepo.GetAllByLotId(lot.Id)
	if err != nil {
		log.Error(err)
		return err
	}

	lotSouLotLineDbs, err := s.LotSouLotLineRepo.GetAllByLotId(lot.Id)
	if err != nil {
		log.Error(err)
		return err
	}

	souLotLineMap := map[*int][]*entity.LotSouLotLine{}

	if lotSouLotLineDbs != nil {
		for _, souLotLine := range lotSouLotLineDbs {
			souLotLineMap[souLotLine.LotSouId] = append(souLotLineMap[souLotLine.LotSouId], souLotLine)
		}
	}

	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		lotRepo := lotRepository.NewLotRepository(tx)
		lotAssetTypeRepo := lotAssetTypeRepository.NewLotAssetTypeRepository(tx)
		lotActiveOptionRepo := lotActiveOptionRepository.NewLotActiveOptionRepository(tx)
		lotStreamingRepo := lotStreamingRepository.NewLotStreamingRepository(tx)
		lotSouRepo := lotSouRepository.NewLotSouRepository(tx)
		lotSouLotLineRepo := lotSouLotLineRepository.NewLotSouLotLineRepository(tx)

		err := lotRepo.UpdateAllFields(lot)
		if err != nil {
			log.Error(err)
			return err
		}

		err = lotAssetTypeRepo.DeleteByLotId(lot.Id)
		if err != nil {
			log.Error(err)
			return err
		}

		for _, assetTypeId := range lot.LotAssetTypes {
			assetTypeId.LotId = lot.Id
			err := lotAssetTypeRepo.Insert(assetTypeId)
			if err != nil {
				log.Error(err)
				return err
			}
		}

		err = lotActiveOptionRepo.DeleteByLotId(lot.Id)
		if err != nil {
			log.Error(err)
			return err
		}

		for _, activeOption := range lot.LotActiveOptions {
			activeOption.LotId = lot.Id
			err := lotActiveOptionRepo.Insert(activeOption)
			if err != nil {
				log.Error(err)
				return err
			}
		}

		streamingNotDeleteIds := map[int]int{}

		for _, streaming := range lot.LotStreamings {
			if streaming.Id != 0 {
				err := lotStreamingRepo.UpdateAllFields(streaming)
				if err != nil {
					log.Error(err)
					return err
				}
				streamingNotDeleteIds[streaming.Id] = streaming.Id
			} else {
				streaming.LotId = lot.Id
				err := lotStreamingRepo.Insert(streaming)
				if err != nil {
					log.Error(err)
					return err
				}
				streamingNotDeleteIds[streaming.Id] = streaming.Id
			}
		}

		souNotDeleteIds := map[int]int{}

		for _, sou := range lot.LotSous {
			if sou.Id != 0 {
				err := lotSouRepo.UpdateAllFields(sou)
				if err != nil {
					log.Error(err)
					return err
				}
				souNotDeleteIds[sou.Id] = sou.Id
			} else {
				sou.LotId = lot.Id
				err := lotSouRepo.Insert(sou)
				if err != nil {
					log.Error(err)
					return err
				}
				souNotDeleteIds[sou.Id] = sou.Id
			}
		}

		if lotStreamingDbs != nil {
			for _, streaming := range lotStreamingDbs {
				if streamingNotDeleteIds[streaming.Id] == 0 {
					err = lotStreamingRepo.Delete(streaming.Id)
					if err != nil {
						log.Error(err)
						return err
					}
				}
			}
		}

		if lotSouDbs != nil {
			for _, sou := range lotSouDbs {
				if souNotDeleteIds[sou.Id] == 0 {

					if souLotLineMap[&sou.Id] != nil {
						for _, sou := range souLotLineMap[&sou.Id] {
							err := lotSouLotLineRepo.DeleteBySouId(sou.Id)
							if err != nil {
								log.Error(err)
								return err
							}
						}
					}

					err := lotSouRepo.Delete(sou.Id)
					if err != nil {
						log.Error(err)
						return err
					}
				}
			}
		}

		return nil
	})
}

func calFloorStatus(floorStatus int, showStartDate time.Time, showStartTime *time.Time, showEndDate time.Time, showEndTime *time.Time) *int {
	now := time.Now()

	showStartDateTime := time.Date(showStartDate.Year(), showStartDate.Month(), showStartDate.Day(), showStartTime.Hour(), showStartTime.Minute(), showStartTime.Second(), 0, showStartDate.Location())
	showEndDateTime := time.Date(showEndDate.Year(), showEndDate.Month(), showEndDate.Day(), showEndTime.Hour(), showEndTime.Minute(), showEndTime.Second(), 0, showEndDate.Location())

	if now.After(showStartDateTime) && floorStatus != 4 && floorStatus != 5 {
		floorStatus = 2
	}
	if now.After(showEndDateTime) {
		floorStatus = 3
	}

	return &floorStatus
}

func (s *lotService) LotLineExportExcel(req dto.SouLotLinePageReqDto) (*dto.SouLotLineExportDto, error) {

	lotLineDbs, err := s.LotSouLotLineRepo.FindSouLotLineWithFilterNotPage(req)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	excel := excelize.NewFile()

	styles, err := createExcelStyles(excel)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	err = exportLotLineExcel(excel, styles, lotLineDbs)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	buf, err := excel.WriteToBuffer()
	if err != nil {
		log.Error(err)
		return nil, err
	}

	filename := "products_" + time.Now().Format("200601021504") + ".xlsx"

	resp := dto.SouLotLineExportDto{
		Filename:   &filename,
		ExcelBytes: buf,
	}

	return &resp, err
}

func createExcelStyles(excel *excelize.File) (map[string]int, error) {
	styles := make(map[string]int)

	var err error

	styles["HeaderStyle"], err = excel.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "Angsana New",
			Size:   16,
			Bold:   true,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	if err != nil {
		return nil, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "HeaderStyle error", "")
	}

	styles["RowStyle"], err = excel.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "Angsana New",
			Size:   16,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	if err != nil {
		return nil, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "RowStyle error", "")
	}

	return styles, nil
}

func exportLotLineExcel(
	excel *excelize.File, styles map[string]int,
	lotLineList []entity.LotSouLotLine,
) error {
	layout := constant.DateFormatDMY
	layoutTime := constant.TimeFormatShort

	sheetName := excel.GetSheetName(excel.GetActiveSheetIndex())

	headers := []string{
		"AuctionDate", "DocumentNo", "AssetCode", "Floor", "AuctCode",
		"AuctionNo", "CityCode", "AssetGroupCode", "BrandDescription", "ModelDescription",
		"Model", "SubModel", "SubKey", "EngineSize", "LicensePlateNo",
		"CityDescription", "RegistrationYear", "YearOfManufacture", "ColorInCopy", "TaxDate",
		"VendorGroup", "Mile", "SalesPrice", "SoldAmount", "VatPercentage",
		"AssetYear", "BranchDescription", "ContractNo", "ChassisNo", "EngineNo",
		"AssetType", "BranchCode", "SellerCode", "SellerName", "FuelTap",
		"Gear", "GearName", "Unque", "CarTypeCon", "PointGrade1Final",
		"PointGrade2Con", "PointGrade3Con", "FinanceZipFilename", "SellerOffer", "AuctionTime",
		"Remark", "SpecialAuction", "PersonalAssessmentAuction", "PersonalAssessment", "Tool",
		"SaleRounds", "AuctionStatus", "ProductStatus",
	}
	for i, header := range headers {
		columnName := getExcelColumnName(i + 1)
		cell := fmt.Sprintf("%s%d", columnName, 1)
		excel.SetCellValue(sheetName, cell, header)
		excel.SetCellStyle(sheetName, cell, cell, styles["HeaderStyle"])
	}

	for i, lotLine := range lotLineList {
		rowNum := i + 2

		excel.SetCellValue(sheetName, fmt.Sprintf("A%d", rowNum), FormatExportValue(lotLine.AuctionDate.Format(layout)))
		excel.SetCellValue(sheetName, fmt.Sprintf("B%d", rowNum), FormatExportValue(lotLine.DocumentNo))
		excel.SetCellValue(sheetName, fmt.Sprintf("C%d", rowNum), FormatExportValue(lotLine.AssetCode))
		excel.SetCellValue(sheetName, fmt.Sprintf("D%d", rowNum), FormatExportValue(lotLine.Floor))
		excel.SetCellValue(sheetName, fmt.Sprintf("E%d", rowNum), FormatExportValue(lotLine.AuctCode))
		if lotLine.AuctionNo != nil {
			fmt.Println(lotLine.AuctionNo)
			excel.SetCellValue(sheetName, fmt.Sprintf("F%d", rowNum), FormatExportValue(lotLine.AuctionNo))
		}
		excel.SetCellValue(sheetName, fmt.Sprintf("G%d", rowNum), FormatExportValue(lotLine.CityCode))
		excel.SetCellValue(sheetName, fmt.Sprintf("H%d", rowNum), FormatExportValue(lotLine.AssetGroupCode))
		excel.SetCellValue(sheetName, fmt.Sprintf("I%d", rowNum), FormatExportValue(lotLine.BrandDescription))
		excel.SetCellValue(sheetName, fmt.Sprintf("J%d", rowNum), FormatExportValue(lotLine.ModelDescription))
		excel.SetCellValue(sheetName, fmt.Sprintf("K%d", rowNum), FormatExportValue(lotLine.Model))
		excel.SetCellValue(sheetName, fmt.Sprintf("L%d", rowNum), FormatExportValue(lotLine.SubModel))
		excel.SetCellValue(sheetName, fmt.Sprintf("M%d", rowNum), FormatExportValue(lotLine.SubKey))
		excel.SetCellValue(sheetName, fmt.Sprintf("N%d", rowNum), FormatExportValue(lotLine.EngineSize))
		excel.SetCellValue(sheetName, fmt.Sprintf("O%d", rowNum), FormatExportValue(lotLine.LicensePlateNo))
		excel.SetCellValue(sheetName, fmt.Sprintf("P%d", rowNum), FormatExportValue(lotLine.CityDescription))
		excel.SetCellValue(sheetName, fmt.Sprintf("Q%d", rowNum), FormatExportValue(lotLine.RegistrationYear))
		excel.SetCellValue(sheetName, fmt.Sprintf("R%d", rowNum), FormatExportValue(lotLine.YearOfManufacture))
		excel.SetCellValue(sheetName, fmt.Sprintf("S%d", rowNum), FormatExportValue(lotLine.ColorInCopy))
		excel.SetCellValue(sheetName, fmt.Sprintf("T%d", rowNum), FormatExportValue(lotLine.TaxDate.Format(layout)))
		excel.SetCellValue(sheetName, fmt.Sprintf("U%d", rowNum), FormatExportValue(lotLine.VendorGroup))
		excel.SetCellValue(sheetName, fmt.Sprintf("V%d", rowNum), FormatExportValue(lotLine.Mile))
		excel.SetCellValue(sheetName, fmt.Sprintf("W%d", rowNum), FormatExportValue(lotLine.SalesPrice))
		excel.SetCellValue(sheetName, fmt.Sprintf("X%d", rowNum), FormatExportValue(lotLine.SoldAmount))
		excel.SetCellValue(sheetName, fmt.Sprintf("Y%d", rowNum), FormatExportValue(lotLine.VatPercentage))
		if lotLine.AssetYear != nil {
			excel.SetCellValue(sheetName, fmt.Sprintf("Z%d", rowNum), FormatExportValue(lotLine.AssetYear))
		}
		excel.SetCellValue(sheetName, fmt.Sprintf("AA%d", rowNum), FormatExportValue(lotLine.BranchDescription))
		excel.SetCellValue(sheetName, fmt.Sprintf("AB%d", rowNum), FormatExportValue(lotLine.ContractNo))
		excel.SetCellValue(sheetName, fmt.Sprintf("AC%d", rowNum), FormatExportValue(lotLine.ChassisNo))
		excel.SetCellValue(sheetName, fmt.Sprintf("AD%d", rowNum), FormatExportValue(lotLine.EngineNo))
		excel.SetCellValue(sheetName, fmt.Sprintf("AE%d", rowNum), FormatExportValue(lotLine.AssetType))
		excel.SetCellValue(sheetName, fmt.Sprintf("AF%d", rowNum), FormatExportValue(lotLine.BranchCode))
		excel.SetCellValue(sheetName, fmt.Sprintf("AG%d", rowNum), FormatExportValue(lotLine.SellerCode))
		excel.SetCellValue(sheetName, fmt.Sprintf("AH%d", rowNum), FormatExportValue(lotLine.SellerName))
		excel.SetCellValue(sheetName, fmt.Sprintf("AI%d", rowNum), FormatExportValue(lotLine.FuelTap))
		excel.SetCellValue(sheetName, fmt.Sprintf("AJ%d", rowNum), FormatExportValue(lotLine.Gear))
		excel.SetCellValue(sheetName, fmt.Sprintf("AK%d", rowNum), FormatExportValue(lotLine.GearName))
		excel.SetCellValue(sheetName, fmt.Sprintf("AL%d", rowNum), FormatExportValue(lotLine.Unque))
		excel.SetCellValue(sheetName, fmt.Sprintf("AM%d", rowNum), FormatExportValue(lotLine.CarTypeCon))
		excel.SetCellValue(sheetName, fmt.Sprintf("AN%d", rowNum), FormatExportValue(lotLine.PointGrade1Final))
		excel.SetCellValue(sheetName, fmt.Sprintf("AO%d", rowNum), FormatExportValue(lotLine.PointGrade2Con))
		excel.SetCellValue(sheetName, fmt.Sprintf("AP%d", rowNum), FormatExportValue(lotLine.PointGrade3Con))
		excel.SetCellValue(sheetName, fmt.Sprintf("AQ%d", rowNum), FormatExportValue(lotLine.FinanceZipFilename))
		excel.SetCellValue(sheetName, fmt.Sprintf("AR%d", rowNum), FormatExportValue(lotLine.SellerOffer))
		excel.SetCellValue(sheetName, fmt.Sprintf("AS%d", rowNum), FormatExportValue(lotLine.AuctionTime.Format(layoutTime)))
		excel.SetCellValue(sheetName, fmt.Sprintf("AT%d", rowNum), FormatExportValue(lotLine.Remark))
		excel.SetCellValue(sheetName, fmt.Sprintf("AU%d", rowNum), FormatExportValue(lotLine.SpecialAuction))
		excel.SetCellValue(sheetName, fmt.Sprintf("AV%d", rowNum), FormatExportValue(lotLine.PersonalAssessmentAuction))
		excel.SetCellValue(sheetName, fmt.Sprintf("AW%d", rowNum), FormatExportValue(lotLine.PersonalAssessment))
		excel.SetCellValue(sheetName, fmt.Sprintf("AX%d", rowNum), FormatExportValue(lotLine.Tool))
		excel.SetCellValue(sheetName, fmt.Sprintf("AY%d", rowNum), FormatExportValue(lotLine.SaleRounds))
		excel.SetCellValue(sheetName, fmt.Sprintf("AZ%d", rowNum), FormatExportValue(lotLine.AuctionStatus))
		excel.SetCellValue(sheetName, fmt.Sprintf("BA%d", rowNum), FormatExportValue(lotLine.ProductStatus))

		excel.SetCellStyle(sheetName, fmt.Sprintf("A%d", rowNum), fmt.Sprintf("BA%d", rowNum), styles["RowStyle"])

		customNumFormat := `dd/mm/yyyy`

		styleID1, _ := excel.NewStyle(&excelize.Style{
			CustomNumFmt: &customNumFormat,
			Font: &excelize.Font{
				Family: "Angsana New",
				Size:   16,
			},
			Alignment: &excelize.Alignment{
				Horizontal: "left",
				Vertical:   "center",
				WrapText:   true,
			},
		})

		excel.SetCellStyle(sheetName, fmt.Sprintf("A%d", rowNum), fmt.Sprintf("A%d", rowNum), styleID1)
	}

	excel.SetColWidth(sheetName, "A", "BA", 20)

	return nil
}

func FormatExportValue(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case *string:
		if v != nil {
			return *v
		}
		return ""
	case string:
		return v
	case *int:
		if v != nil {
			return fmt.Sprintf("%v", *v)
		}
		return ""
	case *float64:
		if v != nil {
			return fmt.Sprintf("%v", *v)
		}
		return ""
	case *bool:
		if v != nil {
			return fmt.Sprintf("%v", *v)
		}
		return ""
	case time.Time:
		if !v.IsZero() {
			return v.Format(constant.DateFormatDMY)
		}
		return ""
	case *time.Time:
		if v != nil && !v.IsZero() {
			return v.Format(constant.DateFormatDMY)
		}
		return ""
	default:
		return fmt.Sprintf("%v", value)
	}
}

func getExcelColumnName(n int) string {
	columnName := ""
	for n > 0 {
		n--
		columnName = string(rune('A'+(n%26))) + columnName
		n /= 26
	}
	return columnName
}

func (s *lotService) LotLineUploadExcel(file multipart.File, req model.BaseDtoActionBy) error {
	now := time.Now()

	readedFile, _ := excelize.OpenReader(file)
	rows, _ := readedFile.GetRows("Sheet1")

	lotLineEntityList := make([]entity.LotSouLotLine, 0, len(rows)-1)

	lotLineEntityList, err := util.MapExcelRowsToStruct[entity.LotSouLotLine](rows)
	if err != nil {
		log.Error(err)
		return err
	}

	compositeKeyDupMap := make(map[string]bool)
	auctionNoDupMap := make(map[string]bool)

	for i, e := range lotLineEntityList {

		if compositeKeyDupMap[util.Val(e.AssetCode)] {
			return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "Duplicate AssetCode", "")
		}
		if auctionNoDupMap[util.Val(e.Floor)+"-"+strconv.Itoa(util.Val(e.AuctionNo))] {
			return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "Duplicate Auction Order", "")
		}

		lotLineEntityList[i].LotId = req.Id
		lotLineEntityList[i].BaseEntity = &model.BaseEntity{
			CreatedBy:   req.ActionBy,
			UpdatedBy:   req.ActionBy,
			UpdatedDate: &now,
			CreatedDate: now,
		}

		compositeKeyDupMap[util.Val(e.AssetCode)] = true
		auctionNoDupMap[util.Val(e.Floor)+"-"+strconv.Itoa(util.Val(e.AuctionNo))] = true
	}

	lotSouLotLineDbs, err := s.LotSouLotLineRepo.GetAllByLotId(req.Id)
	if err != nil {
		return err
	}

	for _, e := range lotSouLotLineDbs {
		if compositeKeyDupMap[util.Val(e.AssetCode)] {
			return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "Duplicate AssetCode", "")
		}
		if auctionNoDupMap[util.Val(e.Floor)+"-"+strconv.Itoa(util.Val(e.AuctionNo))] {
			return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "Duplicate Auction Order", "")
		}

		compositeKeyDupMap[util.Val(e.AssetCode)] = true
		auctionNoDupMap[util.Val(e.Floor)+"-"+strconv.Itoa(util.Val(e.AuctionNo))] = true
	}

	if len(lotLineEntityList) > 0 {
		err := s.LotSouLotLineRepo.InsertList(lotLineEntityList)
		if err != nil {
			log.Error(err)
			return err
		}
	}

	return nil
}
