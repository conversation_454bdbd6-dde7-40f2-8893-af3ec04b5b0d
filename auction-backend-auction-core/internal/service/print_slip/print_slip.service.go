package service

import (
	"auction-core-service/internal/model/dto"
	printSlipRepository "auction-core-service/internal/repository/print_slip"
	"backend-common-lib/model"
)

type printSlipService struct {
	Repo printSlipRepository.PrintSlipRepository
}

type PrintSlipService interface {
	SearchPrintSlipFilter(req dto.PrintSlipPageReqDto) (model.PagingModel[dto.PrintSlipDto], error)
}

func NewPrintSlipService(repo printSlipRepository.PrintSlipRepository) PrintSlipService {
	return &printSlipService{Repo: repo}
}
