package service

import (
	"auction-core-service/internal/model/dto"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"net/http"
)

func (s *printSlipService) SearchPrintSlipFilter(req dto.PrintSlipPageReqDto) (model.PagingModel[dto.PrintSlipDto], error) {
	resp := model.PagingModel[dto.PrintSlipDto]{}

	result, err := s.Repo.FindPrintSlipWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountPrintSlipWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.PrintSlipDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.PrintSlipDto](v)
	}

	//NOTE - Set paging model
	resp = *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit)

	return resp, nil
}
