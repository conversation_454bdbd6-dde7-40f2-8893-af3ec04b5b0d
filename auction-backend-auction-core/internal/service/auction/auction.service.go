package service

import (
	"auction-core-service/internal/model/dto"
	auctionRepository "auction-core-service/internal/repository/auction"
	auctionAmountLimitRepository "auction-core-service/internal/repository/auction_amount_limit"
	auctionAssetRepository "auction-core-service/internal/repository/auction_asset"
	auctionBidStepRepository "auction-core-service/internal/repository/auction_bid_step"
	auctionFeeRepository "auction-core-service/internal/repository/auction_fee"
	auctionMinimumPaymentRepository "auction-core-service/internal/repository/auction_minimum_payment"
	commonAuctionCoreRepository "auction-core-service/internal/repository/common_auction_core"
)

type auctionService struct {
	Repo                      auctionRepository.AuctionRepository
	AuctionAssetRepo          auctionAssetRepository.AuctionAssetRepository
	AuctionAmountLimitRepo    auctionAmountLimitRepository.AuctionAmountLimitRepository
	AuctionBidStepRepo        auctionBidStepRepository.AuctionBidStepRepository
	AuctionFeeRepo            auctionFeeRepository.AuctionFeeRepository
	AuctionMinimumPaymentRepo auctionMinimumPaymentRepository.AuctionMinimumPaymentRepository
	CommonAuctionCoreRepo     commonAuctionCoreRepository.CommonAuctionCoreRepository
}

type AuctionService interface {
	GetAuctionById(id int) (*dto.AuctionRespInternalServiceDto, error)
	SearchAuctionFilter(req dto.AuctionPageReqDto) (dto.AuctionPageRespDto[dto.AuctionSearchRespDto], error)
	CreateAuction(req dto.AuctionSettingReqDto) error
	UpdateAuction(req dto.AuctionSettingReqDto) error
	UpdateAuctionStatus(req dto.AuctionReqDto) error
	DeleteAuction(id int, actionBy *int) error
}

func NewAuctionService(repo auctionRepository.AuctionRepository,
	auctionAssetRepo auctionAssetRepository.AuctionAssetRepository,
	auctionAmountLimitRepo auctionAmountLimitRepository.AuctionAmountLimitRepository,
	auctionBidStepRepo auctionBidStepRepository.AuctionBidStepRepository,
	auctionFeeRepo auctionFeeRepository.AuctionFeeRepository,
	auctionMinimumPaymentRepo auctionMinimumPaymentRepository.AuctionMinimumPaymentRepository,
	commonAuctionCoreRepo commonAuctionCoreRepository.CommonAuctionCoreRepository) AuctionService {
	return &auctionService{Repo: repo,
		AuctionAssetRepo:          auctionAssetRepo,
		AuctionAmountLimitRepo:    auctionAmountLimitRepo,
		AuctionBidStepRepo:        auctionBidStepRepo,
		AuctionFeeRepo:            auctionFeeRepo,
		AuctionMinimumPaymentRepo: auctionMinimumPaymentRepo,
		CommonAuctionCoreRepo:     commonAuctionCoreRepo}
}
