package service

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	auctionRepository "auction-core-service/internal/repository/auction"
	auctionAmountLimitRepository "auction-core-service/internal/repository/auction_amount_limit"
	auctionAssetRepository "auction-core-service/internal/repository/auction_asset"
	auctionBidStepRepository "auction-core-service/internal/repository/auction_bid_step"
	auctionFeeRepository "auction-core-service/internal/repository/auction_fee"
	auctionMinimumPaymentRepository "auction-core-service/internal/repository/auction_minimum_payment"
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"net/http"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/log"
	"gorm.io/gorm"
)

func (s *auctionService) GetAuctionById(id int) (*dto.AuctionRespInternalServiceDto, error) {

	layout := constant.DateFormatDMY

	auctionDb, err := s.Repo.GetById(id)
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	auctionAssetDbs, err := s.AuctionAssetRepo.GetAllByAuctionId(id)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	auctionAmountLimitDbs, err := s.AuctionAmountLimitRepo.GetAllByAuctionId(id)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	bidStepDbs, err := s.AuctionBidStepRepo.GetAllByAuctionId(id)
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	feeDbs, err := s.AuctionFeeRepo.GetAllByAuctionId(id)
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	minimumPaymentDbs, err := s.AuctionMinimumPaymentRepo.GetAllByAuctionId(id)
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	resultCustomerGroups, err := s.CommonAuctionCoreRepo.GetAllCustomerGroups()
	if err != nil {
		log.Error(err)
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	mapAuctTypeData2Id := map[string]entity.MasterData{}
	mapAuctTypeData2Id["1"] = entity.MasterData{
		DescriptionTh: "ทั่วไป",
		DescriptionEn: "normal",
	}
	mapAuctTypeData2Id["2"] = entity.MasterData{
		DescriptionTh: "พิเศษ",
		DescriptionEn: "special",
	}

	tempMapAuctionAmountLimitListByCGId := map[string][]*dto.AuctionAmountLimit{}
	tempMapAuctionAmountLimitList := map[int][]*dto.AuctionAmountLimit{}
	tempMapAuctionBidStepList := map[int][]*dto.AuctionBidStep{}
	tempMapAuctionFeeList := map[int][]*dto.AuctionFee{}
	tempMapAuctionMinimumPaymentList := map[int][]*dto.AuctionMinimumPayment{}
	tempMapSubAsset := map[string][]*dto.AuctionSubAssetType{}
	tempMapMainAsset := []*dto.AuctionAssetType{}

	if auctionAmountLimitDbs != nil {
		tempHaveValue := []dto.AuctionAmountLimit{}
		tempNotHaveValue := []dto.AuctionAmountLimit{}
		for _, m := range auctionAmountLimitDbs {
			temp := dto.AuctionAmountLimit{
				Id:                      m.Id,
				AuctionId:               m.AuctionId,
				AuctionAssetId:          m.AuctionAssetId,
				CustomerGroupId:         m.CustomerGroupId,
				CustomerGroupName:       &m.CustomerGroup.DescriptionTh,
				DepositAmountTypeCode:   m.DepositAmountTypeCode,
				DepositAmount:           m.DepositAmount,
				CreditAmount:            m.CreditAmount,
				AdditionalAmountPercent: m.AdditionalAmountPercent,
				IsNoLimit:               m.IsNoLimit,
				ItemLimit:               m.ItemLimit,
				CollateralId:            m.CollateralId,
			}
			if temp.DepositAmount != nil || temp.CreditAmount != nil || temp.AdditionalAmountPercent != nil || temp.ItemLimit != nil || (temp.IsNoLimit != nil && *temp.IsNoLimit == true) {
				tempHaveValue = append(tempHaveValue, temp)
			} else {
				tempNotHaveValue = append(tempNotHaveValue, temp)
			}
		}
		for _, AuctionAmount := range tempHaveValue {
			tempMapAuctionAmountLimitList[AuctionAmount.AuctionAssetId] = append(tempMapAuctionAmountLimitList[AuctionAmount.AuctionAssetId], &AuctionAmount)
		}
		for _, AuctionAmount := range tempNotHaveValue {
			tempMapAuctionAmountLimitList[AuctionAmount.AuctionAssetId] = append(tempMapAuctionAmountLimitList[AuctionAmount.AuctionAssetId], &AuctionAmount)
		}
	}

	if bidStepDbs != nil {
		for _, m := range bidStepDbs {
			temp := dto.AuctionBidStep{
				Id:             m.Id,
				AuctionId:      m.AuctionId,
				AuctionAssetId: m.AuctionAssetId,
				From:           m.From,
				To:             m.To,
				Amount:         m.Amount,
			}
			tempMapAuctionBidStepList[m.AuctionAssetId] = append(tempMapAuctionBidStepList[m.AuctionAssetId], &temp)
		}
	}

	if feeDbs != nil {
		for _, m := range feeDbs {
			temp := dto.AuctionFee{
				Id:                m.Id,
				AuctionId:         m.AuctionId,
				AuctionAssetId:    m.AuctionAssetId,
				VendorGroupId:     m.VendorGroupId,
				RegisterTypeId:    m.RegisterTypeId,
				SalesPrice:        m.SalesPrice,
				SoldAmount:        m.SoldAmount,
				CcFrom:            m.CcFrom,
				CcTo:              m.CcTo,
				RegisterTypeCarId: m.RegisterTypeCarId,
				RegisteredYear:    m.RegisteredYear,
				Fee:               m.Fee,
			}
			if m.StartDate != nil {
				startDate := m.StartDate.Format(layout)
				temp.StartDate = &startDate
			}
			if m.EndDate != nil {
				endDate := m.EndDate.Format(layout)
				temp.EndDate = &endDate
			}
			tempMapAuctionFeeList[m.AuctionAssetId] = append(tempMapAuctionFeeList[m.AuctionAssetId], &temp)
		}
	}

	if minimumPaymentDbs != nil {
		for _, m := range minimumPaymentDbs {
			temp := dto.AuctionMinimumPayment{
				Id:                 m.Id,
				AuctionId:          m.AuctionId,
				AuctionAssetId:     m.AuctionAssetId,
				VendorId:           m.VendorId,
				MinimumPaymentType: m.MinimumPaymentType,
				MinimumPercent:     m.MinimumPercent,
				MinimumAmount:      m.MinimumAmount,
			}
			tempMapAuctionMinimumPaymentList[m.AuctionAssetId] = append(tempMapAuctionMinimumPaymentList[m.AuctionAssetId], &temp)
		}
	}

	if auctionAssetDbs != nil {
		for _, m := range auctionAssetDbs {
			if m.AssetGroupId != nil && *m.AssetGroupId != 0 {
				// Sub Asset (Asset Group)
				ag := m.AssetGroupForJoin
				temp := dto.AuctionSubAssetType{
					Id:                        m.Id,
					AuctionId:                 m.AuctionId,
					AssetTypeId:               m.AssetTypeId,
					AssetGroupId:              m.AssetGroupId,
					AssetName:                 &ag.DescriptionTh,
					AssetTypeCode:             &ag.AssetTypeCode,
					AssetGroupCode:            &ag.AssetGroupCode,
					IsDeposit:                 m.IsDeposit,
					IsCredit:                  m.IsCredit,
					IsAdditional:              m.IsAdditional,
					IsItemLimit:               m.IsItemLimit,
					IncrementalUnit:           m.IncrementalUnit,
					ReferenceIncrement:        m.ReferenceIncrement,
					AuctionAmountLimitList:    tempMapAuctionAmountLimitList[m.Id],
					AuctionBidStepList:        tempMapAuctionBidStepList[m.Id],
					AuctionFeeList:            tempMapAuctionFeeList[m.Id],
					AuctionMinimumPaymentList: tempMapAuctionMinimumPaymentList[m.Id],
				}

				for _, al := range temp.AuctionAmountLimitList {
					tempMapAuctionAmountLimitListByCGId[al.CustomerGroupId] = append(tempMapAuctionAmountLimitListByCGId[al.CustomerGroupId], al)
				}

				if resultCustomerGroups != nil {
					for _, cg := range resultCustomerGroups {
						if tempMapAuctionAmountLimitListByCGId[strconv.Itoa(cg.Id)] == nil {
							temp.AuctionAmountLimitList = append(temp.AuctionAmountLimitList, &dto.AuctionAmountLimit{
								AuctionId:         m.AuctionId,
								AuctionAssetId:    m.Id,
								CustomerGroupId:   strconv.Itoa(cg.Id),
								CustomerGroupName: cg.DescriptionTh,
								IsNoLimit:         nil,
							})
						}
					}
				}

				tempMapSubAsset[ag.AssetTypeCode] = append(tempMapSubAsset[ag.AssetTypeCode], &temp)
			}
		}
		tempMapAuctionAmountLimitListByCGId = map[string][]*dto.AuctionAmountLimit{}
		for _, m := range auctionAssetDbs {
			// Main Asset (Asset Type)
			if m.AssetGroupId == nil || *m.AssetGroupId == 0 {
				at := m.AssetTypeForJoin
				temp := dto.AuctionAssetType{
					Id:                        m.Id,
					AuctionId:                 m.AuctionId,
					AssetTypeId:               m.AssetTypeId,
					AssetName:                 &at.DescriptionTh,
					AssetTypeCode:             &at.AssetTypeCode,
					IsDeposit:                 m.IsDeposit,
					IsCredit:                  m.IsCredit,
					IsAdditional:              m.IsAdditional,
					IsItemLimit:               m.IsItemLimit,
					IncrementalUnit:           m.IncrementalUnit,
					ReferenceIncrement:        m.ReferenceIncrement,
					AuctionAmountLimitList:    tempMapAuctionAmountLimitList[m.Id],
					AuctionBidStepList:        tempMapAuctionBidStepList[m.Id],
					AuctionFeeList:            tempMapAuctionFeeList[m.Id],
					AuctionMinimumPaymentList: tempMapAuctionMinimumPaymentList[m.Id],
					AuctionSubAssetTypeList:   tempMapSubAsset[at.AssetTypeCode],
				}

				for _, al := range temp.AuctionAmountLimitList {
					tempMapAuctionAmountLimitListByCGId[al.CustomerGroupId] = append(tempMapAuctionAmountLimitListByCGId[al.CustomerGroupId], al)
				}

				if resultCustomerGroups != nil {
					for _, cg := range resultCustomerGroups {
						if tempMapAuctionAmountLimitListByCGId[strconv.Itoa(cg.Id)] == nil {
							temp.AuctionAmountLimitList = append(temp.AuctionAmountLimitList, &dto.AuctionAmountLimit{
								AuctionId:         m.AuctionId,
								AuctionAssetId:    m.Id,
								CustomerGroupId:   strconv.Itoa(cg.Id),
								CustomerGroupName: cg.DescriptionTh,
								IsNoLimit:         nil,
							})
						}
					}
				}

				tempMapMainAsset = append(tempMapMainAsset, &temp)
			}
		}
	}

	createdBy := auctionDb.CreatedUser.FirstName + " " + auctionDb.CreatedUser.LastName
	updatedBy := auctionDb.UpdatedUser.FirstName + " " + auctionDb.UpdatedUser.LastName
	auctionTypeDescriptionTh := mapAuctTypeData2Id[util.Val(auctionDb.AuctionTypeId)].DescriptionTh
	auctionTypeDescriptionEn := mapAuctTypeData2Id[util.Val(auctionDb.AuctionTypeId)].DescriptionEn
	startDate := auctionDb.StartDate.Format(layout)
	endDate := auctionDb.EndDate.Format(layout)

	auctionRespDto := dto.AuctionRespInternalServiceDto{
		AuctionRespDto: dto.AuctionRespDto{Id: auctionDb.Id,
			CreatedDate:              &auctionDb.CreatedDate,
			CreatedBy:                &createdBy,
			UpdatedDate:              auctionDb.UpdatedDate,
			UpdatedBy:                &updatedBy,
			AuctionTypeCode:          auctionDb.AuctionTypeId,
			AuctionTypeDescriptionTh: &auctionTypeDescriptionTh,
			AuctionTypeDescriptionEn: &auctionTypeDescriptionEn,
			AuctionName:              auctionDb.AuctionName,
			Description:              auctionDb.Description,
			StartDate:                &startDate,
			EndDate:                  &endDate,
			IsActive:                 auctionDb.IsActive,
			EventId:                  auctionDb.EventId,
			IsAutoCollateralRefund:   auctionDb.IsAutoCollateralRefund,
			AuctionAssetTypeList:     tempMapMainAsset},
	}

	if auctionRespDto.UpdatedDate.IsZero() {
		auctionRespDto.UpdatedDate = auctionRespDto.CreatedDate
	}

	return &auctionRespDto, nil
}

func (s *auctionService) SearchAuctionFilter(req dto.AuctionPageReqDto) (dto.AuctionPageRespDto[dto.AuctionSearchRespDto], error) {
	result, err := s.Repo.FindAuctionWithFilter(req)
	if err != nil {
		log.Error(err)
		return dto.AuctionPageRespDto[dto.AuctionSearchRespDto]{}, errs.NewError(http.StatusInternalServerError, err)
	}

	count, err := s.Repo.CountAuctionWithFilter(req)
	if err != nil {
		return dto.AuctionPageRespDto[dto.AuctionSearchRespDto]{}, err
	}

	mapAuctTypeData2Id := map[string]entity.MasterData{}
	mapAuctTypeData2Id["1"] = entity.MasterData{
		DescriptionTh: "ทั่วไป",
		DescriptionEn: "normal",
	}
	mapAuctTypeData2Id["2"] = entity.MasterData{
		DescriptionTh: "พิเศษ",
		DescriptionEn: "special",
	}

	auctionSearchRespDto := []dto.AuctionSearchRespDto{}
	for _, v := range result {
		startDate := v.StartDate.Format(time.DateOnly)
		endDate := v.EndDate.Format(time.DateOnly)
		auctionTypeDescriptionTh := mapAuctTypeData2Id[util.Val(v.AuctionTypeId)].DescriptionTh
		auctionTypeDescriptionEn := mapAuctTypeData2Id[util.Val(v.AuctionTypeId)].DescriptionEn

		createdBy := v.CreatedUser.FirstName + " " + v.CreatedUser.LastName
		updatedBy := v.UpdatedUser.FirstName + " " + v.UpdatedUser.LastName
		auctionSearchRespDto = append(auctionSearchRespDto, dto.AuctionSearchRespDto{
			Id:                       v.Id,
			AuctionType:              v.AuctionTypeId,
			AuctionTypeDescriptionTh: &auctionTypeDescriptionTh,
			AuctionTypeDescriptionEn: &auctionTypeDescriptionEn,
			AuctionName:              v.AuctionName,
			Description:              v.Description,
			StartDate:                &startDate,
			EndDate:                  &endDate,
			CreatedDate:              &v.CreatedDate,
			UpdatedDate:              v.UpdatedDate,
			CreatedBy:                &createdBy,
			UpdatedBy:                &updatedBy,
			IsActive:                 v.IsActive,
		})
	}

	resp := dto.AuctionPageRespDto[dto.AuctionSearchRespDto]{
		PagingModel: *util.MapPaginationResult(auctionSearchRespDto, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
	}

	return resp, nil
}

func (s *auctionService) CreateAuction(req dto.AuctionSettingReqDto) error {
	now := util.Now()
	err := validateTab(req, now)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	auction := prepareCreateAuctionToDb(req, req.ActionBy, now)

	err = s.createAuctionToDb(auction)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return nil
}

func (s *auctionService) UpdateAuction(req dto.AuctionSettingReqDto) error {
	_, err := s.Repo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	now := time.Now()

	err = validateTab(req, now)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	auction, err := s.prepareUpdateAuctionToDb(req, req.ActionBy, now)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	err = s.updateAuctionToDb(auction)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return nil
}

func (s *auctionService) UpdateAuctionStatus(req dto.AuctionReqDto) error {
	_, err := s.Repo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	err = s.Repo.UpdateStatus(req.Id, fieldsToUpdate)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return nil
}

func (s *auctionService) DeleteAuction(id int, actionBy *int) error {
	var err error

	// add check if used later

	err = s.AuctionAmountLimitRepo.DeleteByAuctionId(id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	err = s.AuctionBidStepRepo.DeleteByAuctionId(id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	err = s.AuctionFeeRepo.DeleteByAuctionId(id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	err = s.AuctionMinimumPaymentRepo.DeleteByAuctionId(id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	err = s.AuctionAssetRepo.DeleteByAuctionId(id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	err = s.Repo.Delete(id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return nil
}

func prepareCreateAuctionToDb(req dto.AuctionSettingReqDto, actionBy *int, now time.Time) entity.Auction {

	layoutDMY := constant.DateFormatDMY

	startDate, _ := time.Parse(layoutDMY, util.Val(req.StartDate))
	endDate, _ := time.Parse(layoutDMY, util.Val(req.EndDate))

	entityAuct := entity.Auction{
		AuctionTypeId:          req.AuctionTypeCode,
		AuctionName:            req.AuctionName,
		Description:            req.Description,
		StartDate:              &startDate,
		EndDate:                &endDate,
		IsActive:               req.IsActive,
		EventId:                req.EventId,
		IsAutoCollateralRefund: req.IsAutoCollateralRefund,
		BaseEntity: &model.BaseEntity{
			CreatedDate: now,
			CreatedBy:   actionBy,
			UpdatedDate: &now,
			UpdatedBy:   actionBy,
		},
	}

	tempAssets := []entity.AuctionAsset{}
	tempAsset := entity.AuctionAsset{}
	tempSubAssets := []entity.AuctionAsset{}
	tempSubAsset := entity.AuctionAsset{}

	for _, asset := range req.Assets {
		tempAsset = entity.AuctionAsset{}
		//Insert  Auction Asset
		tempAsset = prepareCreateAuctionAssetToDb(asset, *actionBy, now)

		//Insert  Auction Amount Limit
		if len(asset.AmountLimits) > 0 {
			for _, amountLimit := range asset.AmountLimits {
				tempAsset.AmountLimits = append(tempAsset.AmountLimits, prepareCreateAuctionAmountLimitToDb(amountLimit, *actionBy, now))
			}
		}

		//Insert  Auction Bid Step
		if len(asset.BidSteps) > 0 {
			for _, bidstep := range asset.BidSteps {
				tempAsset.BidSteps = append(tempAsset.BidSteps, prepareCreateAuctionBidStepToDb(bidstep, *actionBy, now))
			}
		}

		//Insert  Auction Fee
		if len(asset.Fees) > 0 {
			for _, fee := range asset.Fees {
				tempAsset.Fees = append(tempAsset.Fees, prepareCreateAuctionFeeToDb(fee, *actionBy, now))
			}
		}

		//Insert  Auction Minimum Payment
		if len(asset.MinimumPayments) > 0 {
			for _, minimumPayment := range asset.MinimumPayments {
				tempAsset.MinimumPayments = append(tempAsset.MinimumPayments, prepareCreateAuctionMinimumPaymentToDb(minimumPayment, *actionBy, now))
			}
		}

		tempSubAssets = []entity.AuctionAsset{}
		for _, subAsset := range asset.SubAssets {
			tempSubAsset = entity.AuctionAsset{}

			//Insert  Auction Sub Asset
			tempSubAsset = prepareCreateAuctionAssetToDb(subAsset, *actionBy, now)

			//Insert  Auction Amount Limit
			if len(subAsset.AmountLimits) > 0 {
				for _, amountLimit := range subAsset.AmountLimits {
					tempSubAsset.AmountLimits = append(tempSubAsset.AmountLimits, prepareCreateAuctionAmountLimitToDb(amountLimit, *actionBy, now))
				}
			}

			//Insert  Auction Bid
			if len(subAsset.BidSteps) > 0 {
				for _, bidstep := range subAsset.BidSteps {
					tempSubAsset.BidSteps = append(tempSubAsset.BidSteps, prepareCreateAuctionBidStepToDb(bidstep, *actionBy, now))
				}
			}

			//Insert  Auction Fee
			if len(subAsset.Fees) > 0 {
				for _, fee := range subAsset.Fees {
					tempSubAsset.Fees = append(tempSubAsset.Fees, prepareCreateAuctionFeeToDb(fee, *actionBy, now))
				}
			}

			//Insert  Auction Minimum Payment
			if len(subAsset.MinimumPayments) > 0 {
				for _, minimumPayment := range subAsset.MinimumPayments {
					tempSubAsset.MinimumPayments = append(tempSubAsset.MinimumPayments, prepareCreateAuctionMinimumPaymentToDb(minimumPayment, *actionBy, now))
				}
			}

			tempSubAssets = append(tempSubAssets, tempSubAsset)
		}

		tempAsset.SubAssets = tempSubAssets

		tempAssets = append(tempAssets, tempAsset)
	}

	entityAuct.Assets = tempAssets

	return entityAuct

}

func prepareCreateAuctionAssetToDb(asset *dto.AuctionAssetReqDto, actionBy int, now time.Time) entity.AuctionAsset {
	entityAsset := entity.AuctionAsset{
		AssetTypeId:        asset.AssetTypeId,
		AssetGroupId:       asset.AssetGroupId,
		IsDeposit:          asset.IsDeposit,
		IsCredit:           asset.IsCredit,
		IsAdditional:       asset.IsAdditional,
		IsItemLimit:        asset.IsItemLimit,
		IsCollateral:       asset.IsCollateral,
		IncrementalUnit:    asset.IncrementalUnit,
		ReferenceIncrement: asset.ReferenceIncrement,
		BaseEntity: &model.BaseEntity{
			CreatedDate: now,
			CreatedBy:   &actionBy,
			UpdatedDate: &now,
			UpdatedBy:   &actionBy,
		},
	}

	return entityAsset
}

func prepareCreateAuctionAmountLimitToDb(amountLimit *dto.AuctionAmountLimit, actionBy int, now time.Time) entity.AuctionAmountLimit {

	entityAuctionAmountLimit := entity.AuctionAmountLimit{
		CustomerGroupId:         amountLimit.CustomerGroupId,
		DepositAmountTypeCode:   amountLimit.DepositAmountTypeCode,
		DepositAmount:           amountLimit.DepositAmount,
		CreditAmount:            amountLimit.CreditAmount,
		AdditionalAmountPercent: amountLimit.AdditionalAmountPercent,
		IsNoLimit:               amountLimit.IsNoLimit,
		ItemLimit:               amountLimit.ItemLimit,
		CollateralId:            amountLimit.CollateralId,
		BaseEntity: &model.BaseEntity{
			CreatedDate: now,
			CreatedBy:   &actionBy,
			UpdatedDate: &now,
			UpdatedBy:   &actionBy,
		},
	}

	return entityAuctionAmountLimit
}

func prepareCreateAuctionBidStepToDb(bidStep *dto.AuctionBidStep, actionBy int, now time.Time) entity.AuctionBidStep {

	entityAuctionBidStep := entity.AuctionBidStep{
		From:   bidStep.From,
		To:     bidStep.To,
		Amount: bidStep.Amount,
		BaseEntity: &model.BaseEntity{
			CreatedDate: now,
			CreatedBy:   &actionBy,
			UpdatedDate: &now,
			UpdatedBy:   &actionBy,
		},
	}

	return entityAuctionBidStep
}

func prepareCreateAuctionFeeToDb(fee *dto.AuctionFee, actionBy int, now time.Time) entity.AuctionFee {

	layoutDMY := constant.DateFormatDMY

	entityAuctionFee := entity.AuctionFee{
		VendorGroupId:     fee.VendorGroupId,
		RegisterTypeId:    fee.RegisterTypeId,
		SalesPrice:        fee.SalesPrice,
		SoldAmount:        fee.SoldAmount,
		CcFrom:            fee.CcFrom,
		CcTo:              fee.CcTo,
		RegisterTypeCarId: fee.RegisterTypeCarId,
		RegisteredYear:    fee.RegisteredYear,
		Fee:               fee.Fee,
		BaseEntity: &model.BaseEntity{
			CreatedDate: now,
			CreatedBy:   &actionBy,
			UpdatedDate: &now,
			UpdatedBy:   &actionBy,
		},
	}

	if fee.StartDate != nil {
		startDate, _ := time.Parse(layoutDMY, *fee.StartDate)
		entityAuctionFee.StartDate = &startDate
	}
	if fee.EndDate != nil {
		endDate, _ := time.Parse(layoutDMY, *fee.EndDate)
		entityAuctionFee.EndDate = &endDate
	}

	return entityAuctionFee
}

func prepareCreateAuctionMinimumPaymentToDb(minimumPayment *dto.AuctionMinimumPayment, actionBy int, now time.Time) entity.AuctionMinimumPayment {

	entityAuctionMinimumPayment := entity.AuctionMinimumPayment{
		VendorId:           minimumPayment.VendorId,
		MinimumPaymentType: minimumPayment.MinimumPaymentType,
		MinimumPercent:     minimumPayment.MinimumPercent,
		MinimumAmount:      minimumPayment.MinimumAmount,
		BaseEntity: &model.BaseEntity{
			CreatedDate: now,
			CreatedBy:   &actionBy,
			UpdatedDate: &now,
			UpdatedBy:   &actionBy,
		},
	}

	return entityAuctionMinimumPayment
}

func (s *auctionService) createAuctionToDb(auction entity.Auction) error {

	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		auctionRepo := auctionRepository.NewAuctionRepository(tx)
		auctionAssetRepo := auctionAssetRepository.NewAuctionAssetRepository(tx)
		auctionAmountLimitRepo := auctionAmountLimitRepository.NewAuctionAmountLimitRepository(tx)
		auctionBidStepRepo := auctionBidStepRepository.NewAuctionBidStepRepository(tx)
		auctionFeeRepo := auctionFeeRepository.NewAuctionFeeRepository(tx)
		auctionMinimumPaymentRepo := auctionMinimumPaymentRepository.NewAuctionMinimumPaymentRepository(tx)

		err := auctionRepo.Insert(auction)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		for _, asset := range auction.Assets {
			//Insert  Auction Asset
			asset.AuctionId = auction.Id
			err := auctionAssetRepo.Insert(asset)
			if err != nil {
				log.Error(err)
				return errs.NewError(http.StatusInternalServerError, err)
			}

			//Insert  Auction Amount Limit
			if len(asset.AmountLimits) > 0 {
				for _, amountLimit := range asset.AmountLimits {
					amountLimit.AuctionId = auction.Id
					amountLimit.AuctionAssetId = asset.Id
					err := auctionAmountLimitRepo.Insert(amountLimit)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}
			}

			//Insert  Auction Bid Step
			if len(asset.BidSteps) > 0 {
				for _, bidstep := range asset.BidSteps {
					bidstep.AuctionId = auction.Id
					bidstep.AuctionAssetId = asset.Id
					err := auctionBidStepRepo.Insert(bidstep)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}
			}

			//Insert  Auction Fee
			if len(asset.Fees) > 0 {
				for _, fee := range asset.Fees {
					fee.AuctionId = auction.Id
					fee.AuctionAssetId = asset.Id
					err := auctionFeeRepo.Insert(fee)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}
			}

			//Insert  Auction Minimum Payment
			if len(asset.MinimumPayments) > 0 {
				for _, minimumPayment := range asset.MinimumPayments {
					minimumPayment.AuctionId = auction.Id
					minimumPayment.AuctionAssetId = asset.Id
					err := auctionMinimumPaymentRepo.Insert(minimumPayment)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}
			}

			for _, subAsset := range asset.SubAssets {
				//Insert  Auction Sub Asset
				subAsset.AuctionId = auction.Id
				err := auctionAssetRepo.Insert(subAsset)
				if err != nil {
					log.Error(err)
					return errs.NewError(http.StatusInternalServerError, err)
				}

				//Insert  Auction Amount Limit
				if len(subAsset.AmountLimits) > 0 {
					for _, amountLimit := range subAsset.AmountLimits {
						amountLimit.AuctionId = auction.Id
						amountLimit.AuctionAssetId = subAsset.Id
						err := auctionAmountLimitRepo.Insert(amountLimit)
						if err != nil {
							log.Error(err)
							return errs.NewError(http.StatusInternalServerError, err)
						}
					}
				}

				//Insert  Auction Bid
				if len(subAsset.BidSteps) > 0 {
					for _, bidstep := range subAsset.BidSteps {
						bidstep.AuctionId = auction.Id
						bidstep.AuctionAssetId = subAsset.Id
						err := auctionBidStepRepo.Insert(bidstep)
						if err != nil {
							log.Error(err)
							return errs.NewError(http.StatusInternalServerError, err)
						}
					}
				}

				//Insert  Auction Fee
				if len(subAsset.Fees) > 0 {
					for _, fee := range subAsset.Fees {
						fee.AuctionId = auction.Id
						fee.AuctionAssetId = subAsset.Id
						err := auctionFeeRepo.Insert(fee)
						if err != nil {
							log.Error(err)
							return errs.NewError(http.StatusInternalServerError, err)
						}
					}
				}

				//Insert  Auction Minimum Payment
				if len(subAsset.MinimumPayments) > 0 {
					for _, minimumPayment := range subAsset.MinimumPayments {
						minimumPayment.AuctionId = auction.Id
						minimumPayment.AuctionAssetId = subAsset.Id
						err := auctionMinimumPaymentRepo.Insert(minimumPayment)
						if err != nil {
							log.Error(err)
							return errs.NewError(http.StatusInternalServerError, err)
						}
					}
				}
			}
		}

		return nil
	})
}

func (s *auctionService) prepareUpdateAuctionToDb(req dto.AuctionSettingReqDto, actionBy *int, now time.Time) (entity.Auction, error) {

	layoutDMY := constant.DateFormatDMY

	startDate, _ := time.Parse(layoutDMY, util.Val(req.StartDate))
	endDate, _ := time.Parse(layoutDMY, util.Val(req.EndDate))

	entityAuct, err := s.Repo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return entityAuct, err
	}

	entityAuct.UpdatedDate = &now
	entityAuct.UpdatedBy = actionBy
	entityAuct.AuctionTypeId = req.AuctionTypeCode
	entityAuct.AuctionName = req.AuctionName
	entityAuct.Description = req.Description
	entityAuct.IsActive = req.IsActive
	entityAuct.StartDate = &startDate
	entityAuct.EndDate = &endDate
	entityAuct.EventId = req.EventId
	entityAuct.IsAutoCollateralRefund = req.IsAutoCollateralRefund

	tempAssets := []entity.AuctionAsset{}
	tempAsset := entity.AuctionAsset{}
	tempSubAssets := []entity.AuctionAsset{}
	tempSubAsset := entity.AuctionAsset{}

	for _, asset := range req.Assets {
		if asset.Id != 0 {
			tempAsset = entity.AuctionAsset{}
			//Update  Auction Asset
			tempAsset = s.prepareUpdateAuctionAssetToDb(asset, *actionBy, now)

			if len(asset.AmountLimits) > 0 {
				for _, amountLimit := range asset.AmountLimits {
					if amountLimit.Id != 0 {
						//Update  Auction Amount Limit
						tempAsset.AmountLimits = append(tempAsset.AmountLimits, s.prepareUpdateAuctionAmountLimitToDb(amountLimit, *actionBy, now))

					} else {
						//Insert  Auction Amount Limit
						tempAsset.AmountLimits = append(tempAsset.AmountLimits, prepareCreateAuctionAmountLimitToDb(amountLimit, *actionBy, now))
					}
				}
			}

			if len(asset.BidSteps) > 0 {
				for _, bidStep := range asset.BidSteps {
					if bidStep.Id != 0 {
						//Update  Auction Bid Step
						tempAsset.BidSteps = append(tempAsset.BidSteps, s.prepareUpdateAuctionBidStepToDb(bidStep, *actionBy, now))
					} else {
						//Insert  Auction Bid Step
						tempAsset.BidSteps = append(tempAsset.BidSteps, prepareCreateAuctionBidStepToDb(bidStep, *actionBy, now))
					}
				}
			}

			if len(asset.Fees) > 0 {
				for _, fee := range asset.Fees {
					if fee.Id != 0 {
						//Update  Auction Fee
						tempAsset.Fees = append(tempAsset.Fees, s.prepareUpdateAuctionFeeToDb(fee, *actionBy, now))
					} else {
						//Insert  Auction Fee
						tempAsset.Fees = append(tempAsset.Fees, prepareCreateAuctionFeeToDb(fee, *actionBy, now))
					}
				}
			}

			if len(asset.MinimumPayments) > 0 {
				for _, minimumPayment := range asset.MinimumPayments {
					if minimumPayment.Id != 0 {
						//Update  Auction Minimum Payment
						tempAsset.MinimumPayments = append(tempAsset.MinimumPayments, s.prepareUpdateAuctionMinimumPaymentToDb(minimumPayment, *actionBy, now))
					} else {
						//Insert  Auction Minimum Payment
						tempAsset.MinimumPayments = append(tempAsset.MinimumPayments, prepareCreateAuctionMinimumPaymentToDb(minimumPayment, *actionBy, now))
					}
				}
			}

			tempSubAssets = []entity.AuctionAsset{}
			for _, subAsset := range asset.SubAssets {

				tempSubAsset = entity.AuctionAsset{}
				if subAsset.Id != 0 {
					//Update  Auction Sub Asset
					tempSubAsset = s.prepareUpdateAuctionAssetToDb(subAsset, *actionBy, now)

					for _, amountLimit := range subAsset.AmountLimits {
						if amountLimit.Id != 0 {
							//Update  Auction Amount Limit
							tempSubAsset.AmountLimits = append(tempSubAsset.AmountLimits, s.prepareUpdateAuctionAmountLimitToDb(amountLimit, *actionBy, now))
						} else {
							//Insert  Auction Amount Limit
							tempSubAsset.AmountLimits = append(tempSubAsset.AmountLimits, prepareCreateAuctionAmountLimitToDb(amountLimit, *actionBy, now))
						}
					}

					if len(subAsset.BidSteps) > 0 {
						for _, bidStep := range subAsset.BidSteps {
							if bidStep.Id != 0 {
								//Update  Auction Bid Step
								tempSubAsset.BidSteps = append(tempSubAsset.BidSteps, s.prepareUpdateAuctionBidStepToDb(bidStep, *actionBy, now))
							} else {
								//Insert  Auction Bid Step
								tempSubAsset.BidSteps = append(tempSubAsset.BidSteps, prepareCreateAuctionBidStepToDb(bidStep, *actionBy, now))
							}
						}
					}

					if len(subAsset.Fees) > 0 {
						for _, fee := range subAsset.Fees {
							if fee.Id != 0 {
								//Update  Auction Fee
								tempSubAsset.Fees = append(tempSubAsset.Fees, s.prepareUpdateAuctionFeeToDb(fee, *actionBy, now))
							} else {
								//Insert  Auction Fee
								tempSubAsset.Fees = append(tempSubAsset.Fees, prepareCreateAuctionFeeToDb(fee, *actionBy, now))
							}
						}
					}

					if len(subAsset.MinimumPayments) > 0 {
						for _, minimumPayment := range subAsset.MinimumPayments {
							if minimumPayment.Id != 0 {
								//Update  Auction Minimum Payment
								tempSubAsset.MinimumPayments = append(tempSubAsset.MinimumPayments, s.prepareUpdateAuctionMinimumPaymentToDb(minimumPayment, *actionBy, now))
							} else {
								//Insert  Auction Minimum Payment
								tempSubAsset.MinimumPayments = append(tempSubAsset.MinimumPayments, prepareCreateAuctionMinimumPaymentToDb(minimumPayment, *actionBy, now))
							}
						}
					}

				} else {

					//Insert  Auction Sub Asset
					tempSubAsset = prepareCreateAuctionAssetToDb(subAsset, *actionBy, now)

					//Insert  Auction Amount Limit
					if len(subAsset.AmountLimits) > 0 {
						for _, amountLimit := range subAsset.AmountLimits {
							tempSubAsset.AmountLimits = append(tempSubAsset.AmountLimits, prepareCreateAuctionAmountLimitToDb(amountLimit, *actionBy, now))
						}
					}

					//Insert  Auction Bid
					if len(subAsset.BidSteps) > 0 {
						for _, bidstep := range subAsset.BidSteps {
							tempSubAsset.BidSteps = append(tempSubAsset.BidSteps, prepareCreateAuctionBidStepToDb(bidstep, *actionBy, now))
						}
					}

					//Insert  Auction Fee
					if len(subAsset.Fees) > 0 {
						for _, fee := range subAsset.Fees {
							tempSubAsset.Fees = append(tempSubAsset.Fees, prepareCreateAuctionFeeToDb(fee, *actionBy, now))
						}
					}

					//Insert  Auction Minimum Payment
					if len(subAsset.MinimumPayments) > 0 {
						for _, minimumPayment := range subAsset.MinimumPayments {
							tempSubAsset.MinimumPayments = append(tempSubAsset.MinimumPayments, prepareCreateAuctionMinimumPaymentToDb(minimumPayment, *actionBy, now))
						}
					}

				}
				tempSubAssets = append(tempSubAssets, tempSubAsset)
			}

			tempAsset.SubAssets = tempSubAssets

			tempAssets = append(tempAssets, tempAsset)

		} else {
			tempAsset = entity.AuctionAsset{}
			//Insert  Auction Asset
			tempAsset = prepareCreateAuctionAssetToDb(asset, *actionBy, now)

			//Insert  Auction Amount Limit
			if len(asset.AmountLimits) > 0 {
				for _, amountLimit := range asset.AmountLimits {
					tempAsset.AmountLimits = append(tempAsset.AmountLimits, prepareCreateAuctionAmountLimitToDb(amountLimit, *actionBy, now))
				}
			}

			//Insert  Auction Bid Step
			if len(asset.BidSteps) > 0 {
				for _, bidstep := range asset.BidSteps {
					tempAsset.BidSteps = append(tempAsset.BidSteps, prepareCreateAuctionBidStepToDb(bidstep, *actionBy, now))
				}
			}

			//Insert  Auction Fee
			if len(asset.Fees) > 0 {
				for _, fee := range asset.Fees {
					tempAsset.Fees = append(tempAsset.Fees, prepareCreateAuctionFeeToDb(fee, *actionBy, now))
				}
			}

			//Insert  Auction Minimum Payment
			if len(asset.MinimumPayments) > 0 {
				for _, minimumPayment := range asset.MinimumPayments {
					tempAsset.MinimumPayments = append(tempAsset.MinimumPayments, prepareCreateAuctionMinimumPaymentToDb(minimumPayment, *actionBy, now))
				}
			}

			tempSubAssets = []entity.AuctionAsset{}
			for _, subAsset := range asset.SubAssets {
				tempSubAsset = entity.AuctionAsset{}

				//Insert  Auction Sub Asset
				tempSubAsset = prepareCreateAuctionAssetToDb(subAsset, *actionBy, now)

				//Insert  Auction Amount Limit
				if len(subAsset.AmountLimits) > 0 {
					for _, amountLimit := range subAsset.AmountLimits {
						tempSubAsset.AmountLimits = append(tempSubAsset.AmountLimits, prepareCreateAuctionAmountLimitToDb(amountLimit, *actionBy, now))
					}
				}

				//Insert  Auction Bid
				if len(subAsset.BidSteps) > 0 {
					for _, bidstep := range subAsset.BidSteps {
						tempSubAsset.BidSteps = append(tempSubAsset.BidSteps, prepareCreateAuctionBidStepToDb(bidstep, *actionBy, now))
					}
				}

				//Insert  Auction Fee
				if len(subAsset.Fees) > 0 {
					for _, fee := range subAsset.Fees {
						tempSubAsset.Fees = append(tempSubAsset.Fees, prepareCreateAuctionFeeToDb(fee, *actionBy, now))
					}
				}

				//Insert  Auction Minimum Payment
				if len(subAsset.MinimumPayments) > 0 {
					for _, minimumPayment := range subAsset.MinimumPayments {
						tempSubAsset.MinimumPayments = append(tempSubAsset.MinimumPayments, prepareCreateAuctionMinimumPaymentToDb(minimumPayment, *actionBy, now))
					}
				}

				tempSubAssets = append(tempSubAssets, tempSubAsset)
			}

			tempAsset.SubAssets = tempSubAssets

			tempAssets = append(tempAssets, tempAsset)
		}
	}

	entityAuct.Assets = tempAssets

	return entityAuct, nil
}

func (s *auctionService) prepareUpdateAuctionAssetToDb(asset *dto.AuctionAssetReqDto, actionBy int, now time.Time) entity.AuctionAsset {
	entityAsset, _ := s.AuctionAssetRepo.GetById(asset.Id)

	entityAsset.UpdatedDate = &now
	entityAsset.UpdatedBy = &actionBy
	entityAsset.AuctionId = asset.AuctionId
	entityAsset.AssetTypeId = asset.AssetTypeId
	entityAsset.AssetGroupId = asset.AssetGroupId
	entityAsset.IsDeposit = asset.IsDeposit
	entityAsset.IsCredit = asset.IsCredit
	entityAsset.IsAdditional = asset.IsAdditional
	entityAsset.IsItemLimit = asset.IsItemLimit
	entityAsset.IsCollateral = asset.IsCollateral
	entityAsset.IncrementalUnit = asset.IncrementalUnit
	entityAsset.ReferenceIncrement = asset.ReferenceIncrement

	return entityAsset
}

func (s *auctionService) prepareUpdateAuctionAmountLimitToDb(amountLimit *dto.AuctionAmountLimit, actionBy int, now time.Time) entity.AuctionAmountLimit {

	entityAuctionAmountLimit, _ := s.AuctionAmountLimitRepo.GetById(amountLimit.Id)

	entityAuctionAmountLimit.UpdatedDate = &now
	entityAuctionAmountLimit.UpdatedBy = &actionBy
	entityAuctionAmountLimit.AuctionId = amountLimit.AuctionId
	entityAuctionAmountLimit.AuctionAssetId = amountLimit.AuctionAssetId
	entityAuctionAmountLimit.CustomerGroupId = amountLimit.CustomerGroupId
	entityAuctionAmountLimit.DepositAmountTypeCode = amountLimit.DepositAmountTypeCode
	entityAuctionAmountLimit.DepositAmount = amountLimit.DepositAmount
	entityAuctionAmountLimit.CreditAmount = amountLimit.CreditAmount
	entityAuctionAmountLimit.AdditionalAmountPercent = amountLimit.AdditionalAmountPercent
	entityAuctionAmountLimit.IsNoLimit = amountLimit.IsNoLimit
	entityAuctionAmountLimit.ItemLimit = amountLimit.ItemLimit
	entityAuctionAmountLimit.CollateralId = amountLimit.CollateralId

	return entityAuctionAmountLimit
}

func (s *auctionService) prepareUpdateAuctionBidStepToDb(bidStep *dto.AuctionBidStep, actionBy int, now time.Time) entity.AuctionBidStep {

	entityAuctionBidStep, _ := s.AuctionBidStepRepo.GetById(bidStep.Id)

	entityAuctionBidStep.UpdatedDate = &now
	entityAuctionBidStep.UpdatedBy = &actionBy
	entityAuctionBidStep.AuctionId = bidStep.AuctionId
	entityAuctionBidStep.AuctionAssetId = bidStep.AuctionAssetId
	entityAuctionBidStep.From = bidStep.From
	entityAuctionBidStep.To = bidStep.To
	entityAuctionBidStep.Amount = bidStep.Amount

	return entityAuctionBidStep
}

func (s *auctionService) prepareUpdateAuctionFeeToDb(fee *dto.AuctionFee, actionBy int, now time.Time) entity.AuctionFee {

	layoutDMY := constant.DateFormatDMY

	entityAuctionFee, _ := s.AuctionFeeRepo.GetById(fee.Id)

	entityAuctionFee.UpdatedDate = &now
	entityAuctionFee.UpdatedBy = &actionBy
	entityAuctionFee.AuctionId = fee.AuctionId
	entityAuctionFee.AuctionAssetId = fee.AuctionAssetId
	entityAuctionFee.VendorGroupId = fee.VendorGroupId
	entityAuctionFee.RegisterTypeId = fee.RegisterTypeId
	entityAuctionFee.SalesPrice = fee.SalesPrice
	entityAuctionFee.SoldAmount = fee.SoldAmount
	entityAuctionFee.CcFrom = fee.CcFrom
	entityAuctionFee.CcTo = fee.CcTo
	entityAuctionFee.RegisterTypeCarId = fee.RegisterTypeCarId
	entityAuctionFee.RegisteredYear = fee.RegisteredYear
	entityAuctionFee.Fee = fee.Fee

	if fee.StartDate != nil {
		startDate, _ := time.Parse(layoutDMY, *fee.StartDate)
		entityAuctionFee.StartDate = &startDate
	}
	if fee.EndDate != nil {
		endDate, _ := time.Parse(layoutDMY, *fee.EndDate)
		entityAuctionFee.EndDate = &endDate
	}

	return entityAuctionFee
}

func (s *auctionService) prepareUpdateAuctionMinimumPaymentToDb(minimumPayment *dto.AuctionMinimumPayment, actionBy int, now time.Time) entity.AuctionMinimumPayment {

	entityAuctionMinimumPayment, _ := s.AuctionMinimumPaymentRepo.GetById(minimumPayment.Id)

	entityAuctionMinimumPayment.UpdatedDate = &now
	entityAuctionMinimumPayment.UpdatedBy = &actionBy
	entityAuctionMinimumPayment.AuctionId = minimumPayment.AuctionId
	entityAuctionMinimumPayment.AuctionAssetId = minimumPayment.AuctionAssetId
	entityAuctionMinimumPayment.VendorId = minimumPayment.VendorId
	entityAuctionMinimumPayment.MinimumPaymentType = minimumPayment.MinimumPaymentType
	entityAuctionMinimumPayment.MinimumPercent = minimumPayment.MinimumPercent
	entityAuctionMinimumPayment.MinimumAmount = minimumPayment.MinimumAmount

	return entityAuctionMinimumPayment
}

func (s *auctionService) updateAuctionToDb(auction entity.Auction) error {

	assetNotDeleteIds := map[int]int{}
	bidStepNotDeleteIds := map[int]int{}
	feeNotDeleteIds := map[int]int{}
	minimumPaymentNotDeleteIds := map[int]int{}

	assetDbs, err := s.AuctionAssetRepo.GetAllByAuctionId(auction.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	bidStepDbs, err := s.AuctionBidStepRepo.GetAllByAuctionId(auction.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	feeDbs, err := s.AuctionFeeRepo.GetAllByAuctionId(auction.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	minimumPaymentDbs, err := s.AuctionMinimumPaymentRepo.GetAllByAuctionId(auction.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		auctionRepo := auctionRepository.NewAuctionRepository(tx)
		auctionAssetRepo := auctionAssetRepository.NewAuctionAssetRepository(tx)
		auctionAmountLimitRepo := auctionAmountLimitRepository.NewAuctionAmountLimitRepository(tx)
		auctionBidStepRepo := auctionBidStepRepository.NewAuctionBidStepRepository(tx)
		auctionFeeRepo := auctionFeeRepository.NewAuctionFeeRepository(tx)
		auctionMinimumPaymentRepo := auctionMinimumPaymentRepository.NewAuctionMinimumPaymentRepository(tx)

		err := auctionRepo.UpdateAllFields(auction)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		for _, asset := range auction.Assets {
			if asset.Id != 0 {
				//Update  Auction Asset
				asset.AuctionId = auction.Id
				err := auctionAssetRepo.UpdateAllFields(asset)
				if err != nil {
					log.Error(err)
					return errs.NewError(http.StatusInternalServerError, err)
				}

				if len(asset.AmountLimits) > 0 {
					for _, amountLimit := range asset.AmountLimits {
						amountLimit.AuctionId = auction.Id
						amountLimit.AuctionAssetId = asset.Id
						if amountLimit.Id != 0 {
							//Update  Auction Amount Limit
							err := auctionAmountLimitRepo.UpdateAllFields(amountLimit)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
						} else {
							//Insert  Auction Amount Limit
							err := auctionAmountLimitRepo.Insert(amountLimit)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
						}
					}
				}

				if len(asset.BidSteps) > 0 {
					for _, bidStep := range asset.BidSteps {
						bidStep.AuctionId = auction.Id
						bidStep.AuctionAssetId = asset.Id
						if bidStep.Id != 0 {
							//Update  Auction Bid Step
							err := auctionBidStepRepo.UpdateAllFields(bidStep)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
						} else {
							//Insert  Auction Bid Step
							err := auctionBidStepRepo.Insert(bidStep)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
						}
						bidStepNotDeleteIds[bidStep.Id] = bidStep.Id
					}
				}

				if len(asset.Fees) > 0 {
					for _, fee := range asset.Fees {
						fee.AuctionId = auction.Id
						fee.AuctionAssetId = asset.Id
						if fee.Id != 0 {
							//Update  Auction Fee
							err := auctionFeeRepo.UpdateAllFields(fee)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
						} else {
							//Insert  Auction Fee
							err := auctionFeeRepo.Insert(fee)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
						}
						feeNotDeleteIds[fee.Id] = fee.Id
					}
				}

				if len(asset.MinimumPayments) > 0 {
					for _, minimumPayment := range asset.MinimumPayments {
						minimumPayment.AuctionId = auction.Id
						minimumPayment.AuctionAssetId = asset.Id
						if minimumPayment.Id != 0 {
							//Update  Auction Minimum Payment
							err := auctionMinimumPaymentRepo.UpdateAllFields(minimumPayment)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
						} else {
							//Insert  Auction Minimum Payment
							err := auctionMinimumPaymentRepo.Insert(minimumPayment)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
						}
						minimumPaymentNotDeleteIds[minimumPayment.Id] = minimumPayment.Id
					}
				}

				for _, subAsset := range asset.SubAssets {
					if subAsset.Id != 0 {
						subAsset.AuctionId = auction.Id
						//Update  Auction Sub Asset
						err := auctionAssetRepo.UpdateAllFields(subAsset)
						if err != nil {
							log.Error(err)
							return errs.NewError(http.StatusInternalServerError, err)
						}
						assetNotDeleteIds[subAsset.Id] = subAsset.Id

						if len(subAsset.AmountLimits) > 0 {
							for _, amountLimit := range subAsset.AmountLimits {
								amountLimit.AuctionId = auction.Id
								amountLimit.AuctionAssetId = subAsset.Id
								if amountLimit.Id != 0 {
									//Update  Auction Amount Limit
									err := auctionAmountLimitRepo.UpdateAllFields(amountLimit)
									if err != nil {
										log.Error(err)
										return errs.NewError(http.StatusInternalServerError, err)
									}
								} else {
									//Insert  Auction Amount Limit
									err := auctionAmountLimitRepo.Insert(amountLimit)
									if err != nil {
										log.Error(err)
										return errs.NewError(http.StatusInternalServerError, err)
									}
								}
							}
						}

						if len(subAsset.BidSteps) > 0 {
							for _, bidStep := range subAsset.BidSteps {
								bidStep.AuctionId = auction.Id
								bidStep.AuctionAssetId = subAsset.Id
								if bidStep.Id != 0 {
									//Update  Auction Bid Step
									err := auctionBidStepRepo.UpdateAllFields(bidStep)
									if err != nil {
										log.Error(err)
										return errs.NewError(http.StatusInternalServerError, err)
									}
								} else {
									//Insert  Auction Bid Step
									err := auctionBidStepRepo.Insert(bidStep)
									if err != nil {
										log.Error(err)
										return errs.NewError(http.StatusInternalServerError, err)
									}
								}
								bidStepNotDeleteIds[bidStep.Id] = bidStep.Id
							}
						}

						if len(subAsset.Fees) > 0 {
							for _, fee := range subAsset.Fees {
								fee.AuctionId = auction.Id
								fee.AuctionAssetId = subAsset.Id
								if fee.Id != 0 {
									//Update  Auction Fee
									err := auctionFeeRepo.UpdateAllFields(fee)
									if err != nil {
										log.Error(err)
										return errs.NewError(http.StatusInternalServerError, err)
									}
								} else {
									//Insert  Auction Fee
									err := auctionFeeRepo.Insert(fee)
									if err != nil {
										log.Error(err)
										return errs.NewError(http.StatusInternalServerError, err)
									}
								}
								feeNotDeleteIds[fee.Id] = fee.Id
							}
						}

						if len(subAsset.MinimumPayments) > 0 {
							for _, minimumPayment := range subAsset.MinimumPayments {
								minimumPayment.AuctionId = auction.Id
								minimumPayment.AuctionAssetId = subAsset.Id
								if minimumPayment.Id != 0 {
									//Update  Auction Minimum Payment
									err := auctionMinimumPaymentRepo.UpdateAllFields(minimumPayment)
									if err != nil {
										log.Error(err)
										return errs.NewError(http.StatusInternalServerError, err)
									}
								} else {
									//Insert  Auction Minimum Payment
									err := auctionMinimumPaymentRepo.Insert(minimumPayment)
									if err != nil {
										log.Error(err)
										return errs.NewError(http.StatusInternalServerError, err)
									}
								}
								minimumPaymentNotDeleteIds[minimumPayment.Id] = minimumPayment.Id
							}
						}

					} else {
						//Insert  Auction Sub Asset
						subAsset.AuctionId = auction.Id
						err := auctionAssetRepo.Insert(subAsset)
						if err != nil {
							log.Error(err)
							return errs.NewError(http.StatusInternalServerError, err)
						}
						assetNotDeleteIds[subAsset.Id] = subAsset.Id

						//Insert  Auction Amount Limit
						if len(subAsset.AmountLimits) > 0 {
							for _, amountLimit := range subAsset.AmountLimits {
								amountLimit.AuctionId = auction.Id
								amountLimit.AuctionAssetId = subAsset.Id
								err := auctionAmountLimitRepo.Insert(amountLimit)
								if err != nil {
									log.Error(err)
									return errs.NewError(http.StatusInternalServerError, err)
								}
							}
						}

						//Insert  Auction Bid
						if len(subAsset.BidSteps) > 0 {
							for _, bidstep := range subAsset.BidSteps {
								bidstep.AuctionId = auction.Id
								bidstep.AuctionAssetId = subAsset.Id
								err := auctionBidStepRepo.Insert(bidstep)
								if err != nil {
									log.Error(err)
									return errs.NewError(http.StatusInternalServerError, err)
								}
								bidStepNotDeleteIds[bidstep.Id] = bidstep.Id
							}
						}

						//Insert  Auction Fee
						if len(subAsset.Fees) > 0 {
							for _, fee := range subAsset.Fees {
								fee.AuctionId = auction.Id
								fee.AuctionAssetId = subAsset.Id
								err := auctionFeeRepo.Insert(fee)
								if err != nil {
									log.Error(err)
									return errs.NewError(http.StatusInternalServerError, err)
								}
								feeNotDeleteIds[fee.Id] = fee.Id
							}
						}

						//Insert  Auction Minimum Payment
						if len(subAsset.MinimumPayments) > 0 {
							for _, minimumPayment := range subAsset.MinimumPayments {
								minimumPayment.AuctionId = auction.Id
								minimumPayment.AuctionAssetId = subAsset.Id
								err := auctionMinimumPaymentRepo.Insert(minimumPayment)
								if err != nil {
									log.Error(err)
									return errs.NewError(http.StatusInternalServerError, err)
								}
								minimumPaymentNotDeleteIds[minimumPayment.Id] = minimumPayment.Id
							}
						}
					}
				}
			} else {
				//Insert  Auction Asset
				asset.AuctionId = auction.Id
				err := auctionAssetRepo.Insert(asset)
				if err != nil {
					log.Error(err)
					return errs.NewError(http.StatusInternalServerError, err)
				}
				assetNotDeleteIds[asset.Id] = asset.Id

				//Insert  Auction Amount Limit
				if len(asset.AmountLimits) > 0 {
					for _, amountLimit := range asset.AmountLimits {
						amountLimit.AuctionId = auction.Id
						amountLimit.AuctionAssetId = asset.Id
						err := auctionAmountLimitRepo.Insert(amountLimit)
						if err != nil {
							log.Error(err)
							return errs.NewError(http.StatusInternalServerError, err)
						}
					}
				}

				//Insert  Auction Bid Step
				if len(asset.BidSteps) > 0 {
					for _, bidstep := range asset.BidSteps {
						bidstep.AuctionId = auction.Id
						bidstep.AuctionAssetId = asset.Id
						err := auctionBidStepRepo.Insert(bidstep)
						if err != nil {
							log.Error(err)
							return errs.NewError(http.StatusInternalServerError, err)
						}
						bidStepNotDeleteIds[bidstep.Id] = bidstep.Id
					}
				}

				//Insert  Auction Fee
				if len(asset.Fees) > 0 {
					for _, fee := range asset.Fees {
						fee.AuctionId = auction.Id
						fee.AuctionAssetId = asset.Id
						err := auctionFeeRepo.Insert(fee)
						if err != nil {
							log.Error(err)
							return errs.NewError(http.StatusInternalServerError, err)
						}
						feeNotDeleteIds[fee.Id] = fee.Id
					}
				}

				//Insert  Auction Minimum Payment
				if len(asset.MinimumPayments) > 0 {
					for _, minimumPayment := range asset.MinimumPayments {
						minimumPayment.AuctionId = auction.Id
						minimumPayment.AuctionAssetId = asset.Id
						err := auctionMinimumPaymentRepo.Insert(minimumPayment)
						if err != nil {
							log.Error(err)
							return errs.NewError(http.StatusInternalServerError, err)
						}
						minimumPaymentNotDeleteIds[minimumPayment.Id] = minimumPayment.Id
					}
				}

				for _, subAsset := range asset.SubAssets {
					//Insert  Auction Sub Asset
					subAsset.AuctionId = auction.Id
					err := auctionAssetRepo.Insert(subAsset)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
					assetNotDeleteIds[subAsset.Id] = subAsset.Id

					//Insert  Auction Amount Limit
					if len(subAsset.AmountLimits) > 0 {
						for _, amountLimit := range subAsset.AmountLimits {
							amountLimit.AuctionId = auction.Id
							amountLimit.AuctionAssetId = subAsset.Id
							err := auctionAmountLimitRepo.Insert(amountLimit)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
						}
					}

					//Insert  Auction Bid
					if len(subAsset.BidSteps) > 0 {
						for _, bidstep := range subAsset.BidSteps {
							bidstep.AuctionId = auction.Id
							bidstep.AuctionAssetId = subAsset.Id
							err := auctionBidStepRepo.Insert(bidstep)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
							bidStepNotDeleteIds[bidstep.Id] = bidstep.Id
						}
					}

					//Insert  Auction Fee
					if len(subAsset.Fees) > 0 {
						for _, fee := range subAsset.Fees {
							fee.AuctionId = auction.Id
							fee.AuctionAssetId = subAsset.Id
							err := auctionFeeRepo.Insert(fee)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
							feeNotDeleteIds[fee.Id] = fee.Id
						}
					}

					//Insert  Auction Minimum Payment
					if len(subAsset.MinimumPayments) > 0 {
						for _, minimumPayment := range subAsset.MinimumPayments {
							minimumPayment.AuctionId = auction.Id
							minimumPayment.AuctionAssetId = subAsset.Id
							err := auctionMinimumPaymentRepo.Insert(minimumPayment)
							if err != nil {
								log.Error(err)
								return errs.NewError(http.StatusInternalServerError, err)
							}
							minimumPaymentNotDeleteIds[minimumPayment.Id] = minimumPayment.Id
						}
					}
				}
			}

			assetNotDeleteIds[asset.Id] = asset.Id
		}

		// delete
		if assetDbs != nil {
			for _, asset := range assetDbs {
				if assetNotDeleteIds[asset.Id] == 0 {
					err = auctionAssetRepo.Delete(asset.Id)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
					err = auctionAmountLimitRepo.DeleteByAuctionAssetId(asset.Id)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
					err = auctionBidStepRepo.DeleteByAuctionAssetId(asset.Id)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
					err = auctionFeeRepo.DeleteByAuctionAssetId(asset.Id)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
					err = auctionMinimumPaymentRepo.DeleteByAuctionAssetId(asset.Id)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}
			}
		}

		if bidStepDbs != nil {
			for _, bidStep := range bidStepDbs {
				if bidStepNotDeleteIds[bidStep.Id] == 0 {
					err = auctionBidStepRepo.Delete(bidStep.Id)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}
			}
		}

		if feeDbs != nil {
			for _, fee := range feeDbs {
				if feeNotDeleteIds[fee.Id] == 0 {
					err = auctionFeeRepo.DeleteByAuctionAssetId(fee.Id)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}
			}
		}

		if minimumPaymentDbs != nil {
			for _, minimumPayment := range minimumPaymentDbs {
				if minimumPaymentNotDeleteIds[minimumPayment.Id] == 0 {
					err = auctionMinimumPaymentRepo.DeleteByAuctionAssetId(minimumPayment.Id)
					if err != nil {
						log.Error(err)
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}
			}
		}

		return nil
	})
}

func validateTab(req dto.AuctionSettingReqDto, now time.Time) error {
	if util.Val(req.AuctionTypeCode) == "" {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "auctionTypeCode is required", "")
	}
	if util.Val(req.AuctionName) == "" {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "auctionName is required", "")
	}
	if util.Val(req.EventId) == 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "eventId is required", "")
	}
	if util.Val(req.StartDate) == "" {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "startDate is required", "")
	}

	layoutDMY := constant.DateFormatDMY
	startDate, _ := time.Parse(layoutDMY, util.Val(req.StartDate))
	endDate, _ := time.Parse(layoutDMY, util.Val(req.EndDate))

	if now.After(startDate) {
		return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "startDate", "")
	}

	if util.Val(req.EndDate) != "" {
		if startDate.After(endDate) {
			return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "startDate & endDate", "")
		}
		if now.After(endDate) {
			return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "endDate", "")
		}
	}

	for _, asset := range req.Assets {

		if asset.BidSteps != nil {
			for i, bidStep := range asset.BidSteps {
				if bidStep.To != nil && bidStep.From != nil {
					if *bidStep.From > *bidStep.To {
						return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "bidStep from can't morer than to", "")
					}
					if (i+1 < len(asset.BidSteps)) && (*bidStep.To > *asset.BidSteps[i+1].From) {
						return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "bidStep to can't morer than next bidStep from", "")
					}
				}
			}
		}

		if asset.Fees != nil {
			for _, fee := range asset.Fees {
				if fee.StartDate != nil && fee.EndDate != nil {
					startDate, _ := time.Parse(layoutDMY, *fee.StartDate)
					endDate, _ := time.Parse(layoutDMY, *fee.EndDate)
					if startDate.After(endDate) {
						return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "fee startDate & endDate", "")
					}
					if now.After(startDate) {
						return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "fee startDate", "")
					}
					if now.After(endDate) {
						return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "fee endDate", "")
					}
				} else {
					if fee.StartDate != nil {
						startDate, _ := time.Parse(layoutDMY, *fee.StartDate)
						if now.After(startDate) {
							return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "fee startDate", "")
						}
					}

					if fee.EndDate != nil {
						endDate, _ := time.Parse(layoutDMY, *fee.EndDate)
						if now.After(endDate) {
							return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "fee endDate", "")
						}
					}
				}
			}
		}

		if asset.SubAssets != nil {

			for _, subAssets := range asset.SubAssets {
				if subAssets.BidSteps != nil {
					for i, bidStep := range subAssets.BidSteps {
						if bidStep.To != nil && bidStep.From != nil {
							if *bidStep.From > *bidStep.To {
								return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "bidStep from can't morer than to", "")
							}
							if (i+1 < len(asset.BidSteps)) && (*bidStep.To > *asset.BidSteps[i+1].From) {
								return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "bidStep to can't morer than next bidStep from", "")
							}
						}
					}
				}

				if subAssets.Fees != nil {
					for _, fee := range subAssets.Fees {
						if fee.StartDate != nil && fee.EndDate != nil {
							startDate, _ := time.Parse(layoutDMY, *fee.StartDate)
							endDate, _ := time.Parse(layoutDMY, *fee.EndDate)
							if startDate.After(endDate) {
								return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "fee startDate & endDate", "")
							}
							if now.After(startDate) {
								return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "fee startDate", "")
							}
							if now.After(endDate) {
								return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "fee endDate", "")
							}
						} else {
							if fee.StartDate != nil {
								startDate, _ := time.Parse(layoutDMY, *fee.StartDate)
								if now.After(startDate) {
									return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "fee startDate", "")
								}
							}

							if fee.EndDate != nil {
								endDate, _ := time.Parse(layoutDMY, *fee.EndDate)
								if now.After(endDate) {
									return errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "fee endDate", "")
								}
							}
						}
					}
				}
			}
		}

	}

	return nil
}
