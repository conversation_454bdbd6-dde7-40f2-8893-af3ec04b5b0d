package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type auctionMinimumPaymentRepositoryImpl struct {
	DB *gorm.DB
}

type AuctionMinimumPaymentRepository interface {
	GetAll() ([]entity.AuctionMinimumPayment, error)
	GetById(id int) (entity.AuctionMinimumPayment, error)
	GetAllByAuctionId(auctionId int) ([]entity.AuctionMinimumPayment, error)
	Insert(auctionMinimumPayment entity.AuctionMinimumPayment) error
	UpdateAllFields(auctionMinimumPayment entity.AuctionMinimumPayment) error
	Delete(id int) error
	DeleteByAuctionAssetId(assetId int) error
	DeleteByAuctionId(auctionId int) error
}

func NewAuctionMinimumPaymentRepository(db *gorm.DB) AuctionMinimumPaymentRepository {
	return &auctionMinimumPaymentRepositoryImpl{DB: db}
}
