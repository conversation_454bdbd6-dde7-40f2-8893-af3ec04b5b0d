package repository

import (
	"auction-core-service/internal/model/entity"
	"fmt"
)

func (r *auctionMinimumPaymentRepositoryImpl) GetAll() ([]entity.AuctionMinimumPayment, error) {
	var auctionMinimumPayments []entity.AuctionMinimumPayment
	err := r.DB.Find(&auctionMinimumPayments).Error
	return auctionMinimumPayments, err
}

func (r *auctionMinimumPaymentRepositoryImpl) GetById(id int) (entity.AuctionMinimumPayment, error) {
	var auctionMinimumPayment entity.AuctionMinimumPayment
	err := r.DB.First(&auctionMinimumPayment, id).Error
	return auctionMinimumPayment, err
}

func (r *auctionMinimumPaymentRepositoryImpl) GetAllByAuctionId(auctionId int) ([]entity.AuctionMinimumPayment, error) {
	var auctionMinimumPayments []entity.AuctionMinimumPayment
	err := r.DB.Where("auction_id = ?", auctionId).Find(&auctionMinimumPayments).Error
	return auctionMinimumPayments, err
}

func (r *auctionMinimumPaymentRepositoryImpl) Insert(auctionMinimumPayment entity.AuctionMinimumPayment) error {
	err := r.DB.Create(&auctionMinimumPayment).Error
	return err
}

func (r *auctionMinimumPaymentRepositoryImpl) UpdateAllFields(auctionMinimumPayment entity.AuctionMinimumPayment) error {
	if err := r.DB.Save(auctionMinimumPayment).Error; err != nil {
		return err
	}
	return nil
}

func (r *auctionMinimumPaymentRepositoryImpl) Delete(id int) error {
	var existing entity.AuctionMinimumPayment
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("auction minimum payment with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("auction minimum payment with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.AuctionMinimumPayment{}).Error
	return err
}

func (r *auctionMinimumPaymentRepositoryImpl) DeleteByAuctionAssetId(assetId int) error {
	err := r.DB.Where("auction_asset_id = ?", assetId).Delete(&entity.AuctionMinimumPayment{}).Error
	return err
}

func (r *auctionMinimumPaymentRepositoryImpl) DeleteByAuctionId(auctionId int) error {
	err := r.DB.Where("auction_id = ?", auctionId).Delete(&entity.AuctionMinimumPayment{}).Error
	return err
}
