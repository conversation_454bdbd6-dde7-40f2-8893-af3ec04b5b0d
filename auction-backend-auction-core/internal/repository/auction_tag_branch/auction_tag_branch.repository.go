package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type auctionTagBranchRepositoryImpl struct {
	DB *gorm.DB
}

type AuctionTagBranchRepository interface {
	InsertAuctionTagBranches(tx *gorm.DB, entityAuctionTagBranches []entity.AuctionTagBranch) error
	GetBranchByAuctionTagIds(ids []int) (map[int][]entity.AuctionTagBranch, error)
	GetAuctionTagBranchesByAuctionTagIds(auctionIds []int) ([]entity.AuctionTagBranch, error)
	Delete(tx *gorm.DB, id int) error
	GetDB() *gorm.DB
}

func NewAuctionTagBranchRepository(db *gorm.DB) AuctionTagBranchRepository {
	return &auctionTagBranchRepositoryImpl{DB: db}
}
