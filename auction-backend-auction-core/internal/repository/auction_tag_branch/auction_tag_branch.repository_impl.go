package repository

import (
	"auction-core-service/internal/model/entity"
	"fmt"

	"gorm.io/gorm"
)

func (r *auctionTagBranchRepositoryImpl) InsertAuctionTagBranches(tx *gorm.DB, entityAuctionTagBranches []entity.AuctionTagBranch) error {
	if err := tx.Create(entityAuctionTagBranches).Error; err != nil {
		return err
	}
	return nil
}

func (r *auctionTagBranchRepositoryImpl) GetBranchByAuctionTagIds(ids []int) (map[int][]entity.AuctionTagBranch, error) {
	result := []entity.AuctionTagBranch{}

	if err := r.DB.Table("auction_tag_branch").
		Preload("Branch").
		Where("auction_tag_id IN ?", ids).Find(&result).Error; err != nil {
		return nil, err
	}

	branchesMap := make(map[int][]entity.AuctionTagBranch)
	for _, b := range result {
		branchesMap[*b.AuctionTagId] = append(branchesMap[*b.AuctionTagId], b)
	}

	return branchesMap, nil
}

func (r *auctionTagBranchRepositoryImpl) GetAuctionTagBranchesByAuctionTagIds(auctionIds []int) ([]entity.AuctionTagBranch, error) {
	var results []entity.AuctionTagBranch
	if err := r.DB.Where("auction_tag_id IN (?)", auctionIds).Order("branch_id ASC").Find(&results).Error; err != nil {
		fmt.Println("error ", err)
		return nil, err
	}

	return results, nil
}

func (r *auctionTagBranchRepositoryImpl) Delete(tx *gorm.DB, id int) error {
	var existing entity.AuctionTagBranch
	err := tx.Find(&existing, "auction_tag_id = ?", id).Error
	if err != nil {
		return fmt.Errorf("auction tag branch with id %d not found", id)
	}
	if existing.DeletedAt != nil {
		return fmt.Errorf("auction tag branch with id %d is already deleted", id)
	}

	err = tx.Model(&entity.AuctionTagBranch{}).Where("auction_tag_id = ?", id).Delete(&entity.AuctionTagBranch{}).Error

	return err
}

func (r *auctionTagBranchRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
