package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type lotAssetTypeRepositoryImpl struct {
	DB *gorm.DB
}

type LotAssetTypeRepository interface {
	GetById(id int) (entity.LotAssetType, error)
	GetAllByLotId(lotId int) ([]*entity.LotAssetType, error)
	Insert(lotAssetType entity.LotAssetType) error
	DeleteByLotId(lotId int) error
}

func NewLotAssetTypeRepository(db *gorm.DB) LotAssetTypeRepository {
	return &lotAssetTypeRepositoryImpl{DB: db}
}
