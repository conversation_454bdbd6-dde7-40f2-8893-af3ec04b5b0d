package repository

import (
	"auction-core-service/internal/model/entity"
)

func (r *lotAssetTypeRepositoryImpl) GetById(id int) (entity.LotAssetType, error) {
	var lotAssetType entity.LotAssetType
	err := r.DB.First(&lotAssetType, id).Error
	return lotAssetType, err
}

func (r *lotAssetTypeRepositoryImpl) GetAllByLotId(lotId int) ([]*entity.LotAssetType, error) {
	var lotAssetTypes []*entity.LotAssetType
	err := r.DB.Where("lot_id = ?", lotId).Find(&lotAssetTypes).Error
	return lotAssetTypes, err
}

func (r *lotAssetTypeRepositoryImpl) Insert(lotAssetType entity.LotAssetType) error {
	err := r.DB.Create(&lotAssetType).Error
	return err
}

func (r *lotAssetTypeRepositoryImpl) DeleteByLotId(lotId int) error {
	err := r.DB.Where("lot_id = ?", lotId).Delete(&entity.LotAssetType{}).Error
	return err
}
