package repository

import (
	"auction-core-service/internal/model/entity"
)

func (r *campaignCustomerGroupRepositoryImpl) GetAllByCampaignId(campaignId int) ([]entity.CampaignCustomerGroup, error) {
	var results []entity.CampaignCustomerGroup

	query := r.DB.Model(&entity.CampaignCustomerGroup{}).Preload("CustomerGroupForJoin")

	if err := query.Where("campaign_id = ?", campaignId).Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *campaignCustomerGroupRepositoryImpl) GetAllByCampaignIds(campaignIds []int) ([]entity.CampaignCustomerGroup, error) {
	var results []entity.CampaignCustomerGroup

	query := r.DB.Model(&entity.CampaignCustomerGroup{}).Preload("CustomerGroupForJoin")

	if err := query.Where("campaign_id IN ?", campaignIds).Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *campaignCustomerGroupRepositoryImpl) Insert(campaignCustomerGroup entity.CampaignCustomerGroup) error {
	err := r.DB.Create(&campaignCustomerGroup).Error
	return err
}

func (r *campaignCustomerGroupRepositoryImpl) InsertList(campaignCustomerGroups []entity.CampaignCustomerGroup) error {
	err := r.DB.Create(&campaignCustomerGroups).Error
	return err
}

func (r *campaignCustomerGroupRepositoryImpl) DeleteByCampaignId(campaignId int) error {
	err := r.DB.Where("campaign_id = ?", campaignId).Delete(&entity.CampaignCustomerGroup{}).Error
	return err
}
