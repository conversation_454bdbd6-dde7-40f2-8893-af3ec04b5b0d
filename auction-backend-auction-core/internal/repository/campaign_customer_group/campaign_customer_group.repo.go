package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type campaignCustomerGroupRepositoryImpl struct {
	DB *gorm.DB
}

type CampaignCustomerGroupRepository interface {
	GetAllByCampaignId(campaignId int) ([]entity.CampaignCustomerGroup, error)
	GetAllByCampaignIds(campaignIds []int) ([]entity.CampaignCustomerGroup, error)
	Insert(campaignCustomerGroup entity.CampaignCustomerGroup) error
	InsertList(campaignCustomerGroups []entity.CampaignCustomerGroup) error
	DeleteByCampaignId(campaignId int) error
}

func NewCampaignCustomerGroupRepository(db *gorm.DB) CampaignCustomerGroupRepository {
	return &campaignCustomerGroupRepositoryImpl{DB: db}
}
