package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type auctionBidStepRepositoryImpl struct {
	DB *gorm.DB
}

type AuctionBidStepRepository interface {
	GetAll() ([]entity.AuctionBidStep, error)
	GetById(id int) (entity.AuctionBidStep, error)
	GetAllByAuctionId(auctionId int) ([]entity.AuctionBidStep, error)
	Insert(auctionBidStep entity.AuctionBidStep) error
	UpdateAllFields(auctionBidStep entity.AuctionBidStep) error
	Delete(id int) error
	DeleteByAuctionAssetId(assetId int) error
	DeleteByAuctionId(auctionId int) error
}

func NewAuctionBidStepRepository(db *gorm.DB) AuctionBidStepRepository {
	return &auctionBidStepRepositoryImpl{DB: db}
}
