package repository

import (
	"auction-core-service/internal/model/entity"
	"fmt"
)

func (r *auctionBidStepRepositoryImpl) GetAll() ([]entity.AuctionBidStep, error) {
	var auctionBidSteps []entity.AuctionBidStep
	err := r.DB.Find(&auctionBidSteps).Error
	return auctionBidSteps, err
}

func (r *auctionBidStepRepositoryImpl) GetById(id int) (entity.AuctionBidStep, error) {
	var auctionBidStep entity.AuctionBidStep
	err := r.DB.First(&auctionBidStep, id).Error
	return auctionBidStep, err
}

func (r *auctionBidStepRepositoryImpl) GetAllByAuctionId(auctionId int) ([]entity.AuctionBidStep, error) {
	var auctionBidSteps []entity.AuctionBidStep
	err := r.DB.Where("auction_id = ?", auctionId).Find(&auctionBidSteps).Error
	return auctionBidSteps, err
}

func (r *auctionBidStepRepositoryImpl) Insert(auctionBidStep entity.AuctionBidStep) error {
	err := r.DB.Create(&auctionBidStep).Error
	return err
}

func (r *auctionBidStepRepositoryImpl) UpdateAllFields(auctionBidStep entity.AuctionBidStep) error {
	if err := r.DB.Save(auctionBidStep).Error; err != nil {
		return err
	}
	return nil
}

func (r *auctionBidStepRepositoryImpl) Delete(id int) error {
	var existing entity.AuctionBidStep
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("auction bid step with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("auction bid step with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.AuctionBidStep{}).Error
	return err
}

func (r *auctionBidStepRepositoryImpl) DeleteByAuctionAssetId(assetId int) error {
	err := r.DB.Where("auction_asset_id = ?", assetId).Delete(&entity.AuctionBidStep{}).Error
	return err
}

func (r *auctionBidStepRepositoryImpl) DeleteByAuctionId(auctionId int) error {
	err := r.DB.Where("auction_id = ?", auctionId).Delete(&entity.AuctionBidStep{}).Error
	return err
}
