package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type auctionAssetRepositoryImpl struct {
	DB *gorm.DB
}

type AuctionAssetRepository interface {
	GetAll() ([]entity.AuctionAsset, error)
	GetById(id int) (entity.AuctionAsset, error)
	GetAllByAuctionId(auctionId int) ([]*entity.AuctionAsset, error)
	Insert(auctionAsset entity.AuctionAsset) error
	UpdateAllFields(auctionAsset entity.AuctionAsset) error
	Delete(id int) error
	DeleteByAuctionId(auctionId int) error
	FindAssetTypesByAuctionId(auctionId int) ([]*entity.AuctionAsset, error)
}

func NewAuctionAssetRepository(db *gorm.DB) AuctionAssetRepository {
	return &auctionAssetRepositoryImpl{DB: db}
}
