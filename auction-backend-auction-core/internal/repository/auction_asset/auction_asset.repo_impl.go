package repository

import (
	"auction-core-service/internal/model/entity"
	"fmt"
)

func (r *auctionAssetRepositoryImpl) GetAll() ([]entity.AuctionAsset, error) {
	var auctionAssets []entity.AuctionAsset
	err := r.DB.Find(&auctionAssets).Error
	return auctionAssets, err
}

func (r *auctionAssetRepositoryImpl) GetById(id int) (entity.AuctionAsset, error) {
	var auctionAsset entity.AuctionAsset
	err := r.DB.First(&auctionAsset, id).Error
	return auctionAsset, err
}

func (r *auctionAssetRepositoryImpl) GetAllByAuctionId(auctionId int) ([]*entity.AuctionAsset, error) {
	var auctionAssets []*entity.AuctionAsset
	err := r.DB.Where("auction_id = ?", auctionId).Preload("AssetTypeForJoin").Preload("AssetGroupForJoin").Find(&auctionAssets).Error
	return auctionAssets, err
}

func (r *auctionAssetRepositoryImpl) FindAssetTypesByAuctionId(auctionId int) ([]*entity.AuctionAsset, error) {
	var results []*entity.AuctionAsset
	err := r.DB.Table("auction_asset AS aa").
		Select("DISTINCT aa.asset_type_id, mat.asset_type_code, mat.description_th, mat.description_en").
		Joins("JOIN master_asset_type mat ON aa.asset_type_id = mat.id").
		Where("aa.auction_id = ?", auctionId).
		Scan(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (r *auctionAssetRepositoryImpl) Insert(auctionAsset entity.AuctionAsset) error {
	err := r.DB.Create(&auctionAsset).Error
	return err
}

func (r *auctionAssetRepositoryImpl) UpdateAllFields(auctionAsset entity.AuctionAsset) error {
	if err := r.DB.Save(auctionAsset).Error; err != nil {
		return err
	}
	return nil
}

func (r *auctionAssetRepositoryImpl) Delete(id int) error {
	var existing entity.AuctionAsset
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("auction asset with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("auction asset with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.AuctionAsset{}).Error
	return err
}

func (r *auctionAssetRepositoryImpl) DeleteByAuctionId(auctionId int) error {
	err := r.DB.Where("auction_id = ?", auctionId).Delete(&entity.AuctionAsset{}).Error
	return err
}
