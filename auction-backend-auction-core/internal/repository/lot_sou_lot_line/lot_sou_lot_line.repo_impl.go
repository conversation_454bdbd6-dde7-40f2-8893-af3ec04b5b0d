package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/util"
	"fmt"
)

func (r *lotSouLotLineRepositoryImpl) GetById(id int) (entity.LotSouLotLine, error) {
	var lotSou entity.LotSouLotLine
	err := r.DB.First(&lotSou, id).Error
	return lotSou, err
}

func (r *lotSouLotLineRepositoryImpl) GetAllByLotId(lotId int) ([]*entity.LotSouLotLine, error) {
	var lotSous []*entity.LotSouLotLine
	err := r.DB.Where("lot_id = ?", lotId).Preload("MasterAssetType").Find(&lotSous).Error
	return lotSous, err
}

func (r *lotSouLotLineRepositoryImpl) GetAllByLotIds(lotIds []int) ([]*entity.LotSouLotLine, error) {
	var lotSous []*entity.LotSouLotLine
	err := r.DB.Where("lot_id IN ?", lotIds).Preload("MasterAssetType").Find(&lotSous).Error
	return lotSous, err
}

func (r *lotSouLotLineRepositoryImpl) GetAllBySouId(souId int) ([]entity.LotSouLotLine, error) {
	var lotSous []entity.LotSouLotLine
	err := r.DB.Where("lot_sou_id = ?", souId).Find(&lotSous).Error
	return lotSous, err
}

func (r *lotSouLotLineRepositoryImpl) FindSouLotLineWithFilter(req dto.SouLotLinePageReqDto) ([]entity.LotSouLotLine, error) {
	var results []entity.LotSouLotLine

	query := r.DB.Model(&entity.LotSouLotLine{}).Preload("MasterAssetType")

	if util.Val(req.Keyword) != "" {
		query = query.Where("brand_description ILIKE ? OR model_description ILIKE ? OR license_plate_no ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.Keyword)), fmt.Sprintf("%%%s%%", util.Val(req.Keyword)), fmt.Sprintf("%%%s%%", util.Val(req.Keyword)))
	}
	if util.Val(req.SellerName) != "" {
		query = query.Where("seller_name ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.SellerName)))
	}
	if util.Val(req.AssetType) != "" {
		query = query.Where("asset_type ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.AssetType)))
	}
	if util.Val(req.AuctionStatus) != "" {
		query = query.Where("auction_status ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.AuctionStatus)))
	}
	if req.IsShowRemark {
		query = query.Where("remark IS NOT NULL AND remark != '' ")
	}

	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Where("lot_id = ?", req.LotId).Order("floor asc, auction_no asc").Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *lotSouLotLineRepositoryImpl) CountSouLotLineWithFilter(req dto.SouLotLinePageReqDto) (int64, error) {
	var count int64

	query := r.DB.Model(&entity.LotSouLotLine{}).Preload("MasterAssetType")

	if util.Val(req.Keyword) != "" {
		query = query.Where("brand_description ILIKE ? OR model_description ILIKE ? OR license_plate_no ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.Keyword)), fmt.Sprintf("%%%s%%", util.Val(req.Keyword)), fmt.Sprintf("%%%s%%", util.Val(req.Keyword)))
	}
	if util.Val(req.SellerName) != "" {
		query = query.Where("seller_name ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.SellerName)))
	}
	if util.Val(req.AssetType) != "" {
		query = query.Where("asset_type ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.AssetType)))
	}
	if util.Val(req.AuctionStatus) != "" {
		query = query.Where("auction_status ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.AuctionStatus)))
	}
	if req.IsShowRemark {
		query = query.Where("remark IS NOT NULL AND remark != '' ")
	}

	query = query.Where("lot_id = ?", req.LotId)

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (r *lotSouLotLineRepositoryImpl) FindSouLotLineWithFilterNotPage(req dto.SouLotLinePageReqDto) ([]entity.LotSouLotLine, error) {
	var results []entity.LotSouLotLine

	query := r.DB.Model(&entity.LotSouLotLine{}).Preload("MasterAssetType")

	if util.Val(req.Keyword) != "" {
		query = query.Where("brand_description ILIKE ? OR model_description ILIKE ? OR license_plate_no ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.Keyword)), fmt.Sprintf("%%%s%%", util.Val(req.Keyword)), fmt.Sprintf("%%%s%%", util.Val(req.Keyword)))
	}
	if util.Val(req.SellerName) != "" {
		query = query.Where("seller_name ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.SellerName)))
	}
	if util.Val(req.AssetType) != "" {
		query = query.Where("asset_type ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.AssetType)))
	}
	if util.Val(req.AuctionStatus) != "" {
		query = query.Where("auction_status ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.AuctionStatus)))
	}
	if req.IsShowRemark {
		query = query.Where("remark IS NOT NULL AND remark != '' ")
	}

	query = query.Where("lot_id = ?", req.LotId).Order("floor asc, auction_no asc")

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *lotSouLotLineRepositoryImpl) FindLotLineProxyBidWithFilter(req dto.LotLineProxyBidSearchReqDto) ([]entity.LotSouLotLine, error) {
	var results []entity.LotSouLotLine

	query := r.DB.Model(&entity.LotSouLotLine{}).Preload("MasterAssetType")

	if util.Val(req.Keyword) != "" {
		query = query.Where("brand_description ILIKE ? OR model_description ILIKE ? OR license_plate_no ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.Keyword)), fmt.Sprintf("%%%s%%", util.Val(req.Keyword)), fmt.Sprintf("%%%s%%", util.Val(req.Keyword)))
	}
	if util.Val(req.Seq) != "" {
		query = query.Where("CONCAT(auct_code, auction_no) ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.Seq)))
	}
	if util.Val(req.VendorGroup) != "" {
		query = query.Where("vendor_group ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.VendorGroup)))
	}

	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Where("lot_id = ?", req.LotId).Order("floor asc, auction_no asc").Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *lotSouLotLineRepositoryImpl) CountLotLineProxyBidWithFilter(req dto.LotLineProxyBidSearchReqDto) (int64, error) {
	var count int64

	query := r.DB.Model(&entity.LotSouLotLine{}).Preload("MasterAssetType")

	if util.Val(req.Keyword) != "" {
		query = query.Where("brand_description ILIKE ? OR model_description ILIKE ? OR license_plate_no ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.Keyword)), fmt.Sprintf("%%%s%%", util.Val(req.Keyword)), fmt.Sprintf("%%%s%%", util.Val(req.Keyword)))
	}
	if util.Val(req.Seq) != "" {
		query = query.Where("CONCAT(auct_code, auction_no) ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.Seq)))
	}
	if util.Val(req.VendorGroup) != "" {
		query = query.Where("vendor_group ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.VendorGroup)))
	}

	query = query.Where("lot_id = ?", req.LotId)

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (r *lotSouLotLineRepositoryImpl) Insert(lotSouLotLine entity.LotSouLotLine) error {
	err := r.DB.Create(&lotSouLotLine).Error
	return err
}

func (r *lotSouLotLineRepositoryImpl) InsertList(lotSouLotLines []entity.LotSouLotLine) error {
	err := r.DB.Create(&lotSouLotLines).Error
	return err
}

func (r *lotSouLotLineRepositoryImpl) Update(lotSouLotLine entity.LotSouLotLine) error {
	err := r.DB.Model(&entity.LotSouLotLine{}).Where("id = ?", lotSouLotLine.Id).Updates(lotSouLotLine).Error

	return err
}

func (r *lotSouLotLineRepositoryImpl) DeleteBySouId(souId int) error {
	err := r.DB.Where("lot_sou_id = ?", souId).Delete(&entity.LotSouLotLine{}).Error
	return err
}

func (r *lotSouLotLineRepositoryImpl) DeleteByLotId(lotId int) error {
	err := r.DB.Where("lot_id = ?", lotId).Delete(&entity.LotSouLotLine{}).Error
	return err
}
