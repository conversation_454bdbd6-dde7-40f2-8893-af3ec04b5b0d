package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type lotSouLotLineRepositoryImpl struct {
	DB *gorm.DB
}

type LotSouLotLineRepository interface {
	GetById(id int) (entity.LotSouLotLine, error)
	GetAllByLotId(lotId int) ([]*entity.LotSouLotLine, error)
	GetAllByLotIds(lotIds []int) ([]*entity.LotSouLotLine, error)
	GetAllBySouId(souId int) ([]entity.LotSouLotLine, error)
	FindSouLotLineWithFilter(req dto.SouLotLinePageReqDto) ([]entity.LotSouLotLine, error)
	CountSouLotLineWithFilter(req dto.SouLotLinePageReqDto) (int64, error)
	FindSouLotLineWithFilterNotPage(req dto.SouLotLinePageReqDto) ([]entity.LotSouLotLine, error)
	FindLotLineProxyBidWithFilter(req dto.LotLineProxyBidSearchReqDto) ([]entity.LotSouLotLine, error)
	CountLotLineProxyBidWithFilter(req dto.LotLineProxyBidSearchReqDto) (int64, error)
	Insert(lotSouLotLine entity.LotSouLotLine) error
	InsertList(lotSouLotLines []entity.LotSouLotLine) error
	Update(lotSouLotLine entity.LotSouLotLine) error
	DeleteBySouId(souId int) error
	DeleteByLotId(lotId int) error
}

func NewLotSouLotLineRepository(db *gorm.DB) LotSouLotLineRepository {
	return &lotSouLotLineRepositoryImpl{DB: db}
}
