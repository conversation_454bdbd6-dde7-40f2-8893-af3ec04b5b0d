package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type printSlipRepositoryImpl struct {
	DB *gorm.DB
}

type PrintSlipRepository interface {
	FindPrintSlipWithFilter(req dto.PrintSlipPageReqDto) ([]entity.PrintSlip, error)
	CountPrintSlipWithFilter(req dto.PrintSlipPageReqDto) (int64, error)
}

func NewPrintSlipRepository(db *gorm.DB) PrintSlipRepository {
	return &printSlipRepositoryImpl{DB: db}
}
