package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/util"
	"fmt"

	"gorm.io/gorm"
)

func (r *printSlipRepositoryImpl) buildPrintSlipQuery(req dto.PrintSlipPageReqDto) *gorm.DB {
	query := r.DB.Table("lot_sou_lot_line").
		Select(`
			ps.*,
			lot_sou_lot_line.id AS lot_sou_lot_line_id,
			lot_sou_lot_line.floor || '-' || COALESCE(lot_sou_lot_line.auction_no::TEXT, '') AS no,
			lot_sou_lot_line.license_plate_no AS license_plate_no,
			lot_sou_lot_line.auction_tag AS auction_tag,
			lot_sou_lot_line.sale_date AS sale_date,
			lot_sou_lot_line.sales_price AS sales_price,
			lot_sou_lot_line.sold_amount AS sold_amount,
			lot_sou_lot_line.product_status AS product_status,
			lot_sou_lot_line.floor,
			mb.description_th AS branch_description_th,
			mb.description_en AS branch_description_en,
			lot_sou_lot_line.invoice_no AS invoice_no,
			lot_sou_lot_line.credit_no AS credit_no,
			lot_sou_lot_line.rvu AS rvu,
			created_user.first_name || ' ' || created_user.last_name AS created_user,
			updated_user.first_name || ' ' || updated_user.last_name AS updated_user
		`)

	query = query.
		Joins("LEFT JOIN print_slip ps  ON ps.lot_sou_lot_line_id = lot_sou_lot_line.id").
		Joins("LEFT JOIN master_branch mb ON mb.branch_code = lot_sou_lot_line.branch_code").
		Joins("LEFT JOIN employee created_user ON created_user.id = ps.created_by").
		Joins("LEFT JOIN employee updated_user ON updated_user.id = ps.updated_by")

	if util.Val(req.BranchCode) != "" {
		query = query.Where("lot_sou_lot_line.branch_code ILIKE ?", fmt.Sprintf("%s", util.Val(req.BranchCode)))
	}
	if util.Val(req.Floor) != "" {
		query = query.Where("lot_sou_lot_line.floor ILIKE ?", fmt.Sprintf("%s", util.Val(req.Floor)))
	}
	if util.Val(req.No) != "" {
		query = query.Where("CONCAT(lot_sou_lot_line.floor,'-', lot_sou_lot_line.auction_no) ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.No)))
	}
	if util.Val(req.LicensePlateNo) != "" {
		query = query.Where("lot_sou_lot_line.license_plate_no ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.LicensePlateNo)))
	}
	if util.Val(req.AuctionTag) != "" {
		query = query.Where("lot_sou_lot_line.auction_tag ILIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.AuctionTag)))
	}
	if req.SaleDate != nil {
		query = query.Where("lot_sou_lot_line.sale_date = ?", util.Val(req.SaleDate))
	}

	return query
}

func (r *printSlipRepositoryImpl) FindPrintSlipWithFilter(req dto.PrintSlipPageReqDto) ([]entity.PrintSlip, error) {
	var results []entity.PrintSlip

	query := r.buildPrintSlipQuery(req)
	sortingPrintSlip := map[string][]string{
		"no":                    {"lot_sou_lot_line.floor", "lot_sou_lot_line.auction_no"},
		"printed_date":          {"ps.printed_date"},
		"print_no":              {"ps.print_no"},
		"reason_reprint":        {"ps.reason_reprint"},
		"branch_description_th": {"mb.description_th"},
		"branch_description_en": {"mb.description_en"},
	}

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplySortFromMapField(query, snakeSort, req.SortOrder, "lot_sou_lot_line", sortingPrintSlip, false)
	}
	//NOTE - default order
	query.Order("ps.print_no asc nulls first, lot_sou_lot_line.sale_date desc, lot_sou_lot_line.id asc").Where("lot_sou_lot_line.product_status = 'Sold'")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *printSlipRepositoryImpl) CountPrintSlipWithFilter(req dto.PrintSlipPageReqDto) (int64, error) {
	var count int64
	query := r.buildPrintSlipQuery(req).Where("lot_sou_lot_line.product_status = 'Sold'")
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
