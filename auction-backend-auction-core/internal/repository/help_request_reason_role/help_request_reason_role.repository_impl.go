package repository

import (
	"auction-core-service/internal/model/entity"
)

func (r *helpRequestReasonRoleRepositoryImpl) GetAllByHelpRequestReasonId(helpRequestReasonId int) ([]*entity.HelpRequestReasonRole, error) {
	var helpRequestReasonRoles []*entity.HelpRequestReasonRole
	err := r.DB.Where("help_request_reason_id = ?", helpRequestReasonId).Preload("RoleForJoin").Find(&helpRequestReasonRoles).Error
	return helpRequestReasonRoles, err
}

func (r *helpRequestReasonRoleRepositoryImpl) GetAllByInHelpRequestReasonId(ids []int) ([]entity.HelpRequestReasonRole, error) {
	var helpRequestReasonRoles []entity.HelpRequestReasonRole
	err := r.DB.Where("help_request_reason_id IN ?", ids).Preload("RoleForJoin").Find(&helpRequestReasonRoles).Error
	return helpRequestReasonRoles, err
}

func (r *helpRequestReasonRoleRepositoryImpl) InsertList(helpRequestReasonRoles []entity.HelpRequestReasonRole) error {
	err := r.DB.Create(&helpRequestReasonRoles).Error
	return err
}

func (r *helpRequestReasonRoleRepositoryImpl) DeleteByHelpRequestReasonId(helpRequestReasonId int) error {
	err := r.DB.Where("help_request_reason_id = ?", helpRequestReasonId).Delete(&entity.HelpRequestReasonRole{}).Error
	return err
}
