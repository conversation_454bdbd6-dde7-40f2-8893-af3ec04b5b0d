package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type helpRequestReasonRoleRepositoryImpl struct {
	DB *gorm.DB
}

type HelpRequestReasonRoleRepository interface {
	GetAllByHelpRequestReasonId(helpRequestReasonId int) ([]*entity.HelpRequestReasonRole, error)
	GetAllByInHelpRequestReasonId(ids []int) ([]entity.HelpRequestReasonRole, error)
	InsertList(helpRequestReasonRoles []entity.HelpRequestReasonRole) error
	DeleteByHelpRequestReasonId(helpRequestReasonId int) error
}

func NewHelpRequestReasonRoleRepository(db *gorm.DB) HelpRequestReasonRoleRepository {
	return &helpRequestReasonRoleRepositoryImpl{DB: db}
}
