package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type campaignAssetGroupRepositoryImpl struct {
	DB *gorm.DB
}

type CampaignAssetGroupRepository interface {
	GetAllByCampaignId(campaignId int) ([]entity.CampaignAssetGroup, error)
	GetAllByCampaignIds(campaignIds []int) ([]entity.CampaignAssetGroup, error)
	Insert(campaignAssetGroup entity.CampaignAssetGroup) error
	InsertList(campaignAssetGroups []entity.CampaignAssetGroup) error
	DeleteByCampaignId(campaignId int) error
}

func NewCampaignAssetGroupRepository(db *gorm.DB) CampaignAssetGroupRepository {
	return &campaignAssetGroupRepositoryImpl{DB: db}
}
