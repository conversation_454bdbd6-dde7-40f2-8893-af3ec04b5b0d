package repository

import (
	"auction-core-service/internal/model/entity"
)

func (r *campaignAssetGroupRepositoryImpl) GetAllByCampaignId(campaignId int) ([]entity.CampaignAssetGroup, error) {
	var results []entity.CampaignAssetGroup

	query := r.DB.Model(&entity.CampaignAssetGroup{}).Preload("AssetGroupForJoin")

	if err := query.Where("campaign_id = ?", campaignId).Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *campaignAssetGroupRepositoryImpl) GetAllByCampaignIds(campaignIds []int) ([]entity.CampaignAssetGroup, error) {
	var results []entity.CampaignAssetGroup

	query := r.DB.Model(&entity.CampaignAssetGroup{}).Preload("AssetGroupForJoin")

	if err := query.Where("campaign_id IN ?", campaignIds).Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *campaignAssetGroupRepositoryImpl) Insert(campaignAssetGroup entity.CampaignAssetGroup) error {
	err := r.DB.Create(&campaignAssetGroup).Error
	return err
}

func (r *campaignAssetGroupRepositoryImpl) InsertList(campaignAssetGroups []entity.CampaignAssetGroup) error {
	err := r.DB.Create(&campaignAssetGroups).Error
	return err
}

func (r *campaignAssetGroupRepositoryImpl) DeleteByCampaignId(campaignId int) error {
	err := r.DB.Where("campaign_id = ?", campaignId).Delete(&entity.CampaignAssetGroup{}).Error
	return err
}
