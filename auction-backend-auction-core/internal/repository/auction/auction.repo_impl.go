package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/util"
	"fmt"

	"gorm.io/gorm"
)

func (r *auctionRepositoryImpl) GetAll() ([]entity.Auction, error) {
	var auctions []entity.Auction
	err := r.DB.Where("1 = ?", 1).Find(&auctions).Error
	return auctions, err
}

func (r *auctionRepositoryImpl) GetById(id int) (entity.Auction, error) {
	var auction entity.Auction
	err := r.DB.Preload("CreatedUser").
		Preload("UpdatedUser").
		Preload("Event").
		First(&auction, id).Error
	return auction, err
}

func (r *auctionRepositoryImpl) FindAuctionWithFilter(req dto.AuctionPageReqDto) ([]entity.Auction, error) {
	var results []entity.Auction

	query := r.DB.Model(&entity.Auction{})
	query = util.JoinUsers("auction")(query)

	if req.AuctionName != nil {
		query = query.Where("auction_name LIKE ?", fmt.Sprintf("%%%s%%", *req.AuctionName))
	}
	if req.AuctionTypeCode != nil {
		query = query.Where("auction_type_code = ?", req.AuctionTypeCode)
	}
	if req.StartDate != nil && req.EndDate == nil {
		query = query.Where("start_date >= ?", req.StartDate)
	}
	if req.StartDate == nil && req.EndDate != nil {
		query = query.Where("end_date <= ?", req.EndDate)
	}
	if req.StartDate != nil && req.EndDate != nil {
		query = query.Where("start_date BETWEEN ? AND ?", req.StartDate, req.EndDate)
	}

	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Order("id asc").Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *auctionRepositoryImpl) CountAuctionWithFilter(req dto.AuctionPageReqDto) (int64, error) {
	var count int64

	query := r.DB.Model(&entity.Auction{})
	query = util.JoinUsers("auction")(query)

	if req.AuctionName != nil {
		query = query.Where("auction_name LIKE ?", fmt.Sprintf("%%%s%%", *req.AuctionName))
	}
	if req.AuctionTypeCode != nil {
		query = query.Where("auction_type_code = ?", req.AuctionTypeCode)
	}
	if req.StartDate != nil && req.EndDate == nil {
		query = query.Where("start_date >= ?", req.StartDate)
	}
	if req.StartDate == nil && req.EndDate != nil {
		query = query.Where("end_date <= ?", req.EndDate)
	}
	if req.StartDate != nil && req.EndDate != nil {
		query = query.Where("start_date BETWEEN ? AND ?", req.StartDate, req.EndDate)
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (r *auctionRepositoryImpl) Insert(auction entity.Auction) error {
	err := r.DB.Create(&auction).Error
	return err
}

func (r *auctionRepositoryImpl) UpdateAllFields(auction entity.Auction) error {
	if err := r.DB.Save(auction).Error; err != nil {
		return err
	}
	return nil
}

func (r *auctionRepositoryImpl) UpdateStatus(auctionId int, fields map[string]interface{}) error {
	err := r.DB.Model(&entity.Auction{}).Where("id = ?", auctionId).Updates(fields).Error
	return err
}

func (r *auctionRepositoryImpl) Delete(id int) error {
	var existing entity.Auction
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("auction with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("auction with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.Auction{}).Error
	return err
}

func (r *auctionRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
