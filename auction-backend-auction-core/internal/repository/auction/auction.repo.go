package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type auctionRepositoryImpl struct {
	DB *gorm.DB
}

type AuctionRepository interface {
	GetAll() ([]entity.Auction, error)
	GetById(id int) (entity.Auction, error)
	FindAuctionWithFilter(req dto.AuctionPageReqDto) ([]entity.Auction, error)
	CountAuctionWithFilter(req dto.AuctionPageReqDto) (int64, error)
	Insert(auction entity.Auction) error
	UpdateAllFields(auction entity.Auction) error
	UpdateStatus(auctionId int, fields map[string]interface{}) error
	Delete(id int) error

	GetDB() *gorm.DB
}

func NewAuctionRepository(db *gorm.DB) AuctionRepository {
	return &auctionRepositoryImpl{DB: db}
}
