package repository

import (
	"auction-core-service/internal/model/entity"
	"fmt"
)

func (r *auctionFeeRepositoryImpl) GetAll() ([]entity.AuctionFee, error) {
	var auctionFees []entity.AuctionFee
	err := r.DB.Find(&auctionFees).Error
	return auctionFees, err
}

func (r *auctionFeeRepositoryImpl) GetById(id int) (entity.AuctionFee, error) {
	var auctionFee entity.AuctionFee
	err := r.DB.First(&auctionFee, id).Error
	return auctionFee, err
}

func (r *auctionFeeRepositoryImpl) GetAllByAuctionId(auctionId int) ([]entity.AuctionFee, error) {
	var auctionFees []entity.AuctionFee
	err := r.DB.Where("auction_id = ?", auctionId).Find(&auctionFees).Error
	return auctionFees, err
}

func (r *auctionFeeRepositoryImpl) Insert(auctionFee entity.AuctionFee) error {
	err := r.DB.Create(&auctionFee).Error
	return err
}

func (r *auctionFeeRepositoryImpl) UpdateAllFields(auctionFee entity.AuctionFee) error {
	if err := r.DB.Save(auctionFee).Error; err != nil {
		return err
	}
	return nil
}

func (r *auctionFeeRepositoryImpl) Delete(id int) error {
	var existing entity.AuctionFee
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("auction fee with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("auction fee with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.AuctionFee{}).Error
	return err
}

func (r *auctionFeeRepositoryImpl) DeleteByAuctionAssetId(assetId int) error {
	err := r.DB.Where("auction_asset_id = ?", assetId).Delete(&entity.AuctionFee{}).Error
	return err
}

func (r *auctionFeeRepositoryImpl) DeleteByAuctionId(auctionId int) error {
	err := r.DB.Where("auction_id = ?", auctionId).Delete(&entity.AuctionFee{}).Error
	return err
}
