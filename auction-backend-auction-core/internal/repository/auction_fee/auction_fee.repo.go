package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type auctionFeeRepositoryImpl struct {
	DB *gorm.DB
}

type AuctionFeeRepository interface {
	GetAll() ([]entity.AuctionFee, error)
	GetById(id int) (entity.AuctionFee, error)
	GetAllByAuctionId(auctionId int) ([]entity.AuctionFee, error)
	Insert(auctionFee entity.AuctionFee) error
	UpdateAllFields(auctionFee entity.AuctionFee) error
	Delete(id int) error
	DeleteByAuctionAssetId(assetId int) error
	DeleteByAuctionId(auctionId int) error
}

func NewAuctionFeeRepository(db *gorm.DB) AuctionFeeRepository {
	return &auctionFeeRepositoryImpl{DB: db}
}
