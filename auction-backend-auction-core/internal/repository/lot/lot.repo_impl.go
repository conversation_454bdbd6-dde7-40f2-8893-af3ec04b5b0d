package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/util"
	"fmt"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (r *lotRepositoryImpl) GetById(id int) (entity.Lot, error) {
	var lot entity.Lot
	err := r.DB.First(&lot, id).Error
	return lot, err
}

func (r *lotRepositoryImpl) FindLotWithFilter(req dto.LotSearchReqDto) ([]entity.Lot, error) {
	var results []entity.Lot
	var startDateTime, endDateTime time.Time
	layoutDMY := constant.DateFormatDMY
	layoutDMYTime := constant.DateTimeFormatDMY

	if req.StartDate != nil {
		if parsedTime, err := time.Parse(layoutDMYTime, *req.StartDate); err == nil {
			startDateTime = parsedTime
		} else if parsedDate, err := time.Parse(layoutDMY, *req.StartDate); err == nil {
			startDateTime = time.Date(parsedDate.Year(), parsedDate.Month(), parsedDate.Day(), 0, 0, 0, 0, parsedDate.Location())
		} else {
			return nil, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "Invalid start date format. Use DD/MM/YYYY or DD/MM/YYYY HH:MM", "")
		}
	}

	if req.EndDate != nil {
		if parsedTime, err := time.Parse(layoutDMYTime, *req.EndDate); err == nil {
			endDateTime = parsedTime
		} else if parsedDate, err := time.Parse(layoutDMY, *req.EndDate); err == nil {
			endDateTime = time.Date(parsedDate.Year(), parsedDate.Month(), parsedDate.Day(), 23, 59, 59, 999999999, parsedDate.Location())
		} else {
			return nil, errs.NewBusinessError(fiber.StatusBadRequest, constant.ValueInvalidate, "Invalid end date format. Use DD/MM/YYYY or DD/MM/YYYY HH:MM", "")
		}
	}

	query := r.DB.Model(&entity.Lot{}).Preload("Branch").Preload("Floor")

	if req.StartDate != nil && req.EndDate != nil {
		query = query.Where("(auction_date::timestamp + auction_time::time) BETWEEN ? AND ?", startDateTime, endDateTime)
	} else if req.StartDate != nil {
		query = query.Where("(auction_date::timestamp + auction_time::time) >= ?", startDateTime)
	} else if req.EndDate != nil {
		query = query.Where("(auction_date::timestamp + auction_time::time) <= ?", endDateTime)
	}

	if util.Val(req.BranchId) != 0 {
		query = query.Where("branch_id = ?", req.BranchId)
	}

	if util.Val(req.FloorStatus) != 0 {
		query = query.Where("floor_status = ?", req.FloorStatus)
	}

	if req.IsActive != nil {
		query = query.Where("is_active = ?", req.IsActive)
	}

	sortBy := "auction_date DESC, branch_id ASC, floor_id ASC"
	query = query.Order(sortBy)
	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *lotRepositoryImpl) FindLotProxyBidWithFilter(req dto.LotProxyBidSearchReqDto) ([]entity.Lot, error) {
	var results []entity.Lot

	query := r.DB.Model(&entity.Lot{}).Preload("Branch").Preload("Floor")

	if req.BranchId != nil {
		query = query.Where("branch_id = ?", req.BranchId)
	}

	if req.FloorId != nil {
		query = query.Where("floor_id = ?", req.FloorId)
	}

	sortBy := "auction_date DESC, branch_id ASC, floor_id ASC,lot.id ASC"
	query = query.Order(sortBy)
	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *lotRepositoryImpl) Insert(lot entity.Lot) error {
	err := r.DB.Create(&lot).Error
	return err
}

func (r *lotRepositoryImpl) UpdateAllFields(lot entity.Lot) error {
	if err := r.DB.Save(lot).Error; err != nil {
		return err
	}
	return nil
}

func (r *lotRepositoryImpl) Delete(id int) error {
	var existing entity.Lot
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("lot with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("lot with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.Lot{}).Error
	return err
}

func (r *lotRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
