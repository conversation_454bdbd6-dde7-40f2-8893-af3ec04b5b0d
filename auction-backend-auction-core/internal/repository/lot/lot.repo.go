package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type lotRepositoryImpl struct {
	DB *gorm.DB
}

type LotRepository interface {
	GetById(id int) (entity.Lot, error)
	FindLotWithFilter(req dto.LotSearchReqDto) ([]entity.Lot, error)
	FindLotProxyBidWithFilter(req dto.LotProxyBidSearchReqDto) ([]entity.Lot, error)
	Insert(lot entity.Lot) error
	UpdateAllFields(lot entity.Lot) error
	Delete(id int) error

	GetDB() *gorm.DB
}

func NewLotRepository(db *gorm.DB) LotRepository {
	return &lotRepositoryImpl{DB: db}
}
