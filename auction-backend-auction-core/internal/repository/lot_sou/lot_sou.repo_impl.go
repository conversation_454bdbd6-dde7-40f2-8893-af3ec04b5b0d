package repository

import (
	"auction-core-service/internal/model/entity"
	"fmt"
)

func (r *lotSouRepositoryImpl) GetById(id int) (entity.LotSou, error) {
	var lotSou entity.LotSou
	err := r.DB.First(&lotSou, id).Error
	return lotSou, err
}

func (r *lotSouRepositoryImpl) GetAllByLotId(lotId int) ([]entity.LotSou, error) {
	var lotSous []entity.LotSou
	err := r.DB.Where("lot_id = ?", lotId).Find(&lotSous).Error
	return lotSous, err
}

func (r *lotSouRepositoryImpl) GetByDocumentNo(documentNo string) (entity.LotSou, error) {
	var lotSou entity.LotSou
	err := r.DB.Where("document_no = ?", documentNo).First(&lotSou).Error
	return lotSou, err
}

func (r *lotSouRepositoryImpl) Insert(lotSou entity.LotSou) error {
	err := r.DB.Create(&lotSou).Error
	return err
}

func (r *lotSouRepositoryImpl) UpdateAllFields(lotSou entity.LotSou) error {
	if err := r.DB.Save(lotSou).Error; err != nil {
		return err
	}
	return nil
}

func (r *lotSouRepositoryImpl) Delete(id int) error {
	var existing entity.LotSou
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("lot sou with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("lot sou with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.LotSou{}).Error
	return err
}

func (r *lotSouRepositoryImpl) DeleteByLotId(lotId int) error {
	err := r.DB.Where("lot_id = ?", lotId).Delete(&entity.LotSou{}).Error
	return err
}
