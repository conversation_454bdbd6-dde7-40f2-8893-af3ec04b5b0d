package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type lotSouRepositoryImpl struct {
	DB *gorm.DB
}

type LotSouRepository interface {
	GetById(id int) (entity.LotSou, error)
	GetAllByLotId(lotId int) ([]entity.LotSou, error)
	GetByDocumentNo(documentNo string) (entity.LotSou, error)
	Insert(lotSou entity.LotSou) error
	UpdateAllFields(lotSou entity.LotSou) error
	Delete(id int) error
	DeleteByLotId(lotId int) error
}

func NewLotSouRepository(db *gorm.DB) LotSouRepository {
	return &lotSouRepositoryImpl{DB: db}
}
