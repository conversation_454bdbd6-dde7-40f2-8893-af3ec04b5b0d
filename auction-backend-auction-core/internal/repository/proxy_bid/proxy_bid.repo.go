package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type proxyBidRepositoryImpl struct {
	DB *gorm.DB
}

type ProxyBidRepository interface {
	GetAllByLotId(lotId int) ([]entity.ProxyBid, error)
	GetAllBySouLotLineId(souLotLineId int) ([]entity.ProxyBid, error)

	UpdateProxyBid(tx *gorm.DB, id int, fieldToUpdate map[string]interface{}) (int64, error)
}

func NewProxyBidRepository(db *gorm.DB) ProxyBidRepository {
	return &proxyBidRepositoryImpl{DB: db}
}
