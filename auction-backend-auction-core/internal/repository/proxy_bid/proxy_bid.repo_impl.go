package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *proxyBidRepositoryImpl) GetAllByLotId(lotId int) ([]entity.ProxyBid, error) {
	var proxyBids []entity.ProxyBid
	err := r.DB.Where("lot_id = ?", lotId).Find(&proxyBids).Error
	return proxyBids, err
}

func (r *proxyBidRepositoryImpl) GetAllBySouLotLineId(souLotLineId int) ([]entity.ProxyBid, error) {
	var proxyBids []entity.ProxyBid
	err := r.DB.Where("lot_sou_lot_line_id = ?", souLotLineId).Preload("Buyer").Find(&proxyBids).Error
	return proxyBids, err
}

func (r *proxyBidRepositoryImpl) UpdateProxyBid(tx *gorm.DB, id int, fieldToUpdate map[string]interface{}) (int64, error) {
	result := tx.Model(&entity.ProxyBid{}).
		Where("id = ?", id).
		Updates(fieldToUpdate)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}
