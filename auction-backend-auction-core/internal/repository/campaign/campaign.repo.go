package repository

import (
	"auction-core-service/internal/model/entity"
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type campaignRepositoryImpl struct {
	DB *gorm.DB
}

type CampaignRepository interface {
	FindCampaignWithFilter(req model.PagingRequest) ([]entity.Campaign, error)
	CountCampaignWithFilter(req model.PagingRequest) (int64, error)
	GetById(id int) (entity.Campaign, error)
	Insert(campaign entity.Campaign) error
	UpdateAllFields(campaign entity.Campaign) error
	UpdateStatus(campaignId int, fields map[string]interface{}) error
	Delete(id int) error

	GetDB() *gorm.DB
}

func NewCampaignRepository(db *gorm.DB) CampaignRepository {
	return &campaignRepositoryImpl{DB: db}
}
