package repository

import (
	"auction-core-service/internal/model/entity"
	"backend-common-lib/model"
	"fmt"

	"gorm.io/gorm"
)

func (r *campaignRepositoryImpl) FindCampaignWithFilter(req model.PagingRequest) ([]entity.Campaign, error) {
	var results []entity.Campaign

	query := r.DB.Model(&entity.Campaign{}).Preload("EventForJoin").Preload("VendorGroupForJoin")

	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Where("1 = ?", 1).Order("id asc").Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *campaignRepositoryImpl) CountCampaignWithFilter(req model.PagingRequest) (int64, error) {
	var count int64

	query := r.DB.Model(&entity.Campaign{})

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (r *campaignRepositoryImpl) GetById(id int) (entity.Campaign, error) {
	var campaign entity.Campaign
	err := r.DB.First(&campaign, id).Error
	return campaign, err
}

func (r *campaignRepositoryImpl) Insert(campaign entity.Campaign) error {
	err := r.DB.Create(&campaign).Error
	return err
}

func (r *campaignRepositoryImpl) UpdateAllFields(campaign entity.Campaign) error {
	if err := r.DB.Save(campaign).Error; err != nil {
		return err
	}
	return nil
}

func (r *campaignRepositoryImpl) UpdateStatus(campaignId int, fields map[string]interface{}) error {
	err := r.DB.Model(&entity.Campaign{}).Where("id = ?", campaignId).Updates(fields).Error
	return err
}

func (r *campaignRepositoryImpl) Delete(id int) error {
	var existing entity.Campaign
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("campaign with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("campaign with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.Campaign{}).Error
	return err
}

func (r *campaignRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
