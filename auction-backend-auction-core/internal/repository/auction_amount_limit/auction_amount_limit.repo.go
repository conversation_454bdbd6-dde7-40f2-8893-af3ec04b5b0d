package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type auctionAmountLimitRepositoryImpl struct {
	DB *gorm.DB
}

type AuctionAmountLimitRepository interface {
	GetAll() ([]entity.AuctionAmountLimit, error)
	GetById(id int) (entity.AuctionAmountLimit, error)
	GetAllByAuctionId(id int) ([]entity.AuctionAmountLimit, error)
	Insert(auctionAmountLimit entity.AuctionAmountLimit) error
	UpdateAllFields(auctionAmountLimit entity.AuctionAmountLimit) error
	Delete(id int) error
	DeleteByAuctionAssetId(assetId int) error
	DeleteByAuctionId(auctionId int) error
}

func NewAuctionAmountLimitRepository(db *gorm.DB) AuctionAmountLimitRepository {
	return &auctionAmountLimitRepositoryImpl{DB: db}
}
