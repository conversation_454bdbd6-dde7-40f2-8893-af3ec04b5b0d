package repository

import (
	"auction-core-service/internal/model/entity"
	"fmt"
)

func (r *auctionAmountLimitRepositoryImpl) GetAll() ([]entity.AuctionAmountLimit, error) {
	var auctionAmountLimits []entity.AuctionAmountLimit
	err := r.DB.Find(&auctionAmountLimits).Error
	return auctionAmountLimits, err
}

func (r *auctionAmountLimitRepositoryImpl) GetById(id int) (entity.AuctionAmountLimit, error) {
	var auctionAmountLimit entity.AuctionAmountLimit
	err := r.DB.First(&auctionAmountLimit, id).Error
	return auctionAmountLimit, err
}

func (r *auctionAmountLimitRepositoryImpl) GetAllByAuctionId(auctionId int) ([]entity.AuctionAmountLimit, error) {
	var auctionAmountLimits []entity.AuctionAmountLimit
	err := r.DB.Preload("CustomerGroup").Where("auction_id = ?", auctionId).Find(&auctionAmountLimits).Error
	return auctionAmountLimits, err
}

func (r *auctionAmountLimitRepositoryImpl) Insert(auctionAmountLimit entity.AuctionAmountLimit) error {
	err := r.DB.Create(&auctionAmountLimit).Error
	return err
}

func (r *auctionAmountLimitRepositoryImpl) UpdateAllFields(auctionAmountLimit entity.AuctionAmountLimit) error {
	if err := r.DB.Save(auctionAmountLimit).Error; err != nil {
		return err
	}
	return nil
}

func (r *auctionAmountLimitRepositoryImpl) Delete(id int) error {
	var existing entity.AuctionAmountLimit
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("auction amount limit with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("auction amount limit with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.AuctionAmountLimit{}).Error
	return err
}

func (r *auctionAmountLimitRepositoryImpl) DeleteByAuctionAssetId(assetId int) error {

	err := r.DB.Where("auction_asset_id = ?", assetId).Delete(&entity.AuctionAmountLimit{}).Error
	return err
}

func (r *auctionAmountLimitRepositoryImpl) DeleteByAuctionId(auctionId int) error {
	err := r.DB.Where("auction_id = ?", auctionId).Delete(&entity.AuctionAmountLimit{}).Error
	return err
}
