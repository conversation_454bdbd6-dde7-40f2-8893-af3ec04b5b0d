package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/util"

	"gorm.io/gorm"
)

func (r *videoAdsRepositoryImpl) buildVideoAdsQuery(req dto.VideoAdsPageReqDto) *gorm.DB {
	query := r.DB.Table("video_ads").
		Select(`
			video_ads.*,
			me.description_th AS event_description_th,
			me.description_en AS event_description_en,
			created_user.first_name || ' ' || created_user.last_name AS created_by,
			updated_user.first_name || ' ' || updated_user.last_name AS updated_by
		`).
		Joins("LEFT JOIN master_event me ON me.id = video_ads.event_id").
		Joins("LEFT JOIN employee created_user ON created_user.id = video_ads.created_by").
		Joins("LEFT JOIN employee updated_user ON updated_user.id = video_ads.updated_by")

	return query
}
func (r *videoAdsRepositoryImpl) FindAllVideoAds(req dto.VideoAdsPageReqDto) ([]dto.VideoAdsDto, error) {
	var results []dto.VideoAdsDto
	query := r.buildVideoAdsQuery(req)

	//NOTE - sorting
	sortingMap := map[string][]string{
		"event_description_th": {"me.description_th"},
		"event_description_en": {"me.description_en"},
		"start_date":           {"video_ads.start_date", "video_ads.end_date"},
	}
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplySortFromMapField(query, snakeSort, req.SortOrder, "video_ads", sortingMap, false)
	}
	query.Order("video_ads.video_name asc").Where("video_ads.deleted_date is null")

	if err := query.Scan(&results).Error; err != nil {
		return nil, err
	}
	return results, nil
}

func (r *videoAdsRepositoryImpl) FindByEventIdAndVideoName(eventId int, videoName string) (*entity.VideoAds, error) {
	var result entity.VideoAds
	err := r.DB.Where("event_id = ? AND video_name = ?", eventId, videoName).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}
func (r *videoAdsRepositoryImpl) FindDuplicateByEventIdAndVideoNameExcludingId(excludeID int, eventId int, videoName string) (*entity.VideoAds, error) {
	var result entity.VideoAds
	err := r.DB.Where("event_id = ? AND video_name = ? AND id != ?", eventId, videoName, excludeID).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *videoAdsRepositoryImpl) InsertVideoAds(data entity.VideoAds) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *videoAdsRepositoryImpl) UpdatesVideoAdsFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.VideoAds{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *videoAdsRepositoryImpl) FindVideoAdsById(id int) (dto.VideoAdsDto, error) {
	var result dto.VideoAdsDto
	query := r.buildVideoAdsQuery(dto.VideoAdsPageReqDto{})
	query = query.Where("video_ads.id = ?", id)

	if err := query.First(&result).Error; err != nil {
		return result, err
	}

	return result, nil
}

func (r *videoAdsRepositoryImpl) DeleteVideoAdsById(id int) (int64, error) {
	tx := r.DB.Where("id = ?", id).Delete(&entity.VideoAds{})
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}
