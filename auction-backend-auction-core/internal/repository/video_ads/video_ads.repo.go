package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type videoAdsRepositoryImpl struct {
	DB *gorm.DB
}

type VideoAdsRepository interface {
	FindAllVideoAds(req dto.VideoAdsPageReqDto) ([]dto.VideoAdsDto, error)
	FindByEventIdAndVideoName(eventId int, videoName string) (*entity.VideoAds, error)
	FindDuplicateByEventIdAndVideoNameExcludingId(excludeID int, eventId int, videoName string) (*entity.VideoAds, error)
	FindVideoAdsById(id int) (dto.VideoAdsDto, error)

	InsertVideoAds(data entity.VideoAds) error
	UpdatesVideoAdsFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	DeleteVideoAdsById(id int) (int64, error)
}

func NewVideoAdsRepository(db *gorm.DB) VideoAdsRepository {
	return &videoAdsRepositoryImpl{DB: db}
}
