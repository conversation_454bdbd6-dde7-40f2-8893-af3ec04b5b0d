package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type helpRequestRepositoryImpl struct {
	DB *gorm.DB
}

type HelpRequestRepository interface {
	FindHelpRequestWithFilter(req dto.HelpRequestPageReqDto) ([]entity.HelpRequest, error)
	CountHelpRequestWithFilter(req dto.HelpRequestPageReqDto) (int64, error)
	FindHelpRequestById(id int) (entity.HelpRequest, error)

	UpdatesHelpRequestFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
}

func NewHelpRequestRepository(db *gorm.DB) HelpRequestRepository {
	return &helpRequestRepositoryImpl{DB: db}
}
