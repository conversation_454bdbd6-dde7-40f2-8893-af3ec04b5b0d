package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/constant"
	"backend-common-lib/util"

	"gorm.io/gorm"
)

func (r *helpRequestRepositoryImpl) buildHelpRequestQuery(req dto.HelpRequestPageReqDto) *gorm.DB {
	query := r.DB.Table("help_request").
		Select(`
			help_request.*,
			lsl.floor || '-' || COALESCE(lsl.auction_no::TEXT, '') AS no,
			mb.description_th AS branch_description_th,
			mb.description_en AS branch_description_en,
			'ลาน ' || COALESCE(lsl.floor::TEXT, '') AS floor,
			hrs.reason AS help_request_reason,
			cfg.value_string2 AS status_help_request_th,
			cfg.value_string AS status_help_request_en,
			help_request.solution AS solution,
			help_request.created_date AS created_date,
			help_request.updated_date AS updated_date,
			help_request.solve_issue_date AS solve_issue_date,
			solve_issue_user.first_name || ' ' || solve_issue_user.last_name AS solve_issue_by,
			created_user.first_name || ' ' || created_user.last_name AS created_user,
			updated_user.first_name || ' ' || updated_user.last_name AS updated_user
		`)

	query = query.
		Joins("LEFT JOIN help_request_reason hrs ON hrs.id = help_request.help_request_reason_id").
		Joins("LEFT JOIN lot_sou_lot_line lsl ON help_request.lot_sou_lot_line_id = lsl.id").
		Joins(`LEFT JOIN config_parameters cfg ON 
				cfg.parameter_module = ? AND 
				cfg.parameter_name = ? AND 
				cfg.value_int = help_request.status_help_request_id`,
			constant.ConfigParamConst.MODULE_SYSTEM_ADMIN,
			constant.ConfigParamConst.DROPDOWN_HELP_REQUEST_STATUS,
		).
		Joins("LEFT JOIN master_branch mb ON mb.branch_code = lsl.branch_code").
		Joins("LEFT JOIN employee solve_issue_user ON solve_issue_user.id = help_request.solve_issue_by").
		Joins("LEFT JOIN employee created_user ON created_user.id = help_request.created_by").
		Joins("LEFT JOIN employee updated_user ON updated_user.id = help_request.updated_by")

	fieldMap := map[string]string{
		"AuctionDate":         "help_request.created_date",
		"BranchCode":          "lsl.branch_code",
		"StatusHelpRequestId": "help_request.status_help_request_id",
		"Floor":               "lsl.floor",
		"HelpRequestReasonId": "help_request.help_request_reason_id",
	}

	query = util.ApplyFiltersFromStruct(query, req, fieldMap)

	return query
}

func (r *helpRequestRepositoryImpl) FindHelpRequestWithFilter(req dto.HelpRequestPageReqDto) ([]entity.HelpRequest, error) {
	var results []entity.HelpRequest

	query := r.buildHelpRequestQuery(req)
	sortingHelpRequest := map[string][]string{
		"no":                     {"lsl.floor", "lsl.auction_no"},
		"branch_description_th":  {"mb.description_th"},
		"branch_description_en":  {"mb.description_en"},
		"floor":                  {"lsl.floor"},
		"help_request_reason":    {"hrs.reason"},
		"status_help_request_th": {"cfg.value_string2"},
		"status_help_request_en": {"cfg.value_string"},
		"solution":               {"help_request.solution"},
		"created_date":           {"help_request.created_date"},
		"created_by":             {"created_user.first_name", "created_user.last_name"},
		"solve_issue_by":         {"solve_issue_user.first_name", "solve_issue_user.last_name"},
		"solve_issue_date":       {"help_request.solve_issue_date"},
	}

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplySortFromMapField(query, snakeSort, req.SortOrder, "help_request", sortingHelpRequest, false)
	}
	//NOTE - default order
	query.Order("help_request.updated_date desc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *helpRequestRepositoryImpl) CountHelpRequestWithFilter(req dto.HelpRequestPageReqDto) (int64, error) {
	var count int64
	query := r.buildHelpRequestQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *helpRequestRepositoryImpl) FindHelpRequestById(id int) (entity.HelpRequest, error) {
	var result entity.HelpRequest
	query := r.buildHelpRequestQuery(dto.HelpRequestPageReqDto{})
	query = query.Where("help_request.id = ?", id)

	if err := query.First(&result).Error; err != nil {
		return result, err
	}
	return result, nil
}

func (r *helpRequestRepositoryImpl) UpdatesHelpRequestFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.HelpRequest{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}
