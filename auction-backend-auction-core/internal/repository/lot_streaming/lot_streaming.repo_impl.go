package repository

import (
	"auction-core-service/internal/model/entity"
	"fmt"
)

func (r *lotStreamingRepositoryImpl) GetById(id int) (entity.LotStreaming, error) {
	var lotStreaming entity.LotStreaming
	err := r.DB.First(&lotStreaming, id).Error
	return lotStreaming, err
}

func (r *lotStreamingRepositoryImpl) GetAllByLotId(lotId int) ([]entity.LotStreaming, error) {
	var lotStreamings []entity.LotStreaming
	err := r.DB.Where("lot_id = ?", lotId).Find(&lotStreamings).Error
	return lotStreamings, err
}

func (r *lotStreamingRepositoryImpl) Insert(lotStreaming entity.LotStreaming) error {
	err := r.DB.Create(&lotStreaming).Error
	return err
}

func (r *lotStreamingRepositoryImpl) UpdateAllFields(lotStreaming entity.LotStreaming) error {
	if err := r.DB.Save(lotStreaming).Error; err != nil {
		return err
	}
	return nil
}

func (r *lotStreamingRepositoryImpl) Delete(id int) error {
	var existing entity.LotStreaming
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("lot streaming with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("lot streaming with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.LotStreaming{}).Error
	return err
}

func (r *lotStreamingRepositoryImpl) DeleteByLotId(lotId int) error {
	err := r.DB.Where("lot_id = ?", lotId).Delete(&entity.LotStreaming{}).Error
	return err
}
