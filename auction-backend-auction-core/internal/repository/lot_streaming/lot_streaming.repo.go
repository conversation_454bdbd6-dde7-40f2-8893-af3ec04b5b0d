package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type lotStreamingRepositoryImpl struct {
	DB *gorm.DB
}

type LotStreamingRepository interface {
	GetById(id int) (entity.LotStreaming, error)
	GetAllByLotId(lotId int) ([]entity.LotStreaming, error)
	Insert(lotStreaming entity.LotStreaming) error
	UpdateAllFields(lotStreaming entity.LotStreaming) error
	Delete(id int) error
	DeleteByLotId(lotId int) error
}

func NewLotStreamingRepository(db *gorm.DB) LotStreamingRepository {
	return &lotStreamingRepositoryImpl{DB: db}
}
