package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type helpRequestReasonRepositoryImpl struct {
	DB *gorm.DB
}

type HelpRequestReasonRepository interface {
	HelpRequestReasonFilter(req dto.HelpRequestReasonPageReqDto) ([]entity.HelpRequestReason, error)
	CountHelpRequestReasonFilter(req dto.HelpRequestReasonPageReqDto) (int64, error)
	GetById(id int) (entity.HelpRequestReason, error)
	Insert(helpRequestReason entity.HelpRequestReason) error
	UpdateAllFields(helpRequestReason entity.HelpRequestReason) error
	UpdateStatus(helpRequestReasonId int, fields map[string]interface{}) error
	Delete(id int) error

	GetDB() *gorm.DB
}

func NewHelpRequestReasonRepository(db *gorm.DB) HelpRequestReasonRepository {
	return &helpRequestReasonRepositoryImpl{DB: db}
}
