package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/util"
	"fmt"

	"gorm.io/gorm"
)

func (r *helpRequestReasonRepositoryImpl) buildHelpRequestReasonQuery(req dto.HelpRequestReasonPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.HelpRequestReason{}).Preload("CreatedUser").Preload("UpdatedUser")
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "help_request_reason")
	}
	return query
}

func (r *helpRequestReasonRepositoryImpl) HelpRequestReasonFilter(req dto.HelpRequestReasonPageReqDto) ([]entity.HelpRequestReason, error) {
	var results []entity.HelpRequestReason

	query := r.buildHelpRequestReasonQuery(req)

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *helpRequestReasonRepositoryImpl) CountHelpRequestReasonFilter(req dto.HelpRequestReasonPageReqDto) (int64, error) {
	var count int64
	query := r.buildHelpRequestReasonQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *helpRequestReasonRepositoryImpl) GetById(id int) (entity.HelpRequestReason, error) {
	var helpRequestReason entity.HelpRequestReason
	err := r.DB.Preload("CreatedUser").
		Preload("UpdatedUser").
		First(&helpRequestReason, id).Error
	return helpRequestReason, err
}

func (r *helpRequestReasonRepositoryImpl) Insert(helpRequestReason entity.HelpRequestReason) error {
	err := r.DB.Create(&helpRequestReason).Error
	return err
}

func (r *helpRequestReasonRepositoryImpl) UpdateAllFields(helpRequestReason entity.HelpRequestReason) error {
	err := r.DB.Save(helpRequestReason).Error
	return err

}

func (r *helpRequestReasonRepositoryImpl) UpdateStatus(helpRequestReasonId int, fields map[string]interface{}) error {
	err := r.DB.Model(&entity.HelpRequestReason{}).Where("id = ?", helpRequestReasonId).Updates(fields).Error
	return err
}

func (r *helpRequestReasonRepositoryImpl) Delete(id int) error {
	var existing entity.HelpRequestReason
	err := r.DB.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("help request with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("help request with id %d is already deleted", id)
	}

	err = r.DB.Where("id = ?", id).Delete(&entity.HelpRequestReason{}).Error
	return err
}

func (r *helpRequestReasonRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
