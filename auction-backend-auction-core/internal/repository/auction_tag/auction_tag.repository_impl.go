package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/model"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

func (r *auctionTagRepositoryImpl) InsertAuctionTag(tx *gorm.DB, entityAuctionTag *entity.AuctionTag) error {
	if err := tx.Create(entityAuctionTag).Error; err != nil {
		return err
	}
	return nil
}
func (r *auctionTagRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}

func (r *auctionTagRepositoryImpl) FindAuctionTags() ([]entity.AuctionTag, error) {
	var results []entity.AuctionTag
	query := r.DB.Table("auction_tag").Where("auction_tag.is_active = ? and auction_tag.deleted_date IS NULL", true)
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (r *auctionTagRepositoryImpl) FindById(auctionTagId int) (*entity.AuctionTag, error) {

	var result entity.AuctionTag

	query := r.DB.Table("auction_tag").Where("auction_id = ? ", auctionTagId)

	if err := query.First(&result).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &result, nil

}

func (r *auctionTagRepositoryImpl) FindByIds(auctionTagId int) ([]entity.AuctionTag, error) {

	var results []entity.AuctionTag

	query := r.DB.Table("auction_tag").Where("id = ? ", auctionTagId)

	if err := query.Find(&results).Error; err != nil {
		return results, err
	}

	return results, nil

}

func (r *auctionTagRepositoryImpl) FindByIdsExcept(excludeId int) ([]entity.AuctionTag, error) {
	var results []entity.AuctionTag

	query := r.DB.Table("auction_tag").Where("id != ? ", excludeId)

	if err := query.Find(&results).Error; err != nil {
		return results, err
	}
	return results, nil
}

func (r *auctionTagRepositoryImpl) FindAuctionTagsPageSimple(req model.PagingRequest) ([]entity.AuctionTag, error) {
	var results []entity.AuctionTag

	// Default pagination
	if req.PageNumber <= 0 {
		req.PageNumber = 1
	}
	if req.PageLimit <= 0 {
		req.PageLimit = 10
	}

	sortOrder := strings.ToLower(req.SortOrder)
	if sortOrder != "desc" {
		sortOrder = "asc"
	}

	validSortColumns := map[string]string{
		"auction_name": "a.auction_name",
		"is_active":    "at.is_active",
	}
	sortByColumn, ok := validSortColumns[req.SortBy]
	if !ok {
		sortByColumn = "at.id"
	}

	err := r.DB.
		Table("auction_tag AS at").
		Select(`
	        at.id,
	        at.auction_id,
	        at.branch_ids,
	        at.is_active,
	        a.auction_name,
	        at.start_tag_number,
	        at.end_tag_number,
			at.created_by,
			at.created_date,
			at.updated_by,
			at.updated_date,
			created_user.first_name || ' ' || created_user.last_name AS created_user,
			updated_user.first_name || ' ' || updated_user.last_name AS updated_user
	    `).
		Joins("LEFT JOIN auction a ON a.id = at.auction_id").
		Joins("LEFT JOIN employee created_user ON created_user.id = at.created_by").
		Joins("LEFT JOIN employee updated_user ON updated_user.id = at.updated_by").
		Where("at.deleted_date IS NULL").
		Order(fmt.Sprintf("%s %s, at.id ASC", sortByColumn, sortOrder)).
		Limit(req.PageLimit).
		Offset((req.PageNumber - 1) * req.PageLimit).
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *auctionTagRepositoryImpl) FindAuctionTagsPageSimpleById(actionTagId int) (*entity.AuctionTag, error) {
	var result entity.AuctionTag

	err := r.DB.
		Table("auction_tag AS at").
		Select(`
	        at.id,
	        at.auction_id,
	        at.branch_ids,
	        at.is_active,
	        a.auction_name,
	        at.start_tag_number,
	        at.end_tag_number,
			at.created_by,
			at.created_date,
			at.updated_by,
			at.updated_date,
			created_user.first_name || ' ' || created_user.last_name AS created_user,
			updated_user.first_name || ' ' || updated_user.last_name AS updated_user
	    `).
		Joins("LEFT JOIN auction a ON a.id = at.auction_id").
		Joins("LEFT JOIN employee created_user ON created_user.id = at.created_by").
		Joins("LEFT JOIN employee updated_user ON updated_user.id = at.updated_by").
		Where("at.id = ?", actionTagId).
		Where("at.deleted_date IS NULL").
		Scan(&result).Error

	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *auctionTagRepositoryImpl) FindAssetTypesByAuctionTagIds(auctionTagIds []int) (map[int][]dto.AuctionAssetType, error) {
	var result []struct {
		AuctionTagId    int
		AuctionId       int
		AssetTypeId     int
		AssetTypeCode   string
		AssetTypeDescTh string
		AssetTypeDescEn string
	}

	query := `
		SELECT
			at.id AS auction_tag_id,
			at.auction_id,
			aa.asset_type_id,
			mat.asset_type_code,
			mat.description_th AS asset_type_desc_th,
			mat.description_en AS asset_type_desc_en
		FROM auction_tag at
		JOIN auction_asset aa ON aa.auction_id = at.auction_id
		JOIN master_asset_type mat ON mat.id = aa.asset_type_id
		WHERE at.id IN ?
	`

	if err := r.DB.Raw(query, auctionTagIds).Scan(&result).Error; err != nil {
		return nil, err
	}

	// Mapping
	assetTypeMap := make(map[int][]dto.AuctionAssetType)
	for _, row := range result {
		info := dto.AuctionAssetType{
			AssetTypeId:   row.AssetTypeId,
			AssetTypeCode: &row.AssetTypeCode,
			DescriptionTh: &row.AssetTypeDescTh,
			DescriptionEn: &row.AssetTypeDescEn,
		}
		assetTypeMap[row.AuctionTagId] = append(assetTypeMap[row.AuctionTagId], info)
	}

	return assetTypeMap, nil
}

func (r *auctionTagRepositoryImpl) CountAuctionTagsWithFilter(req model.PagingRequest) (int64, error) {

	var count int64

	query := `select count(*) from public.auction_tag where deleted_date IS NULL`
	if err := r.DB.Raw(query).Scan(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (r *auctionTagRepositoryImpl) UpdatesAuctionTagFieldsWhere(tx *gorm.DB, fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	result := tx.Model(&entity.AuctionTag{}).Where(whereClause, args...).Updates(fields)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *auctionTagRepositoryImpl) Delete(tx *gorm.DB, id int) error {
	var existing entity.AuctionTag
	err := tx.First(&existing, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("auction tag with id %d not found", id)
	}
	if existing.DeletedDate != nil {
		return fmt.Errorf("auction tag with id %d is already deleted", id)
	}

	err = tx.Model(&entity.AuctionTag{}).Where("id = ?", id).Delete(&entity.AuctionTag{}).Error

	return err
}
