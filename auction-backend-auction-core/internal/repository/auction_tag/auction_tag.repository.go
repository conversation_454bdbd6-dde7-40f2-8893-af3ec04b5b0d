package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type auctionTagRepositoryImpl struct {
	DB *gorm.DB
}

type AuctionTagRepository interface {
	FindById(auctionTagId int) (*entity.AuctionTag, error)
	FindByIds(auctionTagId int) ([]entity.AuctionTag, error)
	FindByIdsExcept(excludeId int) ([]entity.AuctionTag, error)
	FindAuctionTags() ([]entity.AuctionTag, error)
	FindAssetTypesByAuctionTagIds(auctionTagIds []int) (map[int][]dto.AuctionAssetType, error)
	FindAuctionTagsPageSimple(req model.PagingRequest) ([]entity.AuctionTag, error)
	FindAuctionTagsPageSimpleById(actionTagId int) (*entity.AuctionTag, error)
	UpdatesAuctionTagFieldsWhere(tx *gorm.DB, fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	CountAuctionTagsWithFilter(req model.PagingRequest) (int64, error)
	InsertAuctionTag(tx *gorm.DB, entityAuctionTag *entity.AuctionTag) error
	Delete(tx *gorm.DB, id int) error
	GetDB() *gorm.DB
}

func NewAuctionTagRepository(db *gorm.DB) AuctionTagRepository {
	return &auctionTagRepositoryImpl{DB: db}
}
