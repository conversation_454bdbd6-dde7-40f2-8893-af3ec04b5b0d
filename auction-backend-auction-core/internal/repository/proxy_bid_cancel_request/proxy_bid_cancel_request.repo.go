package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type proxyBidCancelRequestRepositoryImpl struct {
	DB *gorm.DB
}

type ProxyBidCancelRequestRepository interface {
	FindProxyBidCancelRequestWithFilter(req dto.ProxyBidCancelRequestPageReqDto) ([]dto.ProxyBidCancelRequestDto, error)
	CountProxyBidCancelRequestWithFilter(req dto.ProxyBidCancelRequestPageReqDto) (int64, error)
	FindById(id int) (*entity.ProxyBidCancelRequest, error)

	UpdateProxyBidCancelRequest(tx *gorm.DB, id int, fieldToUpdate map[string]interface{}) (int64, error)
	GetDB() *gorm.DB
}

func NewProxyBidCancelRequestRepository(db *gorm.DB) ProxyBidCancelRequestRepository {
	return &proxyBidCancelRequestRepositoryImpl{DB: db}
}
