package repository

import (
	"auction-core-service/internal/model/dto"
	"auction-core-service/internal/model/entity"
	"backend-common-lib/constant"
	"backend-common-lib/util"

	"gorm.io/gorm"
)

func (r *proxyBidCancelRequestRepositoryImpl) buildProxyBidCancelRequestQuery(req dto.ProxyBidCancelRequestPageReqDto) *gorm.DB {
	query := r.DB.Table("proxy_bid_cancel_request").
		Select(`
			proxy_bid_cancel_request.*,
			lsl.floor,
			lsl.floor || '-' || COALESCE(lsl.auction_no::TEXT, '') AS no,
			mb.description_th AS branch_description_th,
			mb.description_en AS branch_description_en,
			lsl.auction_date AS auction_date,
			b.first_name || ' ' || COALESCE(b.middle_name, '') || ' ' || b.last_name AS customer_name,
			b.phone_number AS phone_number,
			COALESCE(lsl.brand_description, '') || ' ' || COALESCE(lsl.model_description, '') || ' ' || COALESCE(lsl.registration_year, '') AS product,
			lsl.brand_description AS brand,
			lsl.model_description AS model,
			pb.created_date AS proxy_date,
			cfg.value_string AS proxy_status,
			approve_user.first_name || ' ' || approve_user.last_name AS approve_by,
			created_user.first_name || ' ' || created_user.last_name AS created_by,
			updated_user.first_name || ' ' || updated_user.last_name AS updated_by,
			pbcr.reason AS cancel_reason
		`)

	query = query.
		Joins("LEFT JOIN proxy_bid pb ON pb.id = proxy_bid_cancel_request.proxy_bid_id").
		Joins("LEFT JOIN buyer b ON b.id = proxy_bid_cancel_request.buyer_id").
		Joins("LEFT JOIN lot_sou_lot_line lsl ON pb.lot_sou_lot_line_id = lsl.id").
		Joins("LEFT JOIN lot l ON l.id = pb.lot_id").
		Joins(`LEFT JOIN config_parameters cfg ON 
				cfg.parameter_module = ? AND 
				cfg.parameter_name = ? AND 
				cfg.value_int = pb.proxy_status_id`,
			constant.ConfigParamConst.MODULE_SYSTEM_ADMIN,
			constant.ConfigParamConst.DROPDOWN_PROXY_BID_STATUS,
		).
		Joins("LEFT JOIN master_branch mb ON mb.branch_code = lsl.branch_code").
		Joins("LEFT JOIN employee approve_user ON approve_user.id = proxy_bid_cancel_request.approve_by").
		Joins("LEFT JOIN employee created_user ON created_user.id = proxy_bid_cancel_request.created_by").
		Joins("LEFT JOIN employee updated_user ON updated_user.id = proxy_bid_cancel_request.updated_by").
		Joins("LEFT JOIN proxy_bid_cancel_reason pbcr ON pbcr.id = pb.cancel_reason_id")

	fieldMap := map[string]string{
		"AuctionDate":   "lsl.auction_date",
		"BranchCode":    "lsl.branch_code",
		"ProxyStatusId": "pb.proxy_status_id",
		"Floor":         "lsl.floor",
	}

	query = util.ApplyFiltersFromStruct(query, req, fieldMap)

	return query
}

func (r *proxyBidCancelRequestRepositoryImpl) FindProxyBidCancelRequestWithFilter(req dto.ProxyBidCancelRequestPageReqDto) ([]dto.ProxyBidCancelRequestDto, error) {
	var results []dto.ProxyBidCancelRequestDto

	query := r.buildProxyBidCancelRequestQuery(req)
	sortingProxyBidCancelRequest := map[string][]string{
		"floor":         {"lsl.floor"},
		"no":            {"lsl.floor", "lsl.auction_no"},
		"branch_code":   {"lsl.branch_code"},
		"auction_date":  {"lsl.auction_date"},
		"customer_name": {"b.first_name", "b.middle_name", "b.last_name"},
		"phone_number":  {"b.phone_number"},
		"product":       {"lsl.brand_description", "lsl.model_description", "lsl.registration_year"},
		"brand":         {"lsl.brand_description"},
		"model":         {"lsl.model_description"},
		"proxy_date":    {"pb.created_date"},
		"proxy_status":  {"cfg.value_string"},
		"approve_by":    {"approve_user.first_name", "approve_user.last_name"},
		"created_by":    {"created_user.first_name", "created_user.last_name"},
		"updated_by":    {"updated_user.first_name", "updated_user.last_name"},
		"cancel_reason": {"pbcr.reason"},
	}

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplySortFromMapField(query, snakeSort, req.SortOrder, "proxy_bid_cancel_request", sortingProxyBidCancelRequest, false)
	}
	//NOTE - default order
	query.Order("lsl.auction_date asc, lsl.branch_description asc, lsl.floor asc, lsl.auction_no asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *proxyBidCancelRequestRepositoryImpl) CountProxyBidCancelRequestWithFilter(req dto.ProxyBidCancelRequestPageReqDto) (int64, error) {
	var count int64
	query := r.buildProxyBidCancelRequestQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *proxyBidCancelRequestRepositoryImpl) UpdateProxyBidCancelRequest(tx *gorm.DB, id int, fieldToUpdate map[string]interface{}) (int64, error) {
	result := tx.Model(&entity.ProxyBidCancelRequest{}).
		Where("id = ?", id).
		Updates(fieldToUpdate)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *proxyBidCancelRequestRepositoryImpl) FindById(id int) (*entity.ProxyBidCancelRequest, error) {
	var result entity.ProxyBidCancelRequest
	if err := r.DB.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *proxyBidCancelRequestRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
