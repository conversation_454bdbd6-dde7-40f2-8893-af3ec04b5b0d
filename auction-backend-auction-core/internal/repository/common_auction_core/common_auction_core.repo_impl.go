package repository

import (
	"auction-core-service/internal/model/dto"
	"backend-common-lib/model"
)

func (r *commonAuctionCoreRepositoryImpl) GetAllCustomerGroups() ([]dto.CustomerGroups, error) {
	var results []dto.CustomerGroups
	query := r.DB.Table("master_customer_group").
		Select(`
			id,
			description_th
		`)

	if err := query.Where("1 = ?", 1).Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *commonAuctionCoreRepositoryImpl) GetAllAssetType() ([]model.AssetTypeForJoin, error) {
	var results []model.AssetTypeForJoin
	query := r.DB.Table("master_asset_type").
		Select(`
			id,
			asset_type_code,
			description_th,
			description_en
		`)

	if err := query.Where("1 = ?", 1).Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *commonAuctionCoreRepositoryImpl) GetAllAssetGroup() ([]model.AssetGroupForJoin, error) {
	var results []model.AssetGroupForJoin
	query := r.DB.Table("master_asset_group").
		Select(`
			id,
			asset_group_code,
			asset_type_code,
			description_th,
			description_en
		`)

	if err := query.Where("1 = ?", 1).Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}
