package repository

import (
	"auction-core-service/internal/model/dto"
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type commonAuctionCoreRepositoryImpl struct {
	DB *gorm.DB
}

type CommonAuctionCoreRepository interface {
	GetAllCustomerGroups() ([]dto.CustomerGroups, error)
	GetAllAssetType() ([]model.AssetTypeForJoin, error)
}

func NewCommonAuctionCoreRepository(db *gorm.DB) CommonAuctionCoreRepository {
	return &commonAuctionCoreRepositoryImpl{DB: db}
}
