package repository

import (
	"auction-core-service/internal/model/entity"
)

func (r *campaignDisplayLocationRepositoryImpl) GetAllByCampaignId(campaignId int) ([]entity.CampaignDisplayLocation, error) {
	var results []entity.CampaignDisplayLocation

	query := r.DB.Model(&entity.CampaignDisplayLocation{})

	if err := query.Where("campaign_id = ?", campaignId).Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *campaignDisplayLocationRepositoryImpl) GetAllByCampaignIds(campaignIds []int) ([]entity.CampaignDisplayLocation, error) {
	var results []entity.CampaignDisplayLocation

	query := r.DB.Table("campaign_display_location").
		Joins("JOIN (SELECT value_string2, value_int, parameter_name FROM config_parameters) AS cp ON cp.value_int = campaign_display_location.display_location_id AND cp.parameter_name = ?", "DISPLAY_LOCATION").
		Select("campaign_display_location.*, cp.value_string2")

	if err := query.Where("campaign_id IN ?", campaignIds).Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *campaignDisplayLocationRepositoryImpl) Insert(campaignDisplayLocation entity.CampaignDisplayLocation) error {
	err := r.DB.Create(&campaignDisplayLocation).Error
	return err
}

func (r *campaignDisplayLocationRepositoryImpl) InsertList(campaignDisplayLocations []entity.CampaignDisplayLocation) error {
	err := r.DB.Create(&campaignDisplayLocations).Error
	return err
}

func (r *campaignDisplayLocationRepositoryImpl) DeleteByCampaignId(campaignId int) error {
	err := r.DB.Where("campaign_id = ?", campaignId).Delete(&entity.CampaignDisplayLocation{}).Error
	return err
}
