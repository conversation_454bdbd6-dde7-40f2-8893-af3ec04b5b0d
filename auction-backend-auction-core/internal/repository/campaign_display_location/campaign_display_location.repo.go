package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type campaignDisplayLocationRepositoryImpl struct {
	DB *gorm.DB
}

type CampaignDisplayLocationRepository interface {
	GetAllByCampaignId(campaignId int) ([]entity.CampaignDisplayLocation, error)
	GetAllByCampaignIds(campaignIds []int) ([]entity.CampaignDisplayLocation, error)
	Insert(campaignDisplayLocation entity.CampaignDisplayLocation) error
	InsertList(campaignDisplayLocations []entity.CampaignDisplayLocation) error
	DeleteByCampaignId(campaignId int) error
}

func NewCampaignDisplayLocationRepository(db *gorm.DB) CampaignDisplayLocationRepository {
	return &campaignDisplayLocationRepositoryImpl{DB: db}
}
