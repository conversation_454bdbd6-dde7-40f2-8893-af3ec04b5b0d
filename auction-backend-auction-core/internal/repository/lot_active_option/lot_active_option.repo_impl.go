package repository

import (
	"auction-core-service/internal/model/entity"
)

func (r *lotActiveOptionRepositoryImpl) GetById(id int) (entity.LotActiveOption, error) {
	var lotActiveOption entity.LotActiveOption
	err := r.DB.First(&lotActiveOption, id).Error
	return lotActiveOption, err
}

func (r *lotActiveOptionRepositoryImpl) GetAllByLotId(lotId int) ([]entity.LotActiveOption, error) {
	var lotActiveOptions []entity.LotActiveOption
	err := r.DB.Where("lot_id = ?", lotId).Find(&lotActiveOptions).Error
	return lotActiveOptions, err
}

func (r *lotActiveOptionRepositoryImpl) Insert(lotActiveOption entity.LotActiveOption) error {
	err := r.DB.Create(&lotActiveOption).Error
	return err
}

func (r *lotActiveOptionRepositoryImpl) DeleteByLotId(lotId int) error {
	err := r.DB.Where("lot_id = ?", lotId).Delete(&entity.LotActiveOption{}).Error
	return err
}
