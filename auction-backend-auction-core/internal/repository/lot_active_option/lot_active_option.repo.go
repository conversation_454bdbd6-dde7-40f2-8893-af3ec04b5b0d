package repository

import (
	"auction-core-service/internal/model/entity"

	"gorm.io/gorm"
)

type lotActiveOptionRepositoryImpl struct {
	DB *gorm.DB
}

type LotActiveOptionRepository interface {
	GetById(id int) (entity.LotActiveOption, error)
	GetAllByLotId(lotId int) ([]entity.LotActiveOption, error)
	Insert(lotActiveOption entity.LotActiveOption) error
	DeleteByLotId(lotId int) error
}

func NewLotActiveOptionRepository(db *gorm.DB) LotActiveOptionRepository {
	return &lotActiveOptionRepositoryImpl{DB: db}
}
