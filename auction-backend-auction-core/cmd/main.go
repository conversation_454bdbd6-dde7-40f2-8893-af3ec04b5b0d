package main

import (
	"auction-core-service/internal/bootstrap"
	"auction-core-service/pkg/config"
	"auction-core-service/pkg/db"
	"fmt"
	"log"
	"os"
)

func main() {
	env := os.Getenv("APP_ENV") // "local", "dev", "sit"
	log.Println("env :", env)
	if env == "" {
		env = "local"
	}
	config.LoadConfig(env)

	database := db.ConnectDB()
	// db.Migrate(database)
	// db.Seed(database)

	port := config.Cfg.App.HttpPort
	appCtx := bootstrap.SetupApp(database, &config.Cfg)

	log.Printf("🚀 Starting %s on :%d", config.Cfg.App.AppName, port)
	if env == "siri" {
		err := appCtx.App.ListenTLS(fmt.Sprintf(":%d", port), config.Cfg.App.CertFile, config.Cfg.App.KeyFile)
		if err != nil {
			log.Fatal(err)
			return
		}
	} else {
		err := appCtx.App.Listen(fmt.Sprintf(":%d", port))
		if err != nil {
			log.Fatal(err)
			return
		}
	}
}
