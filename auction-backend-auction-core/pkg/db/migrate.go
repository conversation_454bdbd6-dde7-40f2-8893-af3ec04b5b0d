package db

import (
	"auction-core-service/internal/model/entity"
	"log"

	"gorm.io/gorm"
)

func Migrate(database *gorm.DB) {
	if err := database.AutoMigrate(
		&entity.Auction{},
		&entity.AuctionAsset{},
		&entity.AuctionAmountLimit{},
		&entity.AuctionBidStep{},
		&entity.AuctionFee{},
		&entity.AuctionMinimumPayment{},
		// add other models here later
	); err != nil {
		log.Fatalf("❌ Failed to migrate database: %v", err)
	}

	log.Println("✅ Database migrated successfully")
}
