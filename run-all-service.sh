#!/bin/bash

echo "Running all services concurrently based on the provided image..."

# Run auction_core_service in the background
echo "Running auction_core_service..."
go run auction-backend-auction-core/cmd/main.go &

# # Run auctioneer_service in the background
# echo "Running auctioneer_service..."
# go run auctioneer_service/cmd/main.go &

# Run content_service in the background
echo "Running content_service..."
go run auction-backend-auction-core/cmd/main.go &
go run auction-backend-content/cmd/main.go &

# Run service_repo_example in the background
# echo "Running service_repo_example..."
# go run service_repo_example/cmd/main.go &

# # Run system_admin_service in the background
# echo "Running system_admin_service..."
# go run system_admin_service/cmd/main.go &

# # Run user_service in the background
# echo "Running user_service..."
# go run user_service/cmd/main.go &

# Wait for all background processes to complete
wait

echo "All selected services have finished running."